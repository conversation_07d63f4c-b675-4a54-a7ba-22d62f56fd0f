import{toNestError as e,validateFieldsNatively as t}from"@hookform/resolvers";import{appendErrors as r}from"react-hook-form";function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function a(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function o(e){var t={exports:{}};return e(t,t.exports),t.exports}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;var n=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.regexpCode=t.getEsmExportName=t.getProperty=t.safeStringify=t.stringify=t.strConcat=t.addCodeArg=t.str=t._=t.nil=t._Code=t.Name=t.IDENTIFIER=t._CodeOrName=void 0;class r{}t._CodeOrName=r,t.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;class s extends r{constructor(e){if(super(),!t.IDENTIFIER.test(e))throw new Error("CodeGen: name must be a valid identifier");this.str=e}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}}t.Name=s;class a extends r{constructor(e){super(),this._items="string"==typeof e?[e]:e}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;const e=this._items[0];return""===e||'""'===e}get str(){var e;return null!==(e=this._str)&&void 0!==e?e:this._str=this._items.reduce((e,t)=>`${e}${t}`,"")}get names(){var e;return null!==(e=this._names)&&void 0!==e?e:this._names=this._items.reduce((e,t)=>(t instanceof s&&(e[t.str]=(e[t.str]||0)+1),e),{})}}function o(e,...t){const r=[e[0]];let s=0;for(;s<t.length;)c(r,t[s]),r.push(e[++s]);return new a(r)}t._Code=a,t.nil=new a(""),t._=o;const n=new a("+");function i(e,...t){const r=[d(e[0])];let s=0;for(;s<t.length;)r.push(n),c(r,t[s]),r.push(n,d(e[++s]));return function(e){let t=1;for(;t<e.length-1;){if(e[t]===n){const r=l(e[t-1],e[t+1]);if(void 0!==r){e.splice(t-1,3,r);continue}e[t++]="+"}t++}}(r),new a(r)}function c(e,t){var r;t instanceof a?e.push(...t._items):e.push(t instanceof s?t:"number"==typeof(r=t)||"boolean"==typeof r||null===r?r:d(Array.isArray(r)?r.join(","):r))}function l(e,t){if('""'===t)return e;if('""'===e)return t;if("string"==typeof e){if(t instanceof s||'"'!==e[e.length-1])return;return"string"!=typeof t?`${e.slice(0,-1)}${t}"`:'"'===t[0]?e.slice(0,-1)+t.slice(1):void 0}return"string"!=typeof t||'"'!==t[0]||e instanceof s?void 0:`"${e}${t.slice(1)}`}function d(e){return JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}t.str=i,t.addCodeArg=c,t.strConcat=function(e,t){return t.emptyStr()?e:e.emptyStr()?t:i`${e}${t}`},t.stringify=function(e){return new a(d(e))},t.safeStringify=d,t.getProperty=function(e){return"string"==typeof e&&t.IDENTIFIER.test(e)?new a(`.${e}`):o`[${e}]`},t.getEsmExportName=function(e){if("string"==typeof e&&t.IDENTIFIER.test(e))return new a(`${e}`);throw new Error(`CodeGen: invalid export name: ${e}, use explicit $id name mapping`)},t.regexpCode=function(e){return new a(e.toString())}}),i=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.ValueScope=t.ValueScopeName=t.Scope=t.varKinds=t.UsedValueState=void 0;class r extends Error{constructor(e){super(`CodeGen: "code" for ${e} not defined`),this.value=e.value}}var s;!function(e){e[e.Started=0]="Started",e[e.Completed=1]="Completed"}(s=t.UsedValueState||(t.UsedValueState={})),t.varKinds={const:new n.Name("const"),let:new n.Name("let"),var:new n.Name("var")};class a{constructor({prefixes:e,parent:t}={}){this._names={},this._prefixes=e,this._parent=t}toName(e){return e instanceof n.Name?e:this.name(e)}name(e){return new n.Name(this._newName(e))}_newName(e){return`${e}${(this._names[e]||this._nameGroup(e)).index++}`}_nameGroup(e){var t,r;if((null===(r=null===(t=this._parent)||void 0===t?void 0:t._prefixes)||void 0===r?void 0:r.has(e))||this._prefixes&&!this._prefixes.has(e))throw new Error(`CodeGen: prefix "${e}" is not allowed in this scope`);return this._names[e]={prefix:e,index:0}}}t.Scope=a;class o extends n.Name{constructor(e,t){super(t),this.prefix=e}setValue(e,{property:t,itemIndex:r}){this.value=e,this.scopePath=n._`.${new n.Name(t)}[${r}]`}}t.ValueScopeName=o;const i=n._`\n`;t.ValueScope=class extends a{constructor(e){super(e),this._values={},this._scope=e.scope,this.opts={...e,_n:e.lines?i:n.nil}}get(){return this._scope}name(e){return new o(e,this._newName(e))}value(e,t){var r;if(void 0===t.ref)throw new Error("CodeGen: ref must be passed in value");const s=this.toName(e),{prefix:a}=s,o=null!==(r=t.key)&&void 0!==r?r:t.ref;let n=this._values[a];if(n){const e=n.get(o);if(e)return e}else n=this._values[a]=new Map;n.set(o,s);const i=this._scope[a]||(this._scope[a]=[]),c=i.length;return i[c]=t.ref,s.setValue(t,{property:a,itemIndex:c}),s}getValue(e,t){const r=this._values[e];if(r)return r.get(t)}scopeRefs(e,t=this._values){return this._reduceValues(t,t=>{if(void 0===t.scopePath)throw new Error(`CodeGen: name "${t}" has no value`);return n._`${e}${t.scopePath}`})}scopeCode(e=this._values,t,r){return this._reduceValues(e,e=>{if(void 0===e.value)throw new Error(`CodeGen: name "${e}" has no value`);return e.value.code},t,r)}_reduceValues(e,a,o={},i){let c=n.nil;for(const l in e){const d=e[l];if(!d)continue;const u=o[l]=o[l]||new Map;d.forEach(e=>{if(u.has(e))return;u.set(e,s.Started);let o=a(e);if(o)c=n._`${c}${this.opts.es5?t.varKinds.var:t.varKinds.const} ${e} = ${o};${this.opts._n}`;else{if(!(o=null==i?void 0:i(e)))throw new r(e);c=n._`${c}${o}${this.opts._n}`}u.set(e,s.Completed)})}return c}}}),c=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.or=t.and=t.not=t.CodeGen=t.operators=t.varKinds=t.ValueScopeName=t.ValueScope=t.Scope=t.Name=t.regexpCode=t.stringify=t.getProperty=t.nil=t.strConcat=t.str=t._=void 0;var r=n;Object.defineProperty(t,"_",{enumerable:!0,get:function(){return r._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return r.str}}),Object.defineProperty(t,"strConcat",{enumerable:!0,get:function(){return r.strConcat}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return r.nil}}),Object.defineProperty(t,"getProperty",{enumerable:!0,get:function(){return r.getProperty}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return r.stringify}}),Object.defineProperty(t,"regexpCode",{enumerable:!0,get:function(){return r.regexpCode}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return r.Name}});var s=i;Object.defineProperty(t,"Scope",{enumerable:!0,get:function(){return s.Scope}}),Object.defineProperty(t,"ValueScope",{enumerable:!0,get:function(){return s.ValueScope}}),Object.defineProperty(t,"ValueScopeName",{enumerable:!0,get:function(){return s.ValueScopeName}}),Object.defineProperty(t,"varKinds",{enumerable:!0,get:function(){return s.varKinds}}),t.operators={GT:new n._Code(">"),GTE:new n._Code(">="),LT:new n._Code("<"),LTE:new n._Code("<="),EQ:new n._Code("==="),NEQ:new n._Code("!=="),NOT:new n._Code("!"),OR:new n._Code("||"),AND:new n._Code("&&"),ADD:new n._Code("+")};class a{optimizeNodes(){return this}optimizeNames(e,t){return this}}class o extends a{constructor(e,t,r){super(),this.varKind=e,this.name=t,this.rhs=r}render({es5:e,_n:t}){return`${e?i.varKinds.var:this.varKind} ${this.name}${void 0===this.rhs?"":` = ${this.rhs}`};`+t}optimizeNames(e,t){if(e[this.name.str])return this.rhs&&(this.rhs=O(this.rhs,e,t)),this}get names(){return this.rhs instanceof n._CodeOrName?this.rhs.names:{}}}class c extends a{constructor(e,t,r){super(),this.lhs=e,this.rhs=t,this.sideEffects=r}render({_n:e}){return`${this.lhs} = ${this.rhs};`+e}optimizeNames(e,t){if(!(this.lhs instanceof n.Name)||e[this.lhs.str]||this.sideEffects)return this.rhs=O(this.rhs,e,t),this}get names(){return C(this.lhs instanceof n.Name?{}:{...this.lhs.names},this.rhs)}}class l extends c{constructor(e,t,r,s){super(e,r,s),this.op=t}render({_n:e}){return`${this.lhs} ${this.op}= ${this.rhs};`+e}}class d extends a{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`${this.label}:`+e}}class u extends a{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`break${this.label?` ${this.label}`:""};`+e}}class f extends a{constructor(e){super(),this.error=e}render({_n:e}){return`throw ${this.error};`+e}get names(){return this.error.names}}class h extends a{constructor(e){super(),this.code=e}render({_n:e}){return`${this.code};`+e}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(e,t){return this.code=O(this.code,e,t),this}get names(){return this.code instanceof n._CodeOrName?this.code.names:{}}}class p extends a{constructor(e=[]){super(),this.nodes=e}render(e){return this.nodes.reduce((t,r)=>t+r.render(e),"")}optimizeNodes(){const{nodes:e}=this;let t=e.length;for(;t--;){const r=e[t].optimizeNodes();Array.isArray(r)?e.splice(t,1,...r):r?e[t]=r:e.splice(t,1)}return e.length>0?this:void 0}optimizeNames(e,t){const{nodes:r}=this;let s=r.length;for(;s--;){const a=r[s];a.optimizeNames(e,t)||(x(e,a.names),r.splice(s,1))}return r.length>0?this:void 0}get names(){return this.nodes.reduce((e,t)=>j(e,t.names),{})}}class m extends p{render(e){return"{"+e._n+super.render(e)+"}"+e._n}}class y extends p{}class v extends m{}v.kind="else";class g extends m{constructor(e,t){super(t),this.condition=e}render(e){let t=`if(${this.condition})`+super.render(e);return this.else&&(t+="else "+this.else.render(e)),t}optimizeNodes(){super.optimizeNodes();const e=this.condition;if(!0===e)return this.nodes;let t=this.else;if(t){const e=t.optimizeNodes();t=this.else=Array.isArray(e)?new v(e):e}return t?!1===e?t instanceof g?t:t.nodes:this.nodes.length?this:new g(T(e),t instanceof g?[t]:t.nodes):!1!==e&&this.nodes.length?this:void 0}optimizeNames(e,t){var r;if(this.else=null===(r=this.else)||void 0===r?void 0:r.optimizeNames(e,t),super.optimizeNames(e,t)||this.else)return this.condition=O(this.condition,e,t),this}get names(){const e=super.names;return C(e,this.condition),this.else&&j(e,this.else.names),e}}g.kind="if";class $ extends m{}$.kind="for";class _ extends ${constructor(e){super(),this.iteration=e}render(e){return`for(${this.iteration})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iteration=O(this.iteration,e,t),this}get names(){return j(super.names,this.iteration.names)}}class w extends ${constructor(e,t,r,s){super(),this.varKind=e,this.name=t,this.from=r,this.to=s}render(e){const t=e.es5?i.varKinds.var:this.varKind,{name:r,from:s,to:a}=this;return`for(${t} ${r}=${s}; ${r}<${a}; ${r}++)`+super.render(e)}get names(){const e=C(super.names,this.from);return C(e,this.to)}}class b extends ${constructor(e,t,r,s){super(),this.loop=e,this.varKind=t,this.name=r,this.iterable=s}render(e){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iterable=O(this.iterable,e,t),this}get names(){return j(super.names,this.iterable.names)}}class E extends m{constructor(e,t,r){super(),this.name=e,this.args=t,this.async=r}render(e){return`${this.async?"async ":""}function ${this.name}(${this.args})`+super.render(e)}}E.kind="func";class P extends p{render(e){return"return "+super.render(e)}}P.kind="return";class S extends m{render(e){let t="try"+super.render(e);return this.catch&&(t+=this.catch.render(e)),this.finally&&(t+=this.finally.render(e)),t}optimizeNodes(){var e,t;return super.optimizeNodes(),null===(e=this.catch)||void 0===e||e.optimizeNodes(),null===(t=this.finally)||void 0===t||t.optimizeNodes(),this}optimizeNames(e,t){var r,s;return super.optimizeNames(e,t),null===(r=this.catch)||void 0===r||r.optimizeNames(e,t),null===(s=this.finally)||void 0===s||s.optimizeNames(e,t),this}get names(){const e=super.names;return this.catch&&j(e,this.catch.names),this.finally&&j(e,this.finally.names),e}}class k extends m{constructor(e){super(),this.error=e}render(e){return`catch(${this.error})`+super.render(e)}}k.kind="catch";class N extends m{render(e){return"finally"+super.render(e)}}function j(e,t){for(const r in t)e[r]=(e[r]||0)+(t[r]||0);return e}function C(e,t){return t instanceof n._CodeOrName?j(e,t.names):e}function O(e,t,r){return e instanceof n.Name?a(e):(s=e)instanceof n._Code&&s._items.some(e=>e instanceof n.Name&&1===t[e.str]&&void 0!==r[e.str])?new n._Code(e._items.reduce((e,t)=>(t instanceof n.Name&&(t=a(t)),t instanceof n._Code?e.push(...t._items):e.push(t),e),[])):e;var s;function a(e){const s=r[e.str];return void 0===s||1!==t[e.str]?e:(delete t[e.str],s)}}function x(e,t){for(const r in t)e[r]=(e[r]||0)-(t[r]||0)}function T(e){return"boolean"==typeof e||"number"==typeof e||null===e?!e:n._`!${A(e)}`}N.kind="finally",t.CodeGen=class{constructor(e,t={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...t,_n:t.lines?"\n":""},this._extScope=e,this._scope=new i.Scope({parent:e}),this._nodes=[new y]}toString(){return this._root.render(this.opts)}name(e){return this._scope.name(e)}scopeName(e){return this._extScope.name(e)}scopeValue(e,t){const r=this._extScope.value(e,t);return(this._values[r.prefix]||(this._values[r.prefix]=new Set)).add(r),r}getScopeValue(e,t){return this._extScope.getValue(e,t)}scopeRefs(e){return this._extScope.scopeRefs(e,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(e,t,r,s){const a=this._scope.toName(t);return void 0!==r&&s&&(this._constants[a.str]=r),this._leafNode(new o(e,a,r)),a}const(e,t,r){return this._def(i.varKinds.const,e,t,r)}let(e,t,r){return this._def(i.varKinds.let,e,t,r)}var(e,t,r){return this._def(i.varKinds.var,e,t,r)}assign(e,t,r){return this._leafNode(new c(e,t,r))}add(e,r){return this._leafNode(new l(e,t.operators.ADD,r))}code(e){return"function"==typeof e?e():e!==n.nil&&this._leafNode(new h(e)),this}object(...e){const t=["{"];for(const[r,s]of e)t.length>1&&t.push(","),t.push(r),(r!==s||this.opts.es5)&&(t.push(":"),(0,n.addCodeArg)(t,s));return t.push("}"),new n._Code(t)}if(e,t,r){if(this._blockNode(new g(e)),t&&r)this.code(t).else().code(r).endIf();else if(t)this.code(t).endIf();else if(r)throw new Error('CodeGen: "else" body without "then" body');return this}elseIf(e){return this._elseNode(new g(e))}else(){return this._elseNode(new v)}endIf(){return this._endBlockNode(g,v)}_for(e,t){return this._blockNode(e),t&&this.code(t).endFor(),this}for(e,t){return this._for(new _(e),t)}forRange(e,t,r,s,a=(this.opts.es5?i.varKinds.var:i.varKinds.let)){const o=this._scope.toName(e);return this._for(new w(a,o,t,r),()=>s(o))}forOf(e,t,r,s=i.varKinds.const){const a=this._scope.toName(e);if(this.opts.es5){const e=t instanceof n.Name?t:this.var("_arr",t);return this.forRange("_i",0,n._`${e}.length`,t=>{this.var(a,n._`${e}[${t}]`),r(a)})}return this._for(new b("of",s,a,t),()=>r(a))}forIn(e,t,r,s=(this.opts.es5?i.varKinds.var:i.varKinds.const)){if(this.opts.ownProperties)return this.forOf(e,n._`Object.keys(${t})`,r);const a=this._scope.toName(e);return this._for(new b("in",s,a,t),()=>r(a))}endFor(){return this._endBlockNode($)}label(e){return this._leafNode(new d(e))}break(e){return this._leafNode(new u(e))}return(e){const t=new P;if(this._blockNode(t),this.code(e),1!==t.nodes.length)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(P)}try(e,t,r){if(!t&&!r)throw new Error('CodeGen: "try" without "catch" and "finally"');const s=new S;if(this._blockNode(s),this.code(e),t){const e=this.name("e");this._currNode=s.catch=new k(e),t(e)}return r&&(this._currNode=s.finally=new N,this.code(r)),this._endBlockNode(k,N)}throw(e){return this._leafNode(new f(e))}block(e,t){return this._blockStarts.push(this._nodes.length),e&&this.code(e).endBlock(t),this}endBlock(e){const t=this._blockStarts.pop();if(void 0===t)throw new Error("CodeGen: not in self-balancing block");const r=this._nodes.length-t;if(r<0||void 0!==e&&r!==e)throw new Error(`CodeGen: wrong number of nodes: ${r} vs ${e} expected`);return this._nodes.length=t,this}func(e,t=n.nil,r,s){return this._blockNode(new E(e,t,r)),s&&this.code(s).endFunc(),this}endFunc(){return this._endBlockNode(E)}optimize(e=1){for(;e-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(e){return this._currNode.nodes.push(e),this}_blockNode(e){this._currNode.nodes.push(e),this._nodes.push(e)}_endBlockNode(e,t){const r=this._currNode;if(r instanceof e||t&&r instanceof t)return this._nodes.pop(),this;throw new Error(`CodeGen: not in block "${t?`${e.kind}/${t.kind}`:e.kind}"`)}_elseNode(e){const t=this._currNode;if(!(t instanceof g))throw new Error('CodeGen: "else" without "if"');return this._currNode=t.else=e,this}get _root(){return this._nodes[0]}get _currNode(){const e=this._nodes;return e[e.length-1]}set _currNode(e){const t=this._nodes;t[t.length-1]=e}},t.not=T;const R=D(t.operators.AND);t.and=function(...e){return e.reduce(R)};const I=D(t.operators.OR);function D(e){return(t,r)=>t===n.nil?r:r===n.nil?t:n._`${A(t)} ${e} ${A(r)}`}function A(e){return e instanceof n.Name?e:n._`(${e})`}t.or=function(...e){return e.reduce(I)}}),l=o(function(e,t){function r(e,t=e.schema){const{opts:r,self:s}=e;if(!r.strictSchema)return;if("boolean"==typeof t)return;const a=s.RULES.keywords;for(const r in t)a[r]||h(e,`unknown keyword: "${r}"`)}function s(e,t){if("boolean"==typeof e)return!e;for(const r in e)if(t[r])return!0;return!1}function a(e){return"number"==typeof e?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}function o(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function i({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:s}){return(a,o,n,i)=>{const l=void 0===n?o:n instanceof c.Name?(o instanceof c.Name?e(a,o,n):t(a,o,n),n):o instanceof c.Name?(t(a,n,o),o):r(o,n);return i!==c.Name||l instanceof c.Name?l:s(a,l)}}function l(e,t){if(!0===t)return e.var("props",!0);const r=e.var("props",c._`{}`);return void 0!==t&&d(e,r,t),r}function d(e,t,r){Object.keys(r).forEach(r=>e.assign(c._`${t}${(0,c.getProperty)(r)}`,!0))}Object.defineProperty(t,"__esModule",{value:!0}),t.checkStrictMode=t.getErrorPath=t.Type=t.useFunc=t.setEvaluated=t.evaluatedPropsToName=t.mergeEvaluated=t.eachItem=t.unescapeJsonPointer=t.escapeJsonPointer=t.escapeFragment=t.unescapeFragment=t.schemaRefOrVal=t.schemaHasRulesButRef=t.schemaHasRules=t.checkUnknownRules=t.alwaysValidSchema=t.toHash=void 0,t.toHash=function(e){const t={};for(const r of e)t[r]=!0;return t},t.alwaysValidSchema=function(e,t){return"boolean"==typeof t?t:0===Object.keys(t).length||(r(e,t),!s(t,e.self.RULES.all))},t.checkUnknownRules=r,t.schemaHasRules=s,t.schemaHasRulesButRef=function(e,t){if("boolean"==typeof e)return!e;for(const r in e)if("$ref"!==r&&t.all[r])return!0;return!1},t.schemaRefOrVal=function({topSchemaRef:e,schemaPath:t},r,s,a){if(!a){if("number"==typeof r||"boolean"==typeof r)return r;if("string"==typeof r)return c._`${r}`}return c._`${e}${t}${(0,c.getProperty)(s)}`},t.unescapeFragment=function(e){return o(decodeURIComponent(e))},t.escapeFragment=function(e){return encodeURIComponent(a(e))},t.escapeJsonPointer=a,t.unescapeJsonPointer=o,t.eachItem=function(e,t){if(Array.isArray(e))for(const r of e)t(r);else t(e)},t.mergeEvaluated={props:i({mergeNames:(e,t,r)=>e.if(c._`${r} !== true && ${t} !== undefined`,()=>{e.if(c._`${t} === true`,()=>e.assign(r,!0),()=>e.assign(r,c._`${r} || {}`).code(c._`Object.assign(${r}, ${t})`))}),mergeToName:(e,t,r)=>e.if(c._`${r} !== true`,()=>{!0===t?e.assign(r,!0):(e.assign(r,c._`${r} || {}`),d(e,r,t))}),mergeValues:(e,t)=>!0===e||{...e,...t},resultToName:l}),items:i({mergeNames:(e,t,r)=>e.if(c._`${r} !== true && ${t} !== undefined`,()=>e.assign(r,c._`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`)),mergeToName:(e,t,r)=>e.if(c._`${r} !== true`,()=>e.assign(r,!0===t||c._`${r} > ${t} ? ${r} : ${t}`)),mergeValues:(e,t)=>!0===e||Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})},t.evaluatedPropsToName=l,t.setEvaluated=d;const u={};var f;function h(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,!0===r)throw new Error(t);e.self.logger.warn(t)}}t.useFunc=function(e,t){return e.scopeValue("func",{ref:t,code:u[t.code]||(u[t.code]=new n._Code(t.code))})},function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"}(f=t.Type||(t.Type={})),t.getErrorPath=function(e,t,r){if(e instanceof c.Name){const s=t===f.Num;return r?s?c._`"[" + ${e} + "]"`:c._`"['" + ${e} + "']"`:s?c._`"/" + ${e}`:c._`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,c.getProperty)(e).toString():"/"+a(e)},t.checkStrictMode=h});const d={data:new c.Name("data"),valCxt:new c.Name("valCxt"),instancePath:new c.Name("instancePath"),parentData:new c.Name("parentData"),parentDataProperty:new c.Name("parentDataProperty"),rootData:new c.Name("rootData"),dynamicAnchors:new c.Name("dynamicAnchors"),vErrors:new c.Name("vErrors"),errors:new c.Name("errors"),this:new c.Name("this"),self:new c.Name("self"),scope:new c.Name("scope"),json:new c.Name("json"),jsonPos:new c.Name("jsonPos"),jsonLen:new c.Name("jsonLen"),jsonPart:new c.Name("jsonPart")};var u=/*#__PURE__*/Object.defineProperty({default:d},"__esModule",{value:!0}),f=o(function(e,t){function r(e,t){const r=e.const("err",t);e.if(c._`${u.default.vErrors} === null`,()=>e.assign(u.default.vErrors,c._`[${r}]`),c._`${u.default.vErrors}.push(${r})`),e.code(c._`${u.default.errors}++`)}function s(e,t){const{gen:r,validateName:s,schemaEnv:a}=e;a.$async?r.throw(c._`new ${e.ValidationError}(${t})`):(r.assign(c._`${s}.errors`,t),r.return(!1))}Object.defineProperty(t,"__esModule",{value:!0}),t.extendErrors=t.resetErrorsCount=t.reportExtraError=t.reportError=t.keyword$DataError=t.keywordError=void 0,t.keywordError={message:({keyword:e})=>c.str`must pass "${e}" keyword validation`},t.keyword$DataError={message:({keyword:e,schemaType:t})=>t?c.str`"${e}" keyword must be ${t} ($data)`:c.str`"${e}" keyword is invalid ($data)`},t.reportError=function(e,a=t.keywordError,n,i){const{it:l}=e,{gen:d,compositeRule:u,allErrors:f}=l,h=o(e,a,n);(null!=i?i:u||f)?r(d,h):s(l,c._`[${h}]`)},t.reportExtraError=function(e,a=t.keywordError,n){const{it:i}=e,{gen:c,compositeRule:l,allErrors:d}=i;r(c,o(e,a,n)),l||d||s(i,u.default.vErrors)},t.resetErrorsCount=function(e,t){e.assign(u.default.errors,t),e.if(c._`${u.default.vErrors} !== null`,()=>e.if(t,()=>e.assign(c._`${u.default.vErrors}.length`,t),()=>e.assign(u.default.vErrors,null)))},t.extendErrors=function({gen:e,keyword:t,schemaValue:r,data:s,errsCount:a,it:o}){if(void 0===a)throw new Error("ajv implementation error");const n=e.name("err");e.forRange("i",a,u.default.errors,a=>{e.const(n,c._`${u.default.vErrors}[${a}]`),e.if(c._`${n}.instancePath === undefined`,()=>e.assign(c._`${n}.instancePath`,(0,c.strConcat)(u.default.instancePath,o.errorPath))),e.assign(c._`${n}.schemaPath`,c.str`${o.errSchemaPath}/${t}`),o.opts.verbose&&(e.assign(c._`${n}.schema`,r),e.assign(c._`${n}.data`,s))})};const a={keyword:new c.Name("keyword"),schemaPath:new c.Name("schemaPath"),params:new c.Name("params"),propertyName:new c.Name("propertyName"),message:new c.Name("message"),schema:new c.Name("schema"),parentSchema:new c.Name("parentSchema")};function o(e,t,r){const{createErrors:s}=e.it;return!1===s?c._`{}`:function(e,t,r={}){const{gen:s,it:o}=e,l=[n(o,r),i(e,r)];return function(e,{params:t,message:r},s){const{keyword:o,data:n,schemaValue:i,it:l}=e,{opts:d,propertyName:f,topSchemaRef:h,schemaPath:p}=l;s.push([a.keyword,o],[a.params,"function"==typeof t?t(e):t||c._`{}`]),d.messages&&s.push([a.message,"function"==typeof r?r(e):r]),d.verbose&&s.push([a.schema,i],[a.parentSchema,c._`${h}${p}`],[u.default.data,n]),f&&s.push([a.propertyName,f])}(e,t,l),s.object(...l)}(e,t,r)}function n({errorPath:e},{instancePath:t}){const r=t?c.str`${e}${(0,l.getErrorPath)(t,l.Type.Str)}`:e;return[u.default.instancePath,(0,c.strConcat)(u.default.instancePath,r)]}function i({keyword:e,it:{errSchemaPath:t}},{schemaPath:r,parentSchema:s}){let o=s?t:c.str`${t}/${e}`;return r&&(o=c.str`${o}${(0,l.getErrorPath)(r,l.Type.Str)}`),[a.schemaPath,o]}}),h=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.boolOrEmptySchema=t.topBoolOrEmptySchema=void 0;const r={message:"boolean schema is false"};function s(e,t){const{gen:s,data:a}=e;(0,f.reportError)({gen:s,keyword:"false schema",data:a,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e},r,void 0,t)}t.topBoolOrEmptySchema=function(e){const{gen:t,schema:r,validateName:a}=e;!1===r?s(e,!1):"object"==typeof r&&!0===r.$async?t.return(u.default.data):(t.assign(c._`${a}.errors`,null),t.return(!0))},t.boolOrEmptySchema=function(e,t){const{gen:r,schema:a}=e;!1===a?(r.var(t,!1),s(e)):r.var(t,!0)}}),p=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getRules=t.isJSONType=void 0;const r=new Set(["string","number","integer","boolean","null","object","array"]);t.isJSONType=function(e){return"string"==typeof e&&r.has(e)},t.getRules=function(){const e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}}),m=o(function(e,t){function r(e,t){return t.rules.some(t=>s(e,t))}function s(e,t){var r;return void 0!==e[t.keyword]||(null===(r=t.definition.implements)||void 0===r?void 0:r.some(t=>void 0!==e[t]))}Object.defineProperty(t,"__esModule",{value:!0}),t.shouldUseRule=t.shouldUseGroup=t.schemaHasRulesForType=void 0,t.schemaHasRulesForType=function({schema:e,self:t},s){const a=t.RULES.types[s];return a&&!0!==a&&r(e,a)},t.shouldUseGroup=r,t.shouldUseRule=s}),y=o(function(e,t){var r;function s(e){const t=Array.isArray(e)?e:e?[e]:[];if(t.every(p.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}Object.defineProperty(t,"__esModule",{value:!0}),t.reportTypeError=t.checkDataTypes=t.checkDataType=t.coerceAndCheckDataType=t.getJSONTypes=t.getSchemaTypes=t.DataType=void 0,function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"}(r=t.DataType||(t.DataType={})),t.getSchemaTypes=function(e){const t=s(e.type);if(t.includes("null")){if(!1===e.nullable)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&void 0!==e.nullable)throw new Error('"nullable" cannot be used without "type"');!0===e.nullable&&t.push("null")}return t},t.getJSONTypes=s,t.coerceAndCheckDataType=function(e,t){const{gen:s,data:o,opts:i}=e,l=function(e,t){return t?e.filter(e=>a.has(e)||"array"===t&&"array"===e):[]}(t,i.coerceTypes),u=t.length>0&&!(0===l.length&&1===t.length&&(0,m.schemaHasRulesForType)(e,t[0]));if(u){const u=n(t,o,i.strictNumbers,r.Wrong);s.if(u,()=>{l.length?function(e,t,r){const{gen:s,data:o,opts:i}=e,l=s.let("dataType",c._`typeof ${o}`),u=s.let("coerced",c._`undefined`);"array"===i.coerceTypes&&s.if(c._`${l} == 'object' && Array.isArray(${o}) && ${o}.length == 1`,()=>s.assign(o,c._`${o}[0]`).assign(l,c._`typeof ${o}`).if(n(t,o,i.strictNumbers),()=>s.assign(u,o))),s.if(c._`${u} !== undefined`);for(const e of r)(a.has(e)||"array"===e&&"array"===i.coerceTypes)&&f(e);function f(e){switch(e){case"string":return void s.elseIf(c._`${l} == "number" || ${l} == "boolean"`).assign(u,c._`"" + ${o}`).elseIf(c._`${o} === null`).assign(u,c._`""`);case"number":return void s.elseIf(c._`${l} == "boolean" || ${o} === null
              || (${l} == "string" && ${o} && ${o} == +${o})`).assign(u,c._`+${o}`);case"integer":return void s.elseIf(c._`${l} === "boolean" || ${o} === null
              || (${l} === "string" && ${o} && ${o} == +${o} && !(${o} % 1))`).assign(u,c._`+${o}`);case"boolean":return void s.elseIf(c._`${o} === "false" || ${o} === 0 || ${o} === null`).assign(u,!1).elseIf(c._`${o} === "true" || ${o} === 1`).assign(u,!0);case"null":return s.elseIf(c._`${o} === "" || ${o} === 0 || ${o} === false`),void s.assign(u,null);case"array":s.elseIf(c._`${l} === "string" || ${l} === "number"
              || ${l} === "boolean" || ${o} === null`).assign(u,c._`[${o}]`)}}s.else(),d(e),s.endIf(),s.if(c._`${u} !== undefined`,()=>{s.assign(o,u),function({gen:e,parentData:t,parentDataProperty:r},s){e.if(c._`${t} !== undefined`,()=>e.assign(c._`${t}[${r}]`,s))}(e,u)})}(e,t,l):d(e)})}return u};const a=new Set(["string","number","integer","boolean","null"]);function o(e,t,s,a=r.Correct){const o=a===r.Correct?c.operators.EQ:c.operators.NEQ;let n;switch(e){case"null":return c._`${t} ${o} null`;case"array":n=c._`Array.isArray(${t})`;break;case"object":n=c._`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":n=i(c._`!(${t} % 1) && !isNaN(${t})`);break;case"number":n=i();break;default:return c._`typeof ${t} ${o} ${e}`}return a===r.Correct?n:(0,c.not)(n);function i(e=c.nil){return(0,c.and)(c._`typeof ${t} == "number"`,e,s?c._`isFinite(${t})`:c.nil)}}function n(e,t,r,s){if(1===e.length)return o(e[0],t,r,s);let a;const n=(0,l.toHash)(e);if(n.array&&n.object){const e=c._`typeof ${t} != "object"`;a=n.null?e:c._`!${t} || ${e}`,delete n.null,delete n.array,delete n.object}else a=c.nil;n.number&&delete n.integer;for(const e in n)a=(0,c.and)(a,o(e,t,r,s));return a}t.checkDataType=o,t.checkDataTypes=n;const i={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>"string"==typeof e?c._`{type: ${e}}`:c._`{type: ${t}}`};function d(e){const t=function(e){const{gen:t,data:r,schema:s}=e,a=(0,l.schemaRefOrVal)(e,s,"type");return{gen:t,keyword:"type",data:r,schema:s.type,schemaCode:a,schemaValue:a,parentSchema:s,params:{},it:e}}(e);(0,f.reportError)(t,i)}t.reportTypeError=d}),v=o(function(e,t){function r(e,t,r){const{gen:s,compositeRule:a,data:o,opts:n}=e;if(void 0===r)return;const i=c._`${o}${(0,c.getProperty)(t)}`;if(a)return void(0,l.checkStrictMode)(e,`default is ignored for: ${i}`);let d=c._`${i} === undefined`;"empty"===n.useDefaults&&(d=c._`${d} || ${i} === null || ${i} === ""`),s.if(d,c._`${i} = ${(0,c.stringify)(r)}`)}Object.defineProperty(t,"__esModule",{value:!0}),t.assignDefaults=void 0,t.assignDefaults=function(e,t){const{properties:s,items:a}=e.schema;if("object"===t&&s)for(const t in s)r(e,t,s[t].default);else"array"===t&&Array.isArray(a)&&a.forEach((t,s)=>r(e,s,t.default))}}),g=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.validateUnion=t.validateArray=t.usePattern=t.callValidateCode=t.schemaProperties=t.allSchemaProperties=t.noPropertyInData=t.propertyInData=t.isOwnProperty=t.hasPropFunc=t.reportMissingProp=t.checkMissingProp=t.checkReportMissingProp=void 0;const r=l;function s(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:c._`Object.prototype.hasOwnProperty`})}function a(e,t,r){return c._`${s(e)}.call(${t}, ${r})`}function o(e,t,r,s){const o=c._`${t}${(0,c.getProperty)(r)} === undefined`;return s?(0,c.or)(o,(0,c.not)(a(e,t,r))):o}function n(e){return e?Object.keys(e).filter(e=>"__proto__"!==e):[]}t.checkReportMissingProp=function(e,t){const{gen:r,data:s,it:a}=e;r.if(o(r,s,t,a.opts.ownProperties),()=>{e.setParams({missingProperty:c._`${t}`},!0),e.error()})},t.checkMissingProp=function({gen:e,data:t,it:{opts:r}},s,a){return(0,c.or)(...s.map(s=>(0,c.and)(o(e,t,s,r.ownProperties),c._`${a} = ${s}`)))},t.reportMissingProp=function(e,t){e.setParams({missingProperty:t},!0),e.error()},t.hasPropFunc=s,t.isOwnProperty=a,t.propertyInData=function(e,t,r,s){const o=c._`${t}${(0,c.getProperty)(r)} !== undefined`;return s?c._`${o} && ${a(e,t,r)}`:o},t.noPropertyInData=o,t.allSchemaProperties=n,t.schemaProperties=function(e,t){return n(t).filter(r=>!(0,l.alwaysValidSchema)(e,t[r]))},t.callValidateCode=function({schemaCode:e,data:t,it:{gen:r,topSchemaRef:s,schemaPath:a,errorPath:o},it:n},i,l,d){const f=d?c._`${e}, ${t}, ${s}${a}`:t,h=[[u.default.instancePath,(0,c.strConcat)(u.default.instancePath,o)],[u.default.parentData,n.parentData],[u.default.parentDataProperty,n.parentDataProperty],[u.default.rootData,u.default.rootData]];n.opts.dynamicRef&&h.push([u.default.dynamicAnchors,u.default.dynamicAnchors]);const p=c._`${f}, ${r.object(...h)}`;return l!==c.nil?c._`${i}.call(${l}, ${p})`:c._`${i}(${p})`};const i=c._`new RegExp`;t.usePattern=function({gen:e,it:{opts:t}},s){const a=t.unicodeRegExp?"u":"",{regExp:o}=t.code,n=o(s,a);return e.scopeValue("pattern",{key:n.toString(),ref:n,code:c._`${"new RegExp"===o.code?i:(0,r.useFunc)(e,o)}(${s}, ${a})`})},t.validateArray=function(e){const{gen:t,data:r,keyword:s,it:a}=e,o=t.name("valid");if(a.allErrors){const e=t.let("valid",!0);return n(()=>t.assign(e,!1)),e}return t.var(o,!0),n(()=>t.break()),o;function n(a){const n=t.const("len",c._`${r}.length`);t.forRange("i",0,n,r=>{e.subschema({keyword:s,dataProp:r,dataPropType:l.Type.Num},o),t.if((0,c.not)(o),a)})}},t.validateUnion=function(e){const{gen:t,schema:r,keyword:s,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(r.some(e=>(0,l.alwaysValidSchema)(a,e))&&!a.opts.unevaluated)return;const o=t.let("valid",!1),n=t.name("_valid");t.block(()=>r.forEach((r,a)=>{const i=e.subschema({keyword:s,schemaProp:a,compositeRule:!0},n);t.assign(o,c._`${o} || ${n}`),e.mergeValidEvaluated(i,n)||t.if((0,c.not)(o))})),e.result(o,()=>e.reset(),()=>e.error(!0))}}),$=o(function(e,t){function r(e){const{gen:t,data:r,it:s}=e;t.if(s.parentData,()=>t.assign(r,c._`${s.parentData}[${s.parentDataProperty}]`))}function s(e,t,r){if(void 0===r)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword","function"==typeof r?{ref:r}:{ref:r,code:(0,c.stringify)(r)})}Object.defineProperty(t,"__esModule",{value:!0}),t.validateKeywordUsage=t.validSchemaType=t.funcKeywordCode=t.macroKeywordCode=void 0,t.macroKeywordCode=function(e,t){const{gen:r,keyword:a,schema:o,parentSchema:n,it:i}=e,l=t.macro.call(i.self,o,n,i),d=s(r,a,l);!1!==i.opts.validateSchema&&i.self.validateSchema(l,!0);const u=r.name("valid");e.subschema({schema:l,schemaPath:c.nil,errSchemaPath:`${i.errSchemaPath}/${a}`,topSchemaRef:d,compositeRule:!0},u),e.pass(u,()=>e.error(!0))},t.funcKeywordCode=function(e,t){var a;const{gen:o,keyword:n,schema:i,parentSchema:l,$data:d,it:h}=e;!function({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}(h,t);const p=!d&&t.compile?t.compile.call(h.self,i,l,h):t.validate,m=s(o,n,p),y=o.let("valid");function v(r=(t.async?c._`await `:c.nil)){o.assign(y,c._`${r}${(0,g.callValidateCode)(e,m,h.opts.passContext?u.default.this:u.default.self,!("compile"in t&&!d||!1===t.schema))}`,t.modifying)}function $(e){var r;o.if((0,c.not)(null!==(r=t.valid)&&void 0!==r?r:y),e)}e.block$data(y,function(){if(!1===t.errors)v(),t.modifying&&r(e),$(()=>e.error());else{const s=t.async?function(){const e=o.let("ruleErrs",null);return o.try(()=>v(c._`await `),t=>o.assign(y,!1).if(c._`${t} instanceof ${h.ValidationError}`,()=>o.assign(e,c._`${t}.errors`),()=>o.throw(t))),e}():function(){const e=c._`${m}.errors`;return o.assign(e,null),v(c.nil),e}();t.modifying&&r(e),$(()=>function(e,t){const{gen:r}=e;r.if(c._`Array.isArray(${t})`,()=>{r.assign(u.default.vErrors,c._`${u.default.vErrors} === null ? ${t} : ${u.default.vErrors}.concat(${t})`).assign(u.default.errors,c._`${u.default.vErrors}.length`),(0,f.extendErrors)(e)},()=>e.error())}(e,s))}}),e.ok(null!==(a=t.valid)&&void 0!==a?a:y)},t.validSchemaType=function(e,t,r=!1){return!t.length||t.some(t=>"array"===t?Array.isArray(e):"object"===t?e&&"object"==typeof e&&!Array.isArray(e):typeof e==t||r&&void 0===e)},t.validateKeywordUsage=function({schema:e,opts:t,self:r,errSchemaPath:s},a,o){if(Array.isArray(a.keyword)?!a.keyword.includes(o):a.keyword!==o)throw new Error("ajv implementation error");const n=a.dependencies;if(null==n?void 0:n.some(t=>!Object.prototype.hasOwnProperty.call(e,t)))throw new Error(`parent schema must have dependencies of ${o}: ${n.join(",")}`);if(a.validateSchema&&!a.validateSchema(e[o])){const e=`keyword "${o}" value is invalid at path "${s}": `+r.errorsText(a.validateSchema.errors);if("log"!==t.validateSchema)throw new Error(e);r.logger.error(e)}}}),_=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.extendSubschemaMode=t.extendSubschemaData=t.getSubschema=void 0,t.getSubschema=function(e,{keyword:t,schemaProp:r,schema:s,schemaPath:a,errSchemaPath:o,topSchemaRef:n}){if(void 0!==t&&void 0!==s)throw new Error('both "keyword" and "schema" passed, only one allowed');if(void 0!==t){const s=e.schema[t];return void 0===r?{schema:s,schemaPath:c._`${e.schemaPath}${(0,c.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:s[r],schemaPath:c._`${e.schemaPath}${(0,c.getProperty)(t)}${(0,c.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,l.escapeFragment)(r)}`}}if(void 0!==s){if(void 0===a||void 0===o||void 0===n)throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:s,schemaPath:a,topSchemaRef:n,errSchemaPath:o}}throw new Error('either "keyword" or "schema" must be passed')},t.extendSubschemaData=function(e,t,{dataProp:r,dataPropType:s,data:a,dataTypes:o,propertyName:n}){if(void 0!==a&&void 0!==r)throw new Error('both "data" and "dataProp" passed, only one allowed');const{gen:i}=t;if(void 0!==r){const{errorPath:a,dataPathArr:o,opts:n}=t;d(i.let("data",c._`${t.data}${(0,c.getProperty)(r)}`,!0)),e.errorPath=c.str`${a}${(0,l.getErrorPath)(r,s,n.jsPropertySyntax)}`,e.parentDataProperty=c._`${r}`,e.dataPathArr=[...o,e.parentDataProperty]}function d(r){e.data=r,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,r]}void 0!==a&&(d(a instanceof c.Name?a:i.let("data",a,!0)),void 0!==n&&(e.propertyName=n)),o&&(e.dataTypes=o)},t.extendSubschemaMode=function(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:s,createErrors:a,allErrors:o}){void 0!==s&&(e.compositeRule=s),void 0!==a&&(e.createErrors=a),void 0!==o&&(e.allErrors=o),e.jtdDiscriminator=t,e.jtdMetadata=r}}),w=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var s,a,o;if(Array.isArray(t)){if((s=t.length)!=r.length)return!1;for(a=s;0!=a--;)if(!e(t[a],r[a]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((s=(o=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(a=s;0!=a--;)if(!Object.prototype.hasOwnProperty.call(r,o[a]))return!1;for(a=s;0!=a--;){var n=o[a];if(!e(t[n],r[n]))return!1}return!0}return t!=t&&r!=r},b=o(function(e){var t=e.exports=function(e,t,s){"function"==typeof t&&(s=t,t={}),r(t,"function"==typeof(s=t.cb||s)?s:s.pre||function(){},s.post||function(){},e,"",e)};function r(e,s,a,o,n,i,c,l,d,u){if(o&&"object"==typeof o&&!Array.isArray(o)){for(var f in s(o,n,i,c,l,d,u),o){var h=o[f];if(Array.isArray(h)){if(f in t.arrayKeywords)for(var p=0;p<h.length;p++)r(e,s,a,h[p],n+"/"+f+"/"+p,i,n,f,o,p)}else if(f in t.propsKeywords){if(h&&"object"==typeof h)for(var m in h)r(e,s,a,h[m],n+"/"+f+"/"+m.replace(/~/g,"~0").replace(/\//g,"~1"),i,n,f,o,m)}else(f in t.keywords||e.allKeys&&!(f in t.skipKeywords))&&r(e,s,a,h,n+"/"+f,i,n,f,o)}a(o,n,i,c,l,d,u)}}t.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0,if:!0,then:!0,else:!0},t.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0},t.propsKeywords={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependencies:!0},t.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0}}),E=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getSchemaRefs=t.resolveUrl=t.normalizeId=t._getFullPath=t.getFullPath=t.inlineRef=void 0;const r=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);t.inlineRef=function(e,t=!0){return"boolean"==typeof e||(!0===t?!a(e):!!t&&o(e)<=t)};const s=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function a(e){for(const t in e){if(s.has(t))return!0;const r=e[t];if(Array.isArray(r)&&r.some(a))return!0;if("object"==typeof r&&a(r))return!0}return!1}function o(e){let t=0;for(const s in e){if("$ref"===s)return Infinity;if(t++,!r.has(s)&&("object"==typeof e[s]&&(0,l.eachItem)(e[s],e=>t+=o(e)),Infinity===t))return Infinity}return t}function n(e,t="",r){!1!==r&&(t=d(t));const s=e.parse(t);return i(e,s)}function i(e,t){return e.serialize(t).split("#")[0]+"#"}t.getFullPath=n,t._getFullPath=i;const c=/#\/?$/;function d(e){return e?e.replace(c,""):""}t.normalizeId=d,t.resolveUrl=function(e,t,r){return r=d(r),e.resolve(t,r)};const u=/^[a-z_][-a-z0-9._]*$/i;t.getSchemaRefs=function(e,t){if("boolean"==typeof e)return{};const{schemaId:r,uriResolver:s}=this.opts,a=d(e[r]||t),o={"":a},i=n(s,a,!1),c={},l=new Set;return b(e,{allKeys:!0},(e,t,s,a)=>{if(void 0===a)return;const n=i+t;let p=o[a];function m(t){if(t=d(p?(0,this.opts.uriResolver.resolve)(p,t):t),l.has(t))throw h(t);l.add(t);let r=this.refs[t];return"string"==typeof r&&(r=this.refs[r]),"object"==typeof r?f(e,r.schema,t):t!==d(n)&&("#"===t[0]?(f(e,c[t],t),c[t]=e):this.refs[t]=n),t}function y(e){if("string"==typeof e){if(!u.test(e))throw new Error(`invalid anchor "${e}"`);m.call(this,`#${e}`)}}"string"==typeof e[r]&&(p=m.call(this,e[r])),y.call(this,e.$anchor),y.call(this,e.$dynamicAnchor),o[t]=p}),c;function f(e,t,r){if(void 0!==t&&!w(e,t))throw h(r)}function h(e){return new Error(`reference "${e}" resolves to more than one schema`)}}}),P=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getData=t.KeywordCxt=t.validateFunctionCode=void 0;const r=y;function s({gen:e,validateName:t,schema:r,schemaEnv:s,opts:o},n){o.code.es5?e.func(t,c._`${u.default.data}, ${u.default.valCxt}`,s.$async,()=>{e.code(c._`"use strict"; ${a(r,o)}`),function(e,t){e.if(u.default.valCxt,()=>{e.var(u.default.instancePath,c._`${u.default.valCxt}.${u.default.instancePath}`),e.var(u.default.parentData,c._`${u.default.valCxt}.${u.default.parentData}`),e.var(u.default.parentDataProperty,c._`${u.default.valCxt}.${u.default.parentDataProperty}`),e.var(u.default.rootData,c._`${u.default.valCxt}.${u.default.rootData}`),t.dynamicRef&&e.var(u.default.dynamicAnchors,c._`${u.default.valCxt}.${u.default.dynamicAnchors}`)},()=>{e.var(u.default.instancePath,c._`""`),e.var(u.default.parentData,c._`undefined`),e.var(u.default.parentDataProperty,c._`undefined`),e.var(u.default.rootData,u.default.data),t.dynamicRef&&e.var(u.default.dynamicAnchors,c._`{}`)})}(e,o),e.code(n)}):e.func(t,c._`${u.default.data}, ${function(e){return c._`{${u.default.instancePath}="", ${u.default.parentData}, ${u.default.parentDataProperty}, ${u.default.rootData}=${u.default.data}${e.dynamicRef?c._`, ${u.default.dynamicAnchors}={}`:c.nil}}={}`}(o)}`,s.$async,()=>e.code(a(r,o)).code(n))}function a(e,t){const r="object"==typeof e&&e[t.schemaId];return r&&(t.code.source||t.code.process)?c._`/*# sourceURL=${r} */`:c.nil}function o({schema:e,self:t}){if("boolean"==typeof e)return!e;for(const r in e)if(t.RULES.all[r])return!0;return!1}function n(e){return"boolean"!=typeof e.schema}function i(e){(0,l.checkUnknownRules)(e),function(e){const{schema:t,errSchemaPath:r,opts:s,self:a}=e;t.$ref&&s.ignoreKeywordsWithRef&&(0,l.schemaHasRulesButRef)(t,a.RULES)&&a.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}(e)}function d(e,t){if(e.opts.jtd)return g(e,[],!1,t);const r=(0,y.getSchemaTypes)(e.schema);g(e,r,!(0,y.coerceAndCheckDataType)(e,r),t)}function p({gen:e,schemaEnv:t,schema:r,errSchemaPath:s,opts:a}){const o=r.$comment;if(!0===a.$comment)e.code(c._`${u.default.self}.logger.log(${o})`);else if("function"==typeof a.$comment){const r=c.str`${s}/$comment`,a=e.scopeValue("root",{ref:t.root});e.code(c._`${u.default.self}.opts.$comment(${o}, ${r}, ${a}.schema)`)}}function g(e,t,s,a){const{gen:o,schema:n,data:i,allErrors:d,opts:f,self:h}=e,{RULES:p}=h;function y(l){(0,m.shouldUseGroup)(n,l)&&(l.type?(o.if((0,r.checkDataType)(l.type,i,f.strictNumbers)),w(e,l),1===t.length&&t[0]===l.type&&s&&(o.else(),(0,r.reportTypeError)(e)),o.endIf()):w(e,l),d||o.if(c._`${u.default.errors} === ${a||0}`))}!n.$ref||!f.ignoreKeywordsWithRef&&(0,l.schemaHasRulesButRef)(n,p)?(f.jtd||function(e,t){!e.schemaEnv.meta&&e.opts.strictTypes&&(function(e,t){t.length&&(e.dataTypes.length?(t.forEach(t=>{b(e.dataTypes,t)||P(e,`type "${t}" not allowed by context "${e.dataTypes.join(",")}"`)}),e.dataTypes=e.dataTypes.filter(e=>b(t,e))):e.dataTypes=t)}(e,t),e.opts.allowUnionTypes||function(e,t){t.length>1&&(2!==t.length||!t.includes("null"))&&P(e,"use allowUnionTypes to allow union type keyword")}(e,t),function(e,t){const r=e.self.RULES.all;for(const s in r){const a=r[s];if("object"==typeof a&&(0,m.shouldUseRule)(e.schema,a)){const{type:r}=a.definition;r.length&&!r.some(e=>{return(r=t).includes(s=e)||"number"===s&&r.includes("integer");var r,s})&&P(e,`missing type "${r.join(",")}" for keyword "${s}"`)}}}(e,e.dataTypes))}(e,t),o.block(()=>{for(const e of p.rules)y(e);y(p.post)})):o.block(()=>k(e,"$ref",p.all.$ref.definition))}function w(e,t){const{gen:r,schema:s,opts:{useDefaults:a}}=e;a&&(0,v.assignDefaults)(e,t.type),r.block(()=>{for(const r of t.rules)(0,m.shouldUseRule)(s,r)&&k(e,r.keyword,r.definition,t.type)})}function b(e,t){return e.includes(t)||"integer"===t&&e.includes("number")}function P(e,t){(0,l.checkStrictMode)(e,t+=` at "${e.schemaEnv.baseId+e.errSchemaPath}" (strictTypes)`,e.opts.strictTypes)}t.validateFunctionCode=function(e){n(e)&&(i(e),o(e))?function(e){const{schema:t,opts:r,gen:a}=e;s(e,()=>{r.$comment&&t.$comment&&p(e),function(e){const{schema:t,opts:r}=e;void 0!==t.default&&r.useDefaults&&r.strictSchema&&(0,l.checkStrictMode)(e,"default is ignored in the schema root")}(e),a.let(u.default.vErrors,null),a.let(u.default.errors,0),r.unevaluated&&function(e){const{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",c._`${r}.evaluated`),t.if(c._`${e.evaluated}.dynamicProps`,()=>t.assign(c._`${e.evaluated}.props`,c._`undefined`)),t.if(c._`${e.evaluated}.dynamicItems`,()=>t.assign(c._`${e.evaluated}.items`,c._`undefined`))}(e),d(e),function(e){const{gen:t,schemaEnv:r,validateName:s,ValidationError:a,opts:o}=e;r.$async?t.if(c._`${u.default.errors} === 0`,()=>t.return(u.default.data),()=>t.throw(c._`new ${a}(${u.default.vErrors})`)):(t.assign(c._`${s}.errors`,u.default.vErrors),o.unevaluated&&function({gen:e,evaluated:t,props:r,items:s}){r instanceof c.Name&&e.assign(c._`${t}.props`,r),s instanceof c.Name&&e.assign(c._`${t}.items`,s)}(e),t.return(c._`${u.default.errors} === 0`))}(e)})}(e):s(e,()=>(0,h.topBoolOrEmptySchema)(e))};class S{constructor(e,t,r){if((0,$.validateKeywordUsage)(e,t,r),this.gen=e.gen,this.allErrors=e.allErrors,this.keyword=r,this.data=e.data,this.schema=e.schema[r],this.$data=t.$data&&e.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,l.schemaRefOrVal)(e,this.schema,r,this.$data),this.schemaType=t.schemaType,this.parentSchema=e.schema,this.params={},this.it=e,this.def=t,this.$data)this.schemaCode=e.gen.const("vSchema",C(this.$data,e));else if(this.schemaCode=this.schemaValue,!(0,$.validSchemaType)(this.schema,t.schemaType,t.allowUndefined))throw new Error(`${r} value must be ${JSON.stringify(t.schemaType)}`);("code"in t?t.trackErrors:!1!==t.errors)&&(this.errsCount=e.gen.const("_errs",u.default.errors))}result(e,t,r){this.failResult((0,c.not)(e),t,r)}failResult(e,t,r){this.gen.if(e),r?r():this.error(),t?(this.gen.else(),t(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(e,t){this.failResult((0,c.not)(e),void 0,t)}fail(e){if(void 0===e)return this.error(),void(this.allErrors||this.gen.if(!1));this.gen.if(e),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(e){if(!this.$data)return this.fail(e);const{schemaCode:t}=this;this.fail(c._`${t} !== undefined && (${(0,c.or)(this.invalid$data(),e)})`)}error(e,t,r){if(t)return this.setParams(t),this._error(e,r),void this.setParams({});this._error(e,r)}_error(e,t){(e?f.reportExtraError:f.reportError)(this,this.def.error,t)}$dataError(){(0,f.reportError)(this,this.def.$dataError||f.keyword$DataError)}reset(){if(void 0===this.errsCount)throw new Error('add "trackErrors" to keyword definition');(0,f.resetErrorsCount)(this.gen,this.errsCount)}ok(e){this.allErrors||this.gen.if(e)}setParams(e,t){t?Object.assign(this.params,e):this.params=e}block$data(e,t,r=c.nil){this.gen.block(()=>{this.check$data(e,r),t()})}check$data(e=c.nil,t=c.nil){if(!this.$data)return;const{gen:r,schemaCode:s,schemaType:a,def:o}=this;r.if((0,c.or)(c._`${s} === undefined`,t)),e!==c.nil&&r.assign(e,!0),(a.length||o.validateSchema)&&(r.elseIf(this.invalid$data()),this.$dataError(),e!==c.nil&&r.assign(e,!1)),r.else()}invalid$data(){const{gen:e,schemaCode:t,schemaType:s,def:a,it:o}=this;return(0,c.or)(function(){if(s.length){if(!(t instanceof c.Name))throw new Error("ajv implementation error");const e=Array.isArray(s)?s:[s];return c._`${(0,r.checkDataTypes)(e,t,o.opts.strictNumbers,r.DataType.Wrong)}`}return c.nil}(),function(){if(a.validateSchema){const r=e.scopeValue("validate$data",{ref:a.validateSchema});return c._`!${r}(${t})`}return c.nil}())}subschema(e,t){const r=(0,_.getSubschema)(this.it,e);(0,_.extendSubschemaData)(r,this.it,e),(0,_.extendSubschemaMode)(r,e);const s={...this.it,...r,items:void 0,props:void 0};return function(e,t){n(e)&&(i(e),o(e))?function(e,t){const{schema:r,gen:s,opts:a}=e;a.$comment&&r.$comment&&p(e),function(e){const t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,E.resolveUrl)(e.opts.uriResolver,e.baseId,t))}(e),function(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}(e);const o=s.const("_errs",u.default.errors);d(e,o),s.var(t,c._`${o} === ${u.default.errors}`)}(e,t):(0,h.boolOrEmptySchema)(e,t)}(s,t),s}mergeEvaluated(e,t){const{it:r,gen:s}=this;r.opts.unevaluated&&(!0!==r.props&&void 0!==e.props&&(r.props=l.mergeEvaluated.props(s,e.props,r.props,t)),!0!==r.items&&void 0!==e.items&&(r.items=l.mergeEvaluated.items(s,e.items,r.items,t)))}mergeValidEvaluated(e,t){const{it:r,gen:s}=this;if(r.opts.unevaluated&&(!0!==r.props||!0!==r.items))return s.if(t,()=>this.mergeEvaluated(e,c.Name)),!0}}function k(e,t,r,s){const a=new S(e,r,t);"code"in r?r.code(a,s):a.$data&&r.validate?(0,$.funcKeywordCode)(a,r):"macro"in r?(0,$.macroKeywordCode)(a,r):(r.compile||r.validate)&&(0,$.funcKeywordCode)(a,r)}t.KeywordCxt=S;const N=/^\/(?:[^~]|~0|~1)*$/,j=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function C(e,{dataLevel:t,dataNames:r,dataPathArr:s}){let a,o;if(""===e)return u.default.rootData;if("/"===e[0]){if(!N.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);a=e,o=u.default.rootData}else{const n=j.exec(e);if(!n)throw new Error(`Invalid JSON-pointer: ${e}`);const i=+n[1];if(a=n[2],"#"===a){if(i>=t)throw new Error(d("property/index",i));return s[t-i]}if(i>t)throw new Error(d("data",i));if(o=r[t-i],!a)return o}let n=o;const i=a.split("/");for(const e of i)e&&(o=c._`${o}${(0,c.getProperty)((0,l.unescapeJsonPointer)(e))}`,n=c._`${n} && ${o}`);return n;function d(e,r){return`Cannot access ${e} ${r} levels up, current level is ${t}`}}t.getData=C});class S extends Error{constructor(e){super("validation failed"),this.errors=e,this.ajv=this.validation=!0}}var k=/*#__PURE__*/Object.defineProperty({default:S},"__esModule",{value:!0});class N extends Error{constructor(e,t,r,s){super(s||`can't resolve reference ${r} from id ${t}`),this.missingRef=(0,E.resolveUrl)(e,t,r),this.missingSchema=(0,E.normalizeId)((0,E.getFullPath)(e,this.missingRef))}}var j=/*#__PURE__*/Object.defineProperty({default:N},"__esModule",{value:!0}),C=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.resolveSchema=t.getCompilingSchema=t.resolveRef=t.compileSchema=t.SchemaEnv=void 0;class r{constructor(e){var t;let r;this.refs={},this.dynamicAnchors={},"object"==typeof e.schema&&(r=e.schema),this.schema=e.schema,this.schemaId=e.schemaId,this.root=e.root||this,this.baseId=null!==(t=e.baseId)&&void 0!==t?t:(0,E.normalizeId)(null==r?void 0:r[e.schemaId||"$id"]),this.schemaPath=e.schemaPath,this.localRefs=e.localRefs,this.meta=e.meta,this.$async=null==r?void 0:r.$async,this.refs={}}}function s(e){const t=o.call(this,e);if(t)return t;const r=(0,E.getFullPath)(this.opts.uriResolver,e.root.baseId),{es5:s,lines:a}=this.opts.code,{ownProperties:n}=this.opts,i=new c.CodeGen(this.scope,{es5:s,lines:a,ownProperties:n});let l;e.$async&&(l=i.scopeValue("Error",{ref:k.default,code:c._`require("ajv/dist/runtime/validation_error").default`}));const d=i.scopeName("validate");e.validateName=d;const f={gen:i,allErrors:this.opts.allErrors,data:u.default.data,parentData:u.default.parentData,parentDataProperty:u.default.parentDataProperty,dataNames:[u.default.data],dataPathArr:[c.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:i.scopeValue("schema",!0===this.opts.code.source?{ref:e.schema,code:(0,c.stringify)(e.schema)}:{ref:e.schema}),validateName:d,ValidationError:l,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:c.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:c._`""`,opts:this.opts,self:this};let h;try{this._compilations.add(e),(0,P.validateFunctionCode)(f),i.optimize(this.opts.code.optimize);const t=i.toString();h=`${i.scopeRefs(u.default.scope)}return ${t}`,this.opts.code.process&&(h=this.opts.code.process(h,e));const r=new Function(`${u.default.self}`,`${u.default.scope}`,h)(this,this.scope.get());if(this.scope.value(d,{ref:r}),r.errors=null,r.schema=e.schema,r.schemaEnv=e,e.$async&&(r.$async=!0),!0===this.opts.code.source&&(r.source={validateName:d,validateCode:t,scopeValues:i._values}),this.opts.unevaluated){const{props:e,items:t}=f;r.evaluated={props:e instanceof c.Name?void 0:e,items:t instanceof c.Name?void 0:t,dynamicProps:e instanceof c.Name,dynamicItems:t instanceof c.Name},r.source&&(r.source.evaluated=(0,c.stringify)(r.evaluated))}return e.validate=r,e}catch(t){throw delete e.validate,delete e.validateName,h&&this.logger.error("Error compiling schema, function code:",h),t}finally{this._compilations.delete(e)}}function a(e){return(0,E.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:s.call(this,e)}function o(e){for(const s of this._compilations)if((t=s).schema===(r=e).schema&&t.root===r.root&&t.baseId===r.baseId)return s;var t,r}function n(e,t){let r;for(;"string"==typeof(r=this.refs[t]);)t=r;return r||this.schemas[t]||i.call(this,e,t)}function i(e,t){const a=this.opts.uriResolver.parse(t),o=(0,E._getFullPath)(this.opts.uriResolver,a);let n=(0,E.getFullPath)(this.opts.uriResolver,e.baseId,void 0);if(Object.keys(e.schema).length>0&&o===n)return f.call(this,a,e);const c=(0,E.normalizeId)(o),l=this.refs[c]||this.schemas[c];if("string"==typeof l){const t=i.call(this,e,l);if("object"!=typeof(null==t?void 0:t.schema))return;return f.call(this,a,t)}if("object"==typeof(null==l?void 0:l.schema)){if(l.validate||s.call(this,l),c===(0,E.normalizeId)(t)){const{schema:t}=l,{schemaId:s}=this.opts,a=t[s];return a&&(n=(0,E.resolveUrl)(this.opts.uriResolver,n,a)),new r({schema:t,schemaId:s,root:e,baseId:n})}return f.call(this,a,l)}}t.SchemaEnv=r,t.compileSchema=s,t.resolveRef=function(e,t,s){var o;s=(0,E.resolveUrl)(this.opts.uriResolver,t,s);const i=e.refs[s];if(i)return i;let c=n.call(this,e,s);if(void 0===c){const a=null===(o=e.localRefs)||void 0===o?void 0:o[s],{schemaId:n}=this.opts;a&&(c=new r({schema:a,schemaId:n,root:e,baseId:t}))}return void 0!==c?e.refs[s]=a.call(this,c):void 0},t.getCompilingSchema=o,t.resolveSchema=i;const d=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function f(e,{baseId:t,schema:s,root:a}){var o;if("/"!==(null===(o=e.fragment)||void 0===o?void 0:o[0]))return;for(const r of e.fragment.slice(1).split("/")){if("boolean"==typeof s)return;const e=s[(0,l.unescapeFragment)(r)];if(void 0===e)return;const a="object"==typeof(s=e)&&s[this.opts.schemaId];!d.has(r)&&a&&(t=(0,E.resolveUrl)(this.opts.uriResolver,t,a))}let n;if("boolean"!=typeof s&&s.$ref&&!(0,l.schemaHasRulesButRef)(s,this.RULES)){const e=(0,E.resolveUrl)(this.opts.uriResolver,t,s.$ref);n=i.call(this,a,e)}const{schemaId:c}=this.opts;return n=n||new r({schema:s,schemaId:c,root:a,baseId:t}),n.schema!==n.root.schema?n:void 0}}),O={$id:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#",description:"Meta-schema for $data reference (JSON AnySchema extension proposal)",type:"object",required:["$data"],properties:{$data:{type:"string",anyOf:[{format:"relative-json-pointer"},{format:"json-pointer"}]}},additionalProperties:!1},x=o(function(e,t){!function(e){function t(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(t.length>1){t[0]=t[0].slice(0,-1);for(var s=t.length-1,a=1;a<s;++a)t[a]=t[a].slice(1,-1);return t[s]=t[s].slice(1),t.join("")}return t[0]}function r(e){return"(?:"+e+")"}function s(e){return void 0===e?"undefined":null===e?"null":Object.prototype.toString.call(e).split(" ").pop().split("]").shift().toLowerCase()}function a(e){return e.toUpperCase()}function o(e){var s="[A-Za-z]",a="[0-9]",o=t(a,"[A-Fa-f]"),n=r(r("%[EFef]"+o+"%"+o+o+"%"+o+o)+"|"+r("%[89A-Fa-f]"+o+"%"+o+o)+"|"+r("%"+o+o)),i="[\\!\\$\\&\\'\\(\\)\\*\\+\\,\\;\\=]",c=t("[\\:\\/\\?\\#\\[\\]\\@]",i),l=e?"[\\uE000-\\uF8FF]":"[]",d=t(s,a,"[\\-\\.\\_\\~]",e?"[\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]":"[]");r(s+t(s,a,"[\\+\\-\\.]")+"*"),r(r(n+"|"+t(d,i,"[\\:]"))+"*");var u=r(r("25[0-5]")+"|"+r("2[0-4][0-9]")+"|"+r("1[0-9][0-9]")+"|"+r("0?[1-9][0-9]")+"|0?0?"+a),f=r(u+"\\."+u+"\\."+u+"\\."+u),h=r(o+"{1,4}"),p=r(r(h+"\\:"+h)+"|"+f),m=r(r(h+"\\:")+"{6}"+p),y=r("\\:\\:"+r(h+"\\:")+"{5}"+p),v=r(r(h)+"?\\:\\:"+r(h+"\\:")+"{4}"+p),g=r(r(r(h+"\\:")+"{0,1}"+h)+"?\\:\\:"+r(h+"\\:")+"{3}"+p),$=r(r(r(h+"\\:")+"{0,2}"+h)+"?\\:\\:"+r(h+"\\:")+"{2}"+p),_=r(r(r(h+"\\:")+"{0,3}"+h)+"?\\:\\:"+h+"\\:"+p),w=r(r(r(h+"\\:")+"{0,4}"+h)+"?\\:\\:"+p),b=r(r(r(h+"\\:")+"{0,5}"+h)+"?\\:\\:"+h),E=r(r(r(h+"\\:")+"{0,6}"+h)+"?\\:\\:"),P=r([m,y,v,g,$,_,w,b,E].join("|")),S=r(r(d+"|"+n)+"+");r("[vV]"+o+"+\\."+t(d,i,"[\\:]")+"+"),r(r(n+"|"+t(d,i))+"*");var k=r(n+"|"+t(d,i,"[\\:\\@]"));return r(r(n+"|"+t(d,i,"[\\@]"))+"+"),r(r(k+"|"+t("[\\/\\?]",l))+"*"),{NOT_SCHEME:new RegExp(t("[^]",s,a,"[\\+\\-\\.]"),"g"),NOT_USERINFO:new RegExp(t("[^\\%\\:]",d,i),"g"),NOT_HOST:new RegExp(t("[^\\%\\[\\]\\:]",d,i),"g"),NOT_PATH:new RegExp(t("[^\\%\\/\\:\\@]",d,i),"g"),NOT_PATH_NOSCHEME:new RegExp(t("[^\\%\\/\\@]",d,i),"g"),NOT_QUERY:new RegExp(t("[^\\%]",d,i,"[\\:\\@\\/\\?]",l),"g"),NOT_FRAGMENT:new RegExp(t("[^\\%]",d,i,"[\\:\\@\\/\\?]"),"g"),ESCAPE:new RegExp(t("[^]",d,i),"g"),UNRESERVED:new RegExp(d,"g"),OTHER_CHARS:new RegExp(t("[^\\%]",d,c),"g"),PCT_ENCODED:new RegExp(n,"g"),IPV4ADDRESS:new RegExp("^("+f+")$"),IPV6ADDRESS:new RegExp("^\\[?("+P+")"+r(r("\\%25|\\%(?!"+o+"{2})")+"("+S+")")+"?\\]?$")}}var n=o(!1),i=o(!0),c=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var r=[],s=!0,a=!1,o=void 0;try{for(var n,i=e[Symbol.iterator]();!(s=(n=i.next()).done)&&(r.push(n.value),!t||r.length!==t);s=!0);}catch(e){a=!0,o=e}finally{try{!s&&i.return&&i.return()}finally{if(a)throw o}}return r}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")},l=2147483647,d=36,u=/^xn--/,f=/[^\0-\x7E]/,h=/[\x2E\u3002\uFF0E\uFF61]/g,p={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},m=Math.floor,y=String.fromCharCode;function v(e){throw new RangeError(p[e])}function g(e,t){var r=e.split("@"),s="";return r.length>1&&(s=r[0]+"@",e=r[1]),s+function(e,t){for(var r=[],s=e.length;s--;)r[s]=t(e[s]);return r}((e=e.replace(h,".")).split("."),t).join(".")}var $=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},_=function(e,t,r){var s=0;for(e=r?m(e/700):e>>1,e+=m(e/t);e>455;s+=d)e=m(e/35);return m(s+36*e/(e+38))},w=function(e){return g(e,function(e){return f.test(e)?"xn--"+function(e){var t=[],r=(e=function(e){for(var t=[],r=0,s=e.length;r<s;){var a=e.charCodeAt(r++);if(a>=55296&&a<=56319&&r<s){var o=e.charCodeAt(r++);56320==(64512&o)?t.push(((1023&a)<<10)+(1023&o)+65536):(t.push(a),r--)}else t.push(a)}return t}(e)).length,s=128,a=0,o=72,n=!0,i=!1,c=void 0;try{for(var u,f=e[Symbol.iterator]();!(n=(u=f.next()).done);n=!0){var h=u.value;h<128&&t.push(y(h))}}catch(e){i=!0,c=e}finally{try{!n&&f.return&&f.return()}finally{if(i)throw c}}var p=t.length,g=p;for(p&&t.push("-");g<r;){var w=l,b=!0,E=!1,P=void 0;try{for(var S,k=e[Symbol.iterator]();!(b=(S=k.next()).done);b=!0){var N=S.value;N>=s&&N<w&&(w=N)}}catch(e){E=!0,P=e}finally{try{!b&&k.return&&k.return()}finally{if(E)throw P}}var j=g+1;w-s>m((l-a)/j)&&v("overflow"),a+=(w-s)*j,s=w;var C=!0,O=!1,x=void 0;try{for(var T,R=e[Symbol.iterator]();!(C=(T=R.next()).done);C=!0){var I=T.value;if(I<s&&++a>l&&v("overflow"),I==s){for(var D=a,A=d;;A+=d){var M=A<=o?1:A>=o+26?26:A-o;if(D<M)break;var V=D-M,F=d-M;t.push(y($(M+V%F,0))),D=m(V/F)}t.push(y($(D,0))),o=_(a,j,g==p),a=0,++g}}}catch(e){O=!0,x=e}finally{try{!C&&R.return&&R.return()}finally{if(O)throw x}}++a,++s}return t.join("")}(e):e})},b=function(e){return g(e,function(e){return u.test(e)?function(e){var t,r=[],s=e.length,a=0,o=128,n=72,i=e.lastIndexOf("-");i<0&&(i=0);for(var c=0;c<i;++c)e.charCodeAt(c)>=128&&v("not-basic"),r.push(e.charCodeAt(c));for(var u=i>0?i+1:0;u<s;){for(var f=a,h=1,p=d;;p+=d){u>=s&&v("invalid-input");var y=(t=e.charCodeAt(u++))-48<10?t-22:t-65<26?t-65:t-97<26?t-97:d;(y>=d||y>m((l-a)/h))&&v("overflow"),a+=y*h;var g=p<=n?1:p>=n+26?26:p-n;if(y<g)break;var $=d-g;h>m(l/$)&&v("overflow"),h*=$}var w=r.length+1;n=_(a-f,w,0==f),m(a/w)>l-o&&v("overflow"),o+=m(a/w),a%=w,r.splice(a++,0,o)}return String.fromCodePoint.apply(String,r)}(e.slice(4).toLowerCase()):e})},E={};function P(e){var t=e.charCodeAt(0);return t<16?"%0"+t.toString(16).toUpperCase():t<128?"%"+t.toString(16).toUpperCase():t<2048?"%"+(t>>6|192).toString(16).toUpperCase()+"%"+(63&t|128).toString(16).toUpperCase():"%"+(t>>12|224).toString(16).toUpperCase()+"%"+(t>>6&63|128).toString(16).toUpperCase()+"%"+(63&t|128).toString(16).toUpperCase()}function S(e){for(var t="",r=0,s=e.length;r<s;){var a=parseInt(e.substr(r+1,2),16);if(a<128)t+=String.fromCharCode(a),r+=3;else if(a>=194&&a<224){if(s-r>=6){var o=parseInt(e.substr(r+4,2),16);t+=String.fromCharCode((31&a)<<6|63&o)}else t+=e.substr(r,6);r+=6}else if(a>=224){if(s-r>=9){var n=parseInt(e.substr(r+4,2),16),i=parseInt(e.substr(r+7,2),16);t+=String.fromCharCode((15&a)<<12|(63&n)<<6|63&i)}else t+=e.substr(r,9);r+=9}else t+=e.substr(r,3),r+=3}return t}function k(e,t){function r(e){var r=S(e);return r.match(t.UNRESERVED)?r:e}return e.scheme&&(e.scheme=String(e.scheme).replace(t.PCT_ENCODED,r).toLowerCase().replace(t.NOT_SCHEME,"")),void 0!==e.userinfo&&(e.userinfo=String(e.userinfo).replace(t.PCT_ENCODED,r).replace(t.NOT_USERINFO,P).replace(t.PCT_ENCODED,a)),void 0!==e.host&&(e.host=String(e.host).replace(t.PCT_ENCODED,r).toLowerCase().replace(t.NOT_HOST,P).replace(t.PCT_ENCODED,a)),void 0!==e.path&&(e.path=String(e.path).replace(t.PCT_ENCODED,r).replace(e.scheme?t.NOT_PATH:t.NOT_PATH_NOSCHEME,P).replace(t.PCT_ENCODED,a)),void 0!==e.query&&(e.query=String(e.query).replace(t.PCT_ENCODED,r).replace(t.NOT_QUERY,P).replace(t.PCT_ENCODED,a)),void 0!==e.fragment&&(e.fragment=String(e.fragment).replace(t.PCT_ENCODED,r).replace(t.NOT_FRAGMENT,P).replace(t.PCT_ENCODED,a)),e}function N(e){return e.replace(/^0*(.*)/,"$1")||"0"}function j(e,t){var r=e.match(t.IPV4ADDRESS)||[],s=c(r,2)[1];return s?s.split(".").map(N).join("."):e}function C(e,t){var r=e.match(t.IPV6ADDRESS)||[],s=c(r,3),a=s[1],o=s[2];if(a){for(var n=a.toLowerCase().split("::").reverse(),i=c(n,2),l=i[0],d=i[1],u=d?d.split(":").map(N):[],f=l.split(":").map(N),h=t.IPV4ADDRESS.test(f[f.length-1]),p=h?7:8,m=f.length-p,y=Array(p),v=0;v<p;++v)y[v]=u[v]||f[m+v]||"";h&&(y[p-1]=j(y[p-1],t));var g=y.reduce(function(e,t,r){if(!t||"0"===t){var s=e[e.length-1];s&&s.index+s.length===r?s.length++:e.push({index:r,length:1})}return e},[]).sort(function(e,t){return t.length-e.length})[0],$=void 0;if(g&&g.length>1){var _=y.slice(0,g.index),w=y.slice(g.index+g.length);$=_.join(":")+"::"+w.join(":")}else $=y.join(":");return o&&($+="%"+o),$}return e}var O=/^(?:([^:\/?#]+):)?(?:\/\/((?:([^\/?#@]*)@)?(\[[^\/?#\]]+\]|[^\/?#:]*)(?:\:(\d*))?))?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n|\r)*))?/i,x=void 0==="".match(/(){0}/)[1];function T(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r={},s=!1!==t.iri?i:n;"suffix"===t.reference&&(e=(t.scheme?t.scheme+":":"")+"//"+e);var a=e.match(O);if(a){x?(r.scheme=a[1],r.userinfo=a[3],r.host=a[4],r.port=parseInt(a[5],10),r.path=a[6]||"",r.query=a[7],r.fragment=a[8],isNaN(r.port)&&(r.port=a[5])):(r.scheme=a[1]||void 0,r.userinfo=-1!==e.indexOf("@")?a[3]:void 0,r.host=-1!==e.indexOf("//")?a[4]:void 0,r.port=parseInt(a[5],10),r.path=a[6]||"",r.query=-1!==e.indexOf("?")?a[7]:void 0,r.fragment=-1!==e.indexOf("#")?a[8]:void 0,isNaN(r.port)&&(r.port=e.match(/\/\/(?:.|\n)*\:(?:\/|\?|\#|$)/)?a[4]:void 0)),r.host&&(r.host=C(j(r.host,s),s)),r.reference=void 0!==r.scheme||void 0!==r.userinfo||void 0!==r.host||void 0!==r.port||r.path||void 0!==r.query?void 0===r.scheme?"relative":void 0===r.fragment?"absolute":"uri":"same-document",t.reference&&"suffix"!==t.reference&&t.reference!==r.reference&&(r.error=r.error||"URI is not a "+t.reference+" reference.");var o=E[(t.scheme||r.scheme||"").toLowerCase()];if(t.unicodeSupport||o&&o.unicodeSupport)k(r,s);else{if(r.host&&(t.domainHost||o&&o.domainHost))try{r.host=w(r.host.replace(s.PCT_ENCODED,S).toLowerCase())}catch(e){r.error=r.error||"Host's domain name can not be converted to ASCII via punycode: "+e}k(r,n)}o&&o.parse&&o.parse(r,t)}else r.error=r.error||"URI can not be parsed.";return r}function R(e,t){var r=!1!==t.iri?i:n,s=[];return void 0!==e.userinfo&&(s.push(e.userinfo),s.push("@")),void 0!==e.host&&s.push(C(j(String(e.host),r),r).replace(r.IPV6ADDRESS,function(e,t,r){return"["+t+(r?"%25"+r:"")+"]"})),"number"!=typeof e.port&&"string"!=typeof e.port||(s.push(":"),s.push(String(e.port))),s.length?s.join(""):void 0}var I=/^\.\.?\//,D=/^\/\.(\/|$)/,A=/^\/\.\.(\/|$)/,M=/^\/?(?:.|\n)*?(?=\/|$)/;function V(e){for(var t=[];e.length;)if(e.match(I))e=e.replace(I,"");else if(e.match(D))e=e.replace(D,"/");else if(e.match(A))e=e.replace(A,"/"),t.pop();else if("."===e||".."===e)e="";else{var r=e.match(M);if(!r)throw new Error("Unexpected dot segment condition");var s=r[0];e=e.slice(s.length),t.push(s)}return t.join("")}function F(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.iri?i:n,s=[],a=E[(t.scheme||e.scheme||"").toLowerCase()];if(a&&a.serialize&&a.serialize(e,t),e.host)if(r.IPV6ADDRESS.test(e.host));else if(t.domainHost||a&&a.domainHost)try{e.host=t.iri?b(e.host):w(e.host.replace(r.PCT_ENCODED,S).toLowerCase())}catch(r){e.error=e.error||"Host's domain name can not be converted to "+(t.iri?"Unicode":"ASCII")+" via punycode: "+r}k(e,r),"suffix"!==t.reference&&e.scheme&&(s.push(e.scheme),s.push(":"));var o=R(e,t);if(void 0!==o&&("suffix"!==t.reference&&s.push("//"),s.push(o),e.path&&"/"!==e.path.charAt(0)&&s.push("/")),void 0!==e.path){var c=e.path;t.absolutePath||a&&a.absolutePath||(c=V(c)),void 0===o&&(c=c.replace(/^\/\//,"/%2F")),s.push(c)}return void 0!==e.query&&(s.push("?"),s.push(e.query)),void 0!==e.fragment&&(s.push("#"),s.push(e.fragment)),s.join("")}function U(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s={};return arguments[3]||(e=T(F(e,r),r),t=T(F(t,r),r)),!(r=r||{}).tolerant&&t.scheme?(s.scheme=t.scheme,s.userinfo=t.userinfo,s.host=t.host,s.port=t.port,s.path=V(t.path||""),s.query=t.query):(void 0!==t.userinfo||void 0!==t.host||void 0!==t.port?(s.userinfo=t.userinfo,s.host=t.host,s.port=t.port,s.path=V(t.path||""),s.query=t.query):(t.path?("/"===t.path.charAt(0)?s.path=V(t.path):(s.path=void 0===e.userinfo&&void 0===e.host&&void 0===e.port||e.path?e.path?e.path.slice(0,e.path.lastIndexOf("/")+1)+t.path:t.path:"/"+t.path,s.path=V(s.path)),s.query=t.query):(s.path=e.path,s.query=void 0!==t.query?t.query:e.query),s.userinfo=e.userinfo,s.host=e.host,s.port=e.port),s.scheme=e.scheme),s.fragment=t.fragment,s}function q(e,t){return e&&e.toString().replace(t&&t.iri?i.PCT_ENCODED:n.PCT_ENCODED,S)}var z={scheme:"http",domainHost:!0,parse:function(e,t){return e.host||(e.error=e.error||"HTTP URIs must have a host."),e},serialize:function(e,t){var r="https"===String(e.scheme).toLowerCase();return e.port!==(r?443:80)&&""!==e.port||(e.port=void 0),e.path||(e.path="/"),e}},K={scheme:"https",domainHost:z.domainHost,parse:z.parse,serialize:z.serialize};function L(e){return"boolean"==typeof e.secure?e.secure:"wss"===String(e.scheme).toLowerCase()}var H={scheme:"ws",domainHost:!0,parse:function(e,t){var r=e;return r.secure=L(r),r.resourceName=(r.path||"/")+(r.query?"?"+r.query:""),r.path=void 0,r.query=void 0,r},serialize:function(e,t){if(e.port!==(L(e)?443:80)&&""!==e.port||(e.port=void 0),"boolean"==typeof e.secure&&(e.scheme=e.secure?"wss":"ws",e.secure=void 0),e.resourceName){var r=e.resourceName.split("?"),s=c(r,2),a=s[0],o=s[1];e.path=a&&"/"!==a?a:void 0,e.query=o,e.resourceName=void 0}return e.fragment=void 0,e}},G={scheme:"wss",domainHost:H.domainHost,parse:H.parse,serialize:H.serialize},J={},B="[A-Za-z0-9\\-\\.\\_\\~\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]",W="[0-9A-Fa-f]",Q=r(r("%[EFef][0-9A-Fa-f]%"+W+W+"%"+W+W)+"|"+r("%[89A-Fa-f][0-9A-Fa-f]%"+W+W)+"|"+r("%"+W+W)),Z=t("[\\!\\$\\%\\'\\(\\)\\*\\+\\,\\-\\.0-9\\<\\>A-Z\\x5E-\\x7E]",'[\\"\\\\]'),Y=new RegExp(B,"g"),X=new RegExp(Q,"g"),ee=new RegExp(t("[^]","[A-Za-z0-9\\!\\$\\%\\'\\*\\+\\-\\^\\_\\`\\{\\|\\}\\~]","[\\.]",'[\\"]',Z),"g"),te=new RegExp(t("[^]",B,"[\\!\\$\\'\\(\\)\\*\\+\\,\\;\\:\\@]"),"g"),re=te;function se(e){var t=S(e);return t.match(Y)?t:e}var ae={scheme:"mailto",parse:function(e,t){var r=e,s=r.to=r.path?r.path.split(","):[];if(r.path=void 0,r.query){for(var a=!1,o={},n=r.query.split("&"),i=0,c=n.length;i<c;++i){var l=n[i].split("=");switch(l[0]){case"to":for(var d=l[1].split(","),u=0,f=d.length;u<f;++u)s.push(d[u]);break;case"subject":r.subject=q(l[1],t);break;case"body":r.body=q(l[1],t);break;default:a=!0,o[q(l[0],t)]=q(l[1],t)}}a&&(r.headers=o)}r.query=void 0;for(var h=0,p=s.length;h<p;++h){var m=s[h].split("@");if(m[0]=q(m[0]),t.unicodeSupport)m[1]=q(m[1],t).toLowerCase();else try{m[1]=w(q(m[1],t).toLowerCase())}catch(e){r.error=r.error||"Email address's domain name can not be converted to ASCII via punycode: "+e}s[h]=m.join("@")}return r},serialize:function(e,t){var r,s=e,o=null!=(r=e.to)?r instanceof Array?r:"number"!=typeof r.length||r.split||r.setInterval||r.call?[r]:Array.prototype.slice.call(r):[];if(o){for(var n=0,i=o.length;n<i;++n){var c=String(o[n]),l=c.lastIndexOf("@"),d=c.slice(0,l).replace(X,se).replace(X,a).replace(ee,P),u=c.slice(l+1);try{u=t.iri?b(u):w(q(u,t).toLowerCase())}catch(e){s.error=s.error||"Email address's domain name can not be converted to "+(t.iri?"Unicode":"ASCII")+" via punycode: "+e}o[n]=d+"@"+u}s.path=o.join(",")}var f=e.headers=e.headers||{};e.subject&&(f.subject=e.subject),e.body&&(f.body=e.body);var h=[];for(var p in f)f[p]!==J[p]&&h.push(p.replace(X,se).replace(X,a).replace(te,P)+"="+f[p].replace(X,se).replace(X,a).replace(re,P));return h.length&&(s.query=h.join("&")),s}},oe=/^([^\:]+)\:(.*)/,ne={scheme:"urn",parse:function(e,t){var r=e.path&&e.path.match(oe),s=e;if(r){var a=t.scheme||s.scheme||"urn",o=r[1].toLowerCase(),n=r[2],i=E[a+":"+(t.nid||o)];s.nid=o,s.nss=n,s.path=void 0,i&&(s=i.parse(s,t))}else s.error=s.error||"URN can not be parsed.";return s},serialize:function(e,t){var r=e.nid,s=E[(t.scheme||e.scheme||"urn")+":"+(t.nid||r)];s&&(e=s.serialize(e,t));var a=e;return a.path=(r||t.nid)+":"+e.nss,a}},ie=/^[0-9A-Fa-f]{8}(?:\-[0-9A-Fa-f]{4}){3}\-[0-9A-Fa-f]{12}$/,ce={scheme:"urn:uuid",parse:function(e,t){var r=e;return r.uuid=r.nss,r.nss=void 0,t.tolerant||r.uuid&&r.uuid.match(ie)||(r.error=r.error||"UUID is not valid."),r},serialize:function(e,t){var r=e;return r.nss=(e.uuid||"").toLowerCase(),r}};E[z.scheme]=z,E[K.scheme]=K,E[H.scheme]=H,E[G.scheme]=G,E[ae.scheme]=ae,E[ne.scheme]=ne,E[ce.scheme]=ce,e.SCHEMES=E,e.pctEncChar=P,e.pctDecChars=S,e.parse=T,e.removeDotSegments=V,e.serialize=F,e.resolveComponents=U,e.resolve=function(e,t,r){var s=function(e,t){var r={scheme:"null"};if(t)for(var s in t)r[s]=t[s];return r}(0,r);return F(U(T(e,s),T(t,s),s,!0),s)},e.normalize=function(e,t){return"string"==typeof e?e=F(T(e,t),t):"object"===s(e)&&(e=T(F(e,t),t)),e},e.equal=function(e,t,r){return"string"==typeof e?e=F(T(e,r),r):"object"===s(e)&&(e=F(e,r)),"string"==typeof t?t=F(T(t,r),r):"object"===s(t)&&(t=F(t,r)),e===t},e.escapeComponent=function(e,t){return e&&e.toString().replace(t&&t.iri?i.ESCAPE:n.ESCAPE,P)},e.unescapeComponent=q,Object.defineProperty(e,"__esModule",{value:!0})}(t)});x.code='require("ajv/dist/runtime/uri").default';var T=/*#__PURE__*/Object.defineProperty({default:x},"__esModule",{value:!0}),R=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0,Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return P.KeywordCxt}}),Object.defineProperty(t,"_",{enumerable:!0,get:function(){return c._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return c.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return c.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return c.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return c.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return c.CodeGen}});const r=c,s=(e,t)=>new RegExp(e,t);s.code="new RegExp";const a=["removeAdditional","useDefaults","coerceTypes"],o=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),n={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},i={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'};function d(e){var t,r,a,o,n,i,c,l,d,u,f,h,p,m,y,v,g,$,_,w,b,E,P,S,k;const N=e.strict,j=null===(t=e.code)||void 0===t?void 0:t.optimize,C=!0===j||void 0===j?1:j||0,O=null!==(a=null===(r=e.code)||void 0===r?void 0:r.regExp)&&void 0!==a?a:s,x=null!==(o=e.uriResolver)&&void 0!==o?o:T.default;return{strictSchema:null===(i=null!==(n=e.strictSchema)&&void 0!==n?n:N)||void 0===i||i,strictNumbers:null===(l=null!==(c=e.strictNumbers)&&void 0!==c?c:N)||void 0===l||l,strictTypes:null!==(u=null!==(d=e.strictTypes)&&void 0!==d?d:N)&&void 0!==u?u:"log",strictTuples:null!==(h=null!==(f=e.strictTuples)&&void 0!==f?f:N)&&void 0!==h?h:"log",strictRequired:null!==(m=null!==(p=e.strictRequired)&&void 0!==p?p:N)&&void 0!==m&&m,code:e.code?{...e.code,optimize:C,regExp:O}:{optimize:C,regExp:O},loopRequired:null!==(y=e.loopRequired)&&void 0!==y?y:200,loopEnum:null!==(v=e.loopEnum)&&void 0!==v?v:200,meta:null===(g=e.meta)||void 0===g||g,messages:null===($=e.messages)||void 0===$||$,inlineRefs:null===(_=e.inlineRefs)||void 0===_||_,schemaId:null!==(w=e.schemaId)&&void 0!==w?w:"$id",addUsedSchema:null===(b=e.addUsedSchema)||void 0===b||b,validateSchema:null===(E=e.validateSchema)||void 0===E||E,validateFormats:null===(P=e.validateFormats)||void 0===P||P,unicodeRegExp:null===(S=e.unicodeRegExp)||void 0===S||S,int32range:null===(k=e.int32range)||void 0===k||k,uriResolver:x}}class u{constructor(e={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,e=this.opts={...e,...d(e)};const{es5:t,lines:s}=this.opts.code;this.scope=new r.ValueScope({scope:{},prefixes:o,es5:t,lines:s}),this.logger=function(e){if(!1===e)return _;if(void 0===e)return console;if(e.log&&e.warn&&e.error)return e;throw new Error("logger must implement log, warn and error methods")}(e.logger);const a=e.validateFormats;e.validateFormats=!1,this.RULES=(0,p.getRules)(),f.call(this,n,e,"NOT SUPPORTED"),f.call(this,i,e,"DEPRECATED","warn"),this._metaOpts=$.call(this),e.formats&&v.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),e.keywords&&g.call(this,e.keywords),"object"==typeof e.meta&&this.addMetaSchema(e.meta),m.call(this),e.validateFormats=a}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){const{$data:e,meta:t,schemaId:r}=this.opts;let s=O;"id"===r&&(s={...O},s.id=s.$id,delete s.$id),t&&e&&this.addMetaSchema(s,s[r],!1)}defaultMeta(){const{meta:e,schemaId:t}=this.opts;return this.opts.defaultMeta="object"==typeof e?e[t]||e:void 0}validate(e,t){let r;if("string"==typeof e){if(r=this.getSchema(e),!r)throw new Error(`no schema with key or ref "${e}"`)}else r=this.compile(e);const s=r(t);return"$async"in r||(this.errors=r.errors),s}compile(e,t){const r=this._addSchema(e,t);return r.validate||this._compileSchemaEnv(r)}compileAsync(e,t){if("function"!=typeof this.opts.loadSchema)throw new Error("options.loadSchema should be a function");const{loadSchema:r}=this.opts;return s.call(this,e,t);async function s(e,t){await a.call(this,e.$schema);const r=this._addSchema(e,t);return r.validate||o.call(this,r)}async function a(e){e&&!this.getSchema(e)&&await s.call(this,{$ref:e},!0)}async function o(e){try{return this._compileSchemaEnv(e)}catch(t){if(!(t instanceof j.default))throw t;return n.call(this,t),await i.call(this,t.missingSchema),o.call(this,e)}}function n({missingSchema:e,missingRef:t}){if(this.refs[e])throw new Error(`AnySchema ${e} is loaded but ${t} cannot be resolved`)}async function i(e){const r=await c.call(this,e);this.refs[e]||await a.call(this,r.$schema),this.refs[e]||this.addSchema(r,e,t)}async function c(e){const t=this._loading[e];if(t)return t;try{return await(this._loading[e]=r(e))}finally{delete this._loading[e]}}}addSchema(e,t,r,s=this.opts.validateSchema){if(Array.isArray(e)){for(const t of e)this.addSchema(t,void 0,r,s);return this}let a;if("object"==typeof e){const{schemaId:t}=this.opts;if(a=e[t],void 0!==a&&"string"!=typeof a)throw new Error(`schema ${t} must be string`)}return t=(0,E.normalizeId)(t||a),this._checkUnique(t),this.schemas[t]=this._addSchema(e,r,t,s,!0),this}addMetaSchema(e,t,r=this.opts.validateSchema){return this.addSchema(e,t,!0,r),this}validateSchema(e,t){if("boolean"==typeof e)return!0;let r;if(r=e.$schema,void 0!==r&&"string"!=typeof r)throw new Error("$schema must be a string");if(r=r||this.opts.defaultMeta||this.defaultMeta(),!r)return this.logger.warn("meta-schema not available"),this.errors=null,!0;const s=this.validate(r,e);if(!s&&t){const e="schema is invalid: "+this.errorsText();if("log"!==this.opts.validateSchema)throw new Error(e);this.logger.error(e)}return s}getSchema(e){let t;for(;"string"==typeof(t=h.call(this,e));)e=t;if(void 0===t){const{schemaId:r}=this.opts,s=new C.SchemaEnv({schema:{},schemaId:r});if(t=C.resolveSchema.call(this,s,e),!t)return;this.refs[e]=t}return t.validate||this._compileSchemaEnv(t)}removeSchema(e){if(e instanceof RegExp)return this._removeAllSchemas(this.schemas,e),this._removeAllSchemas(this.refs,e),this;switch(typeof e){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{const t=h.call(this,e);return"object"==typeof t&&this._cache.delete(t.schema),delete this.schemas[e],delete this.refs[e],this}case"object":{this._cache.delete(e);let t=e[this.opts.schemaId];return t&&(t=(0,E.normalizeId)(t),delete this.schemas[t],delete this.refs[t]),this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(e){for(const t of e)this.addKeyword(t);return this}addKeyword(e,t){let r;if("string"==typeof e)r=e,"object"==typeof t&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),t.keyword=r);else{if("object"!=typeof e||void 0!==t)throw new Error("invalid addKeywords parameters");if(r=(t=e).keyword,Array.isArray(r)&&!r.length)throw new Error("addKeywords: keyword must be string or non-empty array")}if(b.call(this,r,t),!t)return(0,l.eachItem)(r,e=>S.call(this,e)),this;x.call(this,t);const s={...t,type:(0,y.getJSONTypes)(t.type),schemaType:(0,y.getJSONTypes)(t.schemaType)};return(0,l.eachItem)(r,0===s.type.length?e=>S.call(this,e,s):e=>s.type.forEach(t=>S.call(this,e,s,t))),this}getKeyword(e){const t=this.RULES.all[e];return"object"==typeof t?t.definition:!!t}removeKeyword(e){const{RULES:t}=this;delete t.keywords[e],delete t.all[e];for(const r of t.rules){const t=r.rules.findIndex(t=>t.keyword===e);t>=0&&r.rules.splice(t,1)}return this}addFormat(e,t){return"string"==typeof t&&(t=new RegExp(t)),this.formats[e]=t,this}errorsText(e=this.errors,{separator:t=", ",dataVar:r="data"}={}){return e&&0!==e.length?e.map(e=>`${r}${e.instancePath} ${e.message}`).reduce((e,r)=>e+t+r):"No errors"}$dataMetaSchema(e,t){const r=this.RULES.all;e=JSON.parse(JSON.stringify(e));for(const s of t){const t=s.split("/").slice(1);let a=e;for(const e of t)a=a[e];for(const e in r){const t=r[e];if("object"!=typeof t)continue;const{$data:s}=t.definition,o=a[e];s&&o&&(a[e]=I(o))}}return e}_removeAllSchemas(e,t){for(const r in e){const s=e[r];t&&!t.test(r)||("string"==typeof s?delete e[r]:s&&!s.meta&&(this._cache.delete(s.schema),delete e[r]))}}_addSchema(e,t,r,s=this.opts.validateSchema,a=this.opts.addUsedSchema){let o;const{schemaId:n}=this.opts;if("object"==typeof e)o=e[n];else{if(this.opts.jtd)throw new Error("schema must be object");if("boolean"!=typeof e)throw new Error("schema must be object or boolean")}let i=this._cache.get(e);if(void 0!==i)return i;r=(0,E.normalizeId)(o||r);const c=E.getSchemaRefs.call(this,e,r);return i=new C.SchemaEnv({schema:e,schemaId:n,meta:t,baseId:r,localRefs:c}),this._cache.set(i.schema,i),a&&!r.startsWith("#")&&(r&&this._checkUnique(r),this.refs[r]=i),s&&this.validateSchema(e,!0),i}_checkUnique(e){if(this.schemas[e]||this.refs[e])throw new Error(`schema with key or id "${e}" already exists`)}_compileSchemaEnv(e){if(e.meta?this._compileMetaSchema(e):C.compileSchema.call(this,e),!e.validate)throw new Error("ajv implementation error");return e.validate}_compileMetaSchema(e){const t=this.opts;this.opts=this._metaOpts;try{C.compileSchema.call(this,e)}finally{this.opts=t}}}function f(e,t,r,s="error"){for(const a in e){const o=a;o in t&&this.logger[s](`${r}: option ${a}. ${e[o]}`)}}function h(e){return e=(0,E.normalizeId)(e),this.schemas[e]||this.refs[e]}function m(){const e=this.opts.schemas;if(e)if(Array.isArray(e))this.addSchema(e);else for(const t in e)this.addSchema(e[t],t)}function v(){for(const e in this.opts.formats){const t=this.opts.formats[e];t&&this.addFormat(e,t)}}function g(e){if(Array.isArray(e))this.addVocabulary(e);else{this.logger.warn("keywords option as map is deprecated, pass array");for(const t in e){const r=e[t];r.keyword||(r.keyword=t),this.addKeyword(r)}}}function $(){const e={...this.opts};for(const t of a)delete e[t];return e}t.default=u,u.ValidationError=k.default,u.MissingRefError=j.default;const _={log(){},warn(){},error(){}},w=/^[a-z_$][a-z0-9_$:-]*$/i;function b(e,t){const{RULES:r}=this;if((0,l.eachItem)(e,e=>{if(r.keywords[e])throw new Error(`Keyword ${e} is already defined`);if(!w.test(e))throw new Error(`Keyword ${e} has invalid name`)}),t&&t.$data&&!("code"in t)&&!("validate"in t))throw new Error('$data keyword must have "code" or "validate" function')}function S(e,t,r){var s;const a=null==t?void 0:t.post;if(r&&a)throw new Error('keyword with "post" flag cannot have "type"');const{RULES:o}=this;let n=a?o.post:o.rules.find(({type:e})=>e===r);if(n||(n={type:r,rules:[]},o.rules.push(n)),o.keywords[e]=!0,!t)return;const i={keyword:e,definition:{...t,type:(0,y.getJSONTypes)(t.type),schemaType:(0,y.getJSONTypes)(t.schemaType)}};t.before?N.call(this,n,i,t.before):n.rules.push(i),o.all[e]=i,null===(s=t.implements)||void 0===s||s.forEach(e=>this.addKeyword(e))}function N(e,t,r){const s=e.rules.findIndex(e=>e.keyword===r);s>=0?e.rules.splice(s,0,t):(e.rules.push(t),this.logger.warn(`rule ${r} is not defined`))}function x(e){let{metaSchema:t}=e;void 0!==t&&(e.$data&&this.opts.$data&&(t=I(t)),e.validateSchema=this.compile(t,!0))}const R={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function I(e){return{anyOf:[e,R]}}}),I=/*#__PURE__*/Object.defineProperty({default:{keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}}},"__esModule",{value:!0}),D=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.callRef=t.getValidate=void 0;const r={keyword:"$ref",schemaType:"string",code(e){const{gen:t,schema:r,it:o}=e,{baseId:n,schemaEnv:i,validateName:l,opts:d,self:u}=o,{root:f}=i;if(("#"===r||"#/"===r)&&n===f.baseId)return function(){if(i===f)return a(e,l,i,i.$async);const r=t.scopeValue("root",{ref:f});return a(e,c._`${r}.validate`,f,f.$async)}();const h=C.resolveRef.call(u,f,n,r);if(void 0===h)throw new j.default(o.opts.uriResolver,n,r);return h instanceof C.SchemaEnv?function(t){const r=s(e,t);a(e,r,t,t.$async)}(h):function(s){const a=t.scopeValue("schema",!0===d.code.source?{ref:s,code:(0,c.stringify)(s)}:{ref:s}),o=t.name("valid"),n=e.subschema({schema:s,dataTypes:[],schemaPath:c.nil,topSchemaRef:a,errSchemaPath:r},o);e.mergeEvaluated(n),e.ok(o)}(h)}};function s(e,t){const{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):c._`${r.scopeValue("wrapper",{ref:t})}.validate`}function a(e,t,r,s){const{gen:a,it:o}=e,{allErrors:n,schemaEnv:i,opts:d}=o,f=d.passContext?u.default.this:c.nil;function h(e){const t=c._`${e}.errors`;a.assign(u.default.vErrors,c._`${u.default.vErrors} === null ? ${t} : ${u.default.vErrors}.concat(${t})`),a.assign(u.default.errors,c._`${u.default.vErrors}.length`)}function p(e){var t;if(!o.opts.unevaluated)return;const s=null===(t=null==r?void 0:r.validate)||void 0===t?void 0:t.evaluated;if(!0!==o.props)if(s&&!s.dynamicProps)void 0!==s.props&&(o.props=l.mergeEvaluated.props(a,s.props,o.props));else{const t=a.var("props",c._`${e}.evaluated.props`);o.props=l.mergeEvaluated.props(a,t,o.props,c.Name)}if(!0!==o.items)if(s&&!s.dynamicItems)void 0!==s.items&&(o.items=l.mergeEvaluated.items(a,s.items,o.items));else{const t=a.var("items",c._`${e}.evaluated.items`);o.items=l.mergeEvaluated.items(a,t,o.items,c.Name)}}s?function(){if(!i.$async)throw new Error("async schema referenced by sync schema");const r=a.let("valid");a.try(()=>{a.code(c._`await ${(0,g.callValidateCode)(e,t,f)}`),p(t),n||a.assign(r,!0)},e=>{a.if(c._`!(${e} instanceof ${o.ValidationError})`,()=>a.throw(e)),h(e),n||a.assign(r,!1)}),e.ok(r)}():e.result((0,g.callValidateCode)(e,t,f),()=>p(t),()=>h(t))}t.getValidate=s,t.callRef=a,t.default=r}),A=/*#__PURE__*/Object.defineProperty({default:["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",I.default,D.default]},"__esModule",{value:!0});const M=c.operators,V={maximum:{okStr:"<=",ok:M.LTE,fail:M.GT},minimum:{okStr:">=",ok:M.GTE,fail:M.LT},exclusiveMaximum:{okStr:"<",ok:M.LT,fail:M.GTE},exclusiveMinimum:{okStr:">",ok:M.GT,fail:M.LTE}},F={message:({keyword:e,schemaCode:t})=>c.str`must be ${V[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>c._`{comparison: ${V[e].okStr}, limit: ${t}}`},U={keyword:Object.keys(V),type:"number",schemaType:"number",$data:!0,error:F,code(e){const{keyword:t,data:r,schemaCode:s}=e;e.fail$data(c._`${r} ${V[t].fail} ${s} || isNaN(${r})`)}};var q=/*#__PURE__*/Object.defineProperty({default:U},"__esModule",{value:!0}),z=/*#__PURE__*/Object.defineProperty({default:{keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:{message:({schemaCode:e})=>c.str`must be multiple of ${e}`,params:({schemaCode:e})=>c._`{multipleOf: ${e}}`},code(e){const{gen:t,data:r,schemaCode:s,it:a}=e,o=a.opts.multipleOfPrecision,n=t.let("res"),i=o?c._`Math.abs(Math.round(${n}) - ${n}) > 1e-${o}`:c._`${n} !== parseInt(${n})`;e.fail$data(c._`(${s} === 0 || (${n} = ${r}/${s}, ${i}))`)}}},"__esModule",{value:!0});function K(e){const t=e.length;let r,s=0,a=0;for(;a<t;)s++,r=e.charCodeAt(a++),r>=55296&&r<=56319&&a<t&&(r=e.charCodeAt(a),56320==(64512&r)&&a++);return s}var L=K;K.code='require("ajv/dist/runtime/ucs2length").default';var H=/*#__PURE__*/Object.defineProperty({default:L},"__esModule",{value:!0}),G=/*#__PURE__*/Object.defineProperty({default:{keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:{message:({keyword:e,schemaCode:t})=>c.str`must NOT have ${"maxLength"===e?"more":"fewer"} than ${t} characters`,params:({schemaCode:e})=>c._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s,it:a}=e,o="maxLength"===t?c.operators.GT:c.operators.LT,n=!1===a.opts.unicode?c._`${r}.length`:c._`${(0,l.useFunc)(e.gen,H.default)}(${r})`;e.fail$data(c._`${n} ${o} ${s}`)}}},"__esModule",{value:!0}),J=/*#__PURE__*/Object.defineProperty({default:{keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>c.str`must match pattern "${e}"`,params:({schemaCode:e})=>c._`{pattern: ${e}}`},code(e){const{data:t,$data:r,schema:s,schemaCode:a,it:o}=e,n=r?c._`(new RegExp(${a}, ${o.opts.unicodeRegExp?"u":""}))`:(0,g.usePattern)(e,s);e.fail$data(c._`!${n}.test(${t})`)}}},"__esModule",{value:!0}),B=/*#__PURE__*/Object.defineProperty({default:{keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:{message:({keyword:e,schemaCode:t})=>c.str`must NOT have ${"maxProperties"===e?"more":"fewer"} than ${t} properties`,params:({schemaCode:e})=>c._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s}=e;e.fail$data(c._`Object.keys(${r}).length ${"maxProperties"===t?c.operators.GT:c.operators.LT} ${s}`)}}},"__esModule",{value:!0}),W=/*#__PURE__*/Object.defineProperty({default:{keyword:"required",type:"object",schemaType:"array",$data:!0,error:{message:({params:{missingProperty:e}})=>c.str`must have required property '${e}'`,params:({params:{missingProperty:e}})=>c._`{missingProperty: ${e}}`},code(e){const{gen:t,schema:r,schemaCode:s,data:a,$data:o,it:n}=e,{opts:i}=n;if(!o&&0===r.length)return;const d=r.length>=i.loopRequired;if(n.allErrors?function(){if(d||o)e.block$data(c.nil,u);else for(const t of r)(0,g.checkReportMissingProp)(e,t)}():function(){const n=t.let("missing");if(d||o){const r=t.let("valid",!0);e.block$data(r,()=>function(r,o){e.setParams({missingProperty:r}),t.forOf(r,s,()=>{t.assign(o,(0,g.propertyInData)(t,a,r,i.ownProperties)),t.if((0,c.not)(o),()=>{e.error(),t.break()})},c.nil)}(n,r)),e.ok(r)}else t.if((0,g.checkMissingProp)(e,r,n)),(0,g.reportMissingProp)(e,n),t.else()}(),i.strictRequired){const t=e.parentSchema.properties,{definedProperties:s}=e.it;for(const e of r)void 0!==(null==t?void 0:t[e])||s.has(e)||(0,l.checkStrictMode)(n,`required property "${e}" is not defined at "${n.schemaEnv.baseId+n.errSchemaPath}" (strictRequired)`,n.opts.strictRequired)}function u(){t.forOf("prop",s,r=>{e.setParams({missingProperty:r}),t.if((0,g.noPropertyInData)(t,a,r,i.ownProperties),()=>e.error())})}}}},"__esModule",{value:!0}),Q=/*#__PURE__*/Object.defineProperty({default:{keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:{message:({keyword:e,schemaCode:t})=>c.str`must NOT have ${"maxItems"===e?"more":"fewer"} than ${t} items`,params:({schemaCode:e})=>c._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s}=e;e.fail$data(c._`${r}.length ${"maxItems"===t?c.operators.GT:c.operators.LT} ${s}`)}}},"__esModule",{value:!0});w.code='require("ajv/dist/runtime/equal").default';var Z=/*#__PURE__*/Object.defineProperty({default:w},"__esModule",{value:!0}),Y=/*#__PURE__*/Object.defineProperty({default:[q.default,z.default,G.default,J.default,B.default,W.default,Q.default,/*#__PURE__*/Object.defineProperty({default:{keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:{message:({params:{i:e,j:t}})=>c.str`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>c._`{i: ${e}, j: ${t}}`},code(e){const{gen:t,data:r,$data:s,schema:a,parentSchema:o,schemaCode:n,it:i}=e;if(!s&&!a)return;const d=t.let("valid"),u=o.items?(0,y.getSchemaTypes)(o.items):[];function f(s,a){const o=t.name("item"),n=(0,y.checkDataTypes)(u,o,i.opts.strictNumbers,y.DataType.Wrong),l=t.const("indices",c._`{}`);t.for(c._`;${s}--;`,()=>{t.let(o,c._`${r}[${s}]`),t.if(n,c._`continue`),u.length>1&&t.if(c._`typeof ${o} == "string"`,c._`${o} += "_"`),t.if(c._`typeof ${l}[${o}] == "number"`,()=>{t.assign(a,c._`${l}[${o}]`),e.error(),t.assign(d,!1).break()}).code(c._`${l}[${o}] = ${s}`)})}function h(s,a){const o=(0,l.useFunc)(t,Z.default),n=t.name("outer");t.label(n).for(c._`;${s}--;`,()=>t.for(c._`${a} = ${s}; ${a}--;`,()=>t.if(c._`${o}(${r}[${s}], ${r}[${a}])`,()=>{e.error(),t.assign(d,!1).break(n)})))}e.block$data(d,function(){const s=t.let("i",c._`${r}.length`),a=t.let("j");e.setParams({i:s,j:a}),t.assign(d,!0),t.if(c._`${s} > 1`,()=>(u.length>0&&!u.some(e=>"object"===e||"array"===e)?f:h)(s,a))},c._`${n} === false`),e.ok(d)}}},"__esModule",{value:!0}).default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},/*#__PURE__*/Object.defineProperty({default:{keyword:"const",$data:!0,error:{message:"must be equal to constant",params:({schemaCode:e})=>c._`{allowedValue: ${e}}`},code(e){const{gen:t,data:r,$data:s,schemaCode:a,schema:o}=e;s||o&&"object"==typeof o?e.fail$data(c._`!${(0,l.useFunc)(t,Z.default)}(${r}, ${a})`):e.fail(c._`${o} !== ${r}`)}}},"__esModule",{value:!0}).default,/*#__PURE__*/Object.defineProperty({default:{keyword:"enum",schemaType:"array",$data:!0,error:{message:"must be equal to one of the allowed values",params:({schemaCode:e})=>c._`{allowedValues: ${e}}`},code(e){const{gen:t,data:r,$data:s,schema:a,schemaCode:o,it:n}=e;if(!s&&0===a.length)throw new Error("enum must have non-empty array");let i;const d=()=>null!=i?i:i=(0,l.useFunc)(t,Z.default);let u;if(a.length>=n.opts.loopEnum||s)u=t.let("valid"),e.block$data(u,function(){t.assign(u,!1),t.forOf("v",o,e=>t.if(c._`${d()}(${r}, ${e})`,()=>t.assign(u,!0).break()))});else{if(!Array.isArray(a))throw new Error("ajv implementation error");const e=t.const("vSchema",o);u=(0,c.or)(...a.map((t,s)=>function(e,t){const s=a[t];return"object"==typeof s&&null!==s?c._`${d()}(${r}, ${e}[${t}])`:c._`${r} === ${s}`}(e,s)))}e.pass(u)}}},"__esModule",{value:!0}).default]},"__esModule",{value:!0}),X=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.validateAdditionalItems=void 0;const r={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:{message:({params:{len:e}})=>c.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>c._`{limit: ${e}}`},code(e){const{parentSchema:t,it:r}=e,{items:a}=t;Array.isArray(a)?s(e,a):(0,l.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas')}};function s(e,t){const{gen:r,schema:s,data:a,keyword:o,it:n}=e;n.items=!0;const i=r.const("len",c._`${a}.length`);if(!1===s)e.setParams({len:t.length}),e.pass(c._`${i} <= ${t.length}`);else if("object"==typeof s&&!(0,l.alwaysValidSchema)(n,s)){const s=r.var("valid",c._`${i} <= ${t.length}`);r.if((0,c.not)(s),()=>function(s){r.forRange("i",t.length,i,t=>{e.subschema({keyword:o,dataProp:t,dataPropType:l.Type.Num},s),n.allErrors||r.if((0,c.not)(s),()=>r.break())})}(s)),e.ok(s)}}t.validateAdditionalItems=s,t.default=r}),ee=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.validateTuple=void 0;const r={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){const{schema:t,it:r}=e;if(Array.isArray(t))return s(e,"additionalItems",t);r.items=!0,(0,l.alwaysValidSchema)(r,t)||e.ok((0,g.validateArray)(e))}};function s(e,t,r=e.schema){const{gen:s,parentSchema:a,data:o,keyword:n,it:i}=e;!function(e){const{opts:s,errSchemaPath:a}=i,o=r.length;s.strictTuples&&(o!==e.minItems||o!==e.maxItems&&!1!==e[t])&&(0,l.checkStrictMode)(i,`"${n}" is ${o}-tuple, but minItems or maxItems/${t} are not specified or different at path "${a}"`,s.strictTuples)}(a),i.opts.unevaluated&&r.length&&!0!==i.items&&(i.items=l.mergeEvaluated.items(s,r.length,i.items));const d=s.name("valid"),u=s.const("len",c._`${o}.length`);r.forEach((t,r)=>{(0,l.alwaysValidSchema)(i,t)||(s.if(c._`${u} > ${r}`,()=>e.subschema({keyword:n,schemaProp:r,dataProp:r},d)),e.ok(d))})}t.validateTuple=s,t.default=r}),te=/*#__PURE__*/Object.defineProperty({default:{keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,ee.validateTuple)(e,"items")}},"__esModule",{value:!0}),re=/*#__PURE__*/Object.defineProperty({default:{keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:{message:({params:{len:e}})=>c.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>c._`{limit: ${e}}`},code(e){const{schema:t,parentSchema:r,it:s}=e,{prefixItems:a}=r;s.items=!0,(0,l.alwaysValidSchema)(s,t)||(a?(0,X.validateAdditionalItems)(e,a):e.ok((0,g.validateArray)(e)))}}},"__esModule",{value:!0}),se=/*#__PURE__*/Object.defineProperty({default:{keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:{message:({params:{min:e,max:t}})=>void 0===t?c.str`must contain at least ${e} valid item(s)`:c.str`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>void 0===t?c._`{minContains: ${e}}`:c._`{minContains: ${e}, maxContains: ${t}}`},code(e){const{gen:t,schema:r,parentSchema:s,data:a,it:o}=e;let n,i;const{minContains:d,maxContains:u}=s;o.opts.next?(n=void 0===d?1:d,i=u):n=1;const f=t.const("len",c._`${a}.length`);if(e.setParams({min:n,max:i}),void 0===i&&0===n)return void(0,l.checkStrictMode)(o,'"minContains" == 0 without "maxContains": "contains" keyword ignored');if(void 0!==i&&n>i)return(0,l.checkStrictMode)(o,'"minContains" > "maxContains" is always invalid'),void e.fail();if((0,l.alwaysValidSchema)(o,r)){let t=c._`${f} >= ${n}`;return void 0!==i&&(t=c._`${t} && ${f} <= ${i}`),void e.pass(t)}o.items=!0;const h=t.name("valid");function p(){const e=t.name("_valid"),r=t.let("count",0);m(e,()=>t.if(e,()=>function(e){t.code(c._`${e}++`),void 0===i?t.if(c._`${e} >= ${n}`,()=>t.assign(h,!0).break()):(t.if(c._`${e} > ${i}`,()=>t.assign(h,!1).break()),1===n?t.assign(h,!0):t.if(c._`${e} >= ${n}`,()=>t.assign(h,!0)))}(r)))}function m(r,s){t.forRange("i",0,f,t=>{e.subschema({keyword:"contains",dataProp:t,dataPropType:l.Type.Num,compositeRule:!0},r),s()})}void 0===i&&1===n?m(h,()=>t.if(h,()=>t.break())):0===n?(t.let(h,!0),void 0!==i&&t.if(c._`${a}.length > 0`,p)):(t.let(h,!1),p()),e.result(h,()=>e.reset())}}},"__esModule",{value:!0}),ae=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.validateSchemaDeps=t.validatePropertyDeps=t.error=void 0,t.error={message:({params:{property:e,depsCount:t,deps:r}})=>c.str`must have ${1===t?"property":"properties"} ${r} when property ${e} is present`,params:({params:{property:e,depsCount:t,deps:r,missingProperty:s}})=>c._`{property: ${e},
    missingProperty: ${s},
    depsCount: ${t},
    deps: ${r}}`};const r={keyword:"dependencies",type:"object",schemaType:"object",error:t.error,code(e){const[t,r]=function({schema:e}){const t={},r={};for(const s in e)"__proto__"!==s&&((Array.isArray(e[s])?t:r)[s]=e[s]);return[t,r]}(e);s(e,t),a(e,r)}};function s(e,t=e.schema){const{gen:r,data:s,it:a}=e;if(0===Object.keys(t).length)return;const o=r.let("missing");for(const n in t){const i=t[n];if(0===i.length)continue;const l=(0,g.propertyInData)(r,s,n,a.opts.ownProperties);e.setParams({property:n,depsCount:i.length,deps:i.join(", ")}),a.allErrors?r.if(l,()=>{for(const t of i)(0,g.checkReportMissingProp)(e,t)}):(r.if(c._`${l} && (${(0,g.checkMissingProp)(e,i,o)})`),(0,g.reportMissingProp)(e,o),r.else())}}function a(e,t=e.schema){const{gen:r,data:s,keyword:a,it:o}=e,n=r.name("valid");for(const i in t)(0,l.alwaysValidSchema)(o,t[i])||(r.if((0,g.propertyInData)(r,s,i,o.opts.ownProperties),()=>{const t=e.subschema({keyword:a,schemaProp:i},n);e.mergeValidEvaluated(t,n)},()=>r.var(n,!0)),e.ok(n))}t.validatePropertyDeps=s,t.validateSchemaDeps=a,t.default=r}),oe=/*#__PURE__*/Object.defineProperty({default:{keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:{message:"property name must be valid",params:({params:e})=>c._`{propertyName: ${e.propertyName}}`},code(e){const{gen:t,schema:r,data:s,it:a}=e;if((0,l.alwaysValidSchema)(a,r))return;const o=t.name("valid");t.forIn("key",s,r=>{e.setParams({propertyName:r}),e.subschema({keyword:"propertyNames",data:r,dataTypes:["string"],propertyName:r,compositeRule:!0},o),t.if((0,c.not)(o),()=>{e.error(!0),a.allErrors||t.break()})}),e.ok(o)}}},"__esModule",{value:!0});const ne={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:{message:"must NOT have additional properties",params:({params:e})=>c._`{additionalProperty: ${e.additionalProperty}}`},code(e){const{gen:t,schema:r,parentSchema:s,data:a,errsCount:o,it:n}=e;if(!o)throw new Error("ajv implementation error");const{allErrors:i,opts:d}=n;if(n.props=!0,"all"!==d.removeAdditional&&(0,l.alwaysValidSchema)(n,r))return;const f=(0,g.allSchemaProperties)(s.properties),h=(0,g.allSchemaProperties)(s.patternProperties);function p(e){t.code(c._`delete ${a}[${e}]`)}function m(s){if("all"===d.removeAdditional||d.removeAdditional&&!1===r)p(s);else{if(!1===r)return e.setParams({additionalProperty:s}),e.error(),void(i||t.break());if("object"==typeof r&&!(0,l.alwaysValidSchema)(n,r)){const r=t.name("valid");"failing"===d.removeAdditional?(y(s,r,!1),t.if((0,c.not)(r),()=>{e.reset(),p(s)})):(y(s,r),i||t.if((0,c.not)(r),()=>t.break()))}}}function y(t,r,s){const a={keyword:"additionalProperties",dataProp:t,dataPropType:l.Type.Str};!1===s&&Object.assign(a,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(a,r)}t.forIn("key",a,r=>{f.length||h.length?t.if(function(r){let a;if(f.length>8){const e=(0,l.schemaRefOrVal)(n,s.properties,"properties");a=(0,g.isOwnProperty)(t,e,r)}else a=f.length?(0,c.or)(...f.map(e=>c._`${r} === ${e}`)):c.nil;return h.length&&(a=(0,c.or)(a,...h.map(t=>c._`${(0,g.usePattern)(e,t)}.test(${r})`))),(0,c.not)(a)}(r),()=>m(r)):m(r)}),e.ok(c._`${o} === ${u.default.errors}`)}};var ie=/*#__PURE__*/Object.defineProperty({default:ne},"__esModule",{value:!0}),ce=/*#__PURE__*/Object.defineProperty({default:{keyword:"properties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,parentSchema:s,data:a,it:o}=e;"all"===o.opts.removeAdditional&&void 0===s.additionalProperties&&ie.default.code(new P.KeywordCxt(o,ie.default,"additionalProperties"));const n=(0,g.allSchemaProperties)(r);for(const e of n)o.definedProperties.add(e);o.opts.unevaluated&&n.length&&!0!==o.props&&(o.props=l.mergeEvaluated.props(t,(0,l.toHash)(n),o.props));const i=n.filter(e=>!(0,l.alwaysValidSchema)(o,r[e]));if(0===i.length)return;const c=t.name("valid");for(const r of i)d(r)?u(r):(t.if((0,g.propertyInData)(t,a,r,o.opts.ownProperties)),u(r),o.allErrors||t.else().var(c,!0),t.endIf()),e.it.definedProperties.add(r),e.ok(c);function d(e){return o.opts.useDefaults&&!o.compositeRule&&void 0!==r[e].default}function u(t){e.subschema({keyword:"properties",schemaProp:t,dataProp:t},c)}}}},"__esModule",{value:!0});const le=l;var de=/*#__PURE__*/Object.defineProperty({default:{keyword:"patternProperties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,data:s,parentSchema:a,it:o}=e,{opts:n}=o,i=(0,g.allSchemaProperties)(r),d=i.filter(e=>(0,l.alwaysValidSchema)(o,r[e]));if(0===i.length||d.length===i.length&&(!o.opts.unevaluated||!0===o.props))return;const u=n.strictSchema&&!n.allowMatchingProperties&&a.properties,f=t.name("valid");!0===o.props||o.props instanceof c.Name||(o.props=(0,le.evaluatedPropsToName)(t,o.props));const{props:h}=o;function p(e){for(const t in u)new RegExp(e).test(t)&&(0,l.checkStrictMode)(o,`property ${t} matches pattern ${e} (use allowMatchingProperties)`)}function m(r){t.forIn("key",s,s=>{t.if(c._`${(0,g.usePattern)(e,r)}.test(${s})`,()=>{const a=d.includes(r);a||e.subschema({keyword:"patternProperties",schemaProp:r,dataProp:s,dataPropType:le.Type.Str},f),o.opts.unevaluated&&!0!==h?t.assign(c._`${h}[${s}]`,!0):a||o.allErrors||t.if((0,c.not)(f),()=>t.break())})})}!function(){for(const e of i)u&&p(e),o.allErrors?m(e):(t.var(f,!0),m(e),t.if(f))}()}}},"__esModule",{value:!0}),ue=/*#__PURE__*/Object.defineProperty({default:{keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){const{gen:t,schema:r,it:s}=e;if((0,l.alwaysValidSchema)(s,r))return void e.fail();const a=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},a),e.failResult(a,()=>e.reset(),()=>e.error())},error:{message:"must NOT be valid"}}},"__esModule",{value:!0}),fe=/*#__PURE__*/Object.defineProperty({default:{keyword:"anyOf",schemaType:"array",trackErrors:!0,code:g.validateUnion,error:{message:"must match a schema in anyOf"}}},"__esModule",{value:!0}),he=/*#__PURE__*/Object.defineProperty({default:{keyword:"oneOf",schemaType:"array",trackErrors:!0,error:{message:"must match exactly one schema in oneOf",params:({params:e})=>c._`{passingSchemas: ${e.passing}}`},code(e){const{gen:t,schema:r,parentSchema:s,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(a.opts.discriminator&&s.discriminator)return;const o=r,n=t.let("valid",!1),i=t.let("passing",null),d=t.name("_valid");e.setParams({passing:i}),t.block(function(){o.forEach((r,s)=>{let o;(0,l.alwaysValidSchema)(a,r)?t.var(d,!0):o=e.subschema({keyword:"oneOf",schemaProp:s,compositeRule:!0},d),s>0&&t.if(c._`${d} && ${n}`).assign(n,!1).assign(i,c._`[${i}, ${s}]`).else(),t.if(d,()=>{t.assign(n,!0),t.assign(i,s),o&&e.mergeEvaluated(o,c.Name)})})}),e.result(n,()=>e.reset(),()=>e.error(!0))}}},"__esModule",{value:!0}),pe=/*#__PURE__*/Object.defineProperty({default:{keyword:"allOf",schemaType:"array",code(e){const{gen:t,schema:r,it:s}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");const a=t.name("valid");r.forEach((t,r)=>{if((0,l.alwaysValidSchema)(s,t))return;const o=e.subschema({keyword:"allOf",schemaProp:r},a);e.ok(a),e.mergeEvaluated(o)})}}},"__esModule",{value:!0});function me(e,t){const r=e.schema[t];return void 0!==r&&!(0,l.alwaysValidSchema)(e,r)}var ye=/*#__PURE__*/Object.defineProperty({default:{keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:{message:({params:e})=>c.str`must match "${e.ifClause}" schema`,params:({params:e})=>c._`{failingKeyword: ${e.ifClause}}`},code(e){const{gen:t,parentSchema:r,it:s}=e;void 0===r.then&&void 0===r.else&&(0,l.checkStrictMode)(s,'"if" without "then" and "else" is ignored');const a=me(s,"then"),o=me(s,"else");if(!a&&!o)return;const n=t.let("valid",!0),i=t.name("_valid");if(function(){const t=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},i);e.mergeEvaluated(t)}(),e.reset(),a&&o){const r=t.let("ifClause");e.setParams({ifClause:r}),t.if(i,d("then",r),d("else",r))}else a?t.if(i,d("then")):t.if((0,c.not)(i),d("else"));function d(r,s){return()=>{const a=e.subschema({keyword:r},i);t.assign(n,i),e.mergeValidEvaluated(a,n),s?t.assign(s,c._`${r}`):e.setParams({ifClause:r})}}e.pass(n,()=>e.error(!0))}}},"__esModule",{value:!0}),ve=/*#__PURE__*/Object.defineProperty({default:{keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){void 0===t.if&&(0,l.checkStrictMode)(r,`"${e}" without "if" is ignored`)}}},"__esModule",{value:!0}),ge=/*#__PURE__*/Object.defineProperty({default:function(e=!1){const t=[ue.default,fe.default,he.default,pe.default,ye.default,ve.default,oe.default,ie.default,ae.default,ce.default,de.default];return e?t.push(te.default,re.default):t.push(X.default,ee.default),t.push(se.default),t}},"__esModule",{value:!0}),$e=/*#__PURE__*/Object.defineProperty({default:[/*#__PURE__*/Object.defineProperty({default:{keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>c.str`must match format "${e}"`,params:({schemaCode:e})=>c._`{format: ${e}}`},code(e,t){const{gen:r,data:s,$data:a,schema:o,schemaCode:n,it:i}=e,{opts:l,errSchemaPath:d,schemaEnv:u,self:f}=i;l.validateFormats&&(a?function(){const a=r.scopeValue("formats",{ref:f.formats,code:l.code.formats}),o=r.const("fDef",c._`${a}[${n}]`),i=r.let("fType"),d=r.let("format");r.if(c._`typeof ${o} == "object" && !(${o} instanceof RegExp)`,()=>r.assign(i,c._`${o}.type || "string"`).assign(d,c._`${o}.validate`),()=>r.assign(i,c._`"string"`).assign(d,o)),e.fail$data((0,c.or)(!1===l.strictSchema?c.nil:c._`${n} && !${d}`,function(){const e=u.$async?c._`(${o}.async ? await ${d}(${s}) : ${d}(${s}))`:c._`${d}(${s})`,r=c._`(typeof ${d} == "function" ? ${e} : ${d}.test(${s}))`;return c._`${d} && ${d} !== true && ${i} === ${t} && !${r}`}()))}():function(){const a=f.formats[o];if(!a)return void function(){if(!1!==l.strictSchema)throw new Error(e());function e(){return`unknown format "${o}" ignored in schema at path "${d}"`}f.logger.warn(e())}();if(!0===a)return;const[n,i,h]=function(e){const t=e instanceof RegExp?(0,c.regexpCode)(e):l.code.formats?c._`${l.code.formats}${(0,c.getProperty)(o)}`:void 0,s=r.scopeValue("formats",{key:o,ref:e,code:t});return"object"!=typeof e||e instanceof RegExp?["string",e,s]:[e.type||"string",e.validate,c._`${s}.validate`]}(a);n===t&&e.pass(function(){if("object"==typeof a&&!(a instanceof RegExp)&&a.async){if(!u.$async)throw new Error("async format in sync schema");return c._`await ${h}(${s})`}return"function"==typeof i?c._`${h}(${s})`:c._`${h}.test(${s})`}())}())}}},"__esModule",{value:!0}).default]},"__esModule",{value:!0}),_e=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.contentVocabulary=t.metadataVocabulary=void 0,t.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"],t.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"]});const we=[A.default,Y.default,(0,ge.default)(),$e.default,_e.metadataVocabulary,_e.contentVocabulary];var be=/*#__PURE__*/Object.defineProperty({default:we},"__esModule",{value:!0}),Ee=o(function(e,t){var r;Object.defineProperty(t,"__esModule",{value:!0}),t.DiscrError=void 0,(r=t.DiscrError||(t.DiscrError={})).Tag="tag",r.Mapping="mapping"}),Pe=/*#__PURE__*/Object.defineProperty({default:{keyword:"discriminator",type:"object",schemaType:"object",error:{message:({params:{discrError:e,tagName:t}})=>e===Ee.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>c._`{error: ${e}, tag: ${r}, tagValue: ${t}}`},code(e){const{gen:t,data:r,schema:s,parentSchema:a,it:o}=e,{oneOf:n}=a;if(!o.opts.discriminator)throw new Error("discriminator: requires discriminator option");const i=s.propertyName;if("string"!=typeof i)throw new Error("discriminator: requires propertyName");if(s.mapping)throw new Error("discriminator: mapping is not supported");if(!n)throw new Error("discriminator: requires oneOf keyword");const d=t.let("valid",!1),u=t.const("tag",c._`${r}${(0,c.getProperty)(i)}`);function f(r){const s=t.name("valid"),a=e.subschema({keyword:"oneOf",schemaProp:r},s);return e.mergeEvaluated(a,c.Name),s}t.if(c._`typeof ${u} == "string"`,()=>function(){const r=function(){var e;const t={},r=c(a);let s=!0;for(let t=0;t<n.length;t++){let a=n[t];(null==a?void 0:a.$ref)&&!(0,l.schemaHasRulesButRef)(a,o.self.RULES)&&(a=C.resolveRef.call(o.self,o.schemaEnv.root,o.baseId,null==a?void 0:a.$ref),a instanceof C.SchemaEnv&&(a=a.schema));const u=null===(e=null==a?void 0:a.properties)||void 0===e?void 0:e[i];if("object"!=typeof u)throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${i}"`);s=s&&(r||c(a)),d(u,t)}if(!s)throw new Error(`discriminator: "${i}" must be required`);return t;function c({required:e}){return Array.isArray(e)&&e.includes(i)}function d(e,t){if(e.const)u(e.const,t);else{if(!e.enum)throw new Error(`discriminator: "properties/${i}" must have "const" or "enum"`);for(const r of e.enum)u(r,t)}}function u(e,r){if("string"!=typeof e||e in t)throw new Error(`discriminator: "${i}" values must be unique strings`);t[e]=r}}();t.if(!1);for(const e in r)t.elseIf(c._`${u} === ${e}`),t.assign(d,f(r[e]));t.else(),e.error(!1,{discrError:Ee.DiscrError.Mapping,tag:u,tagName:i}),t.endIf()}(),()=>e.error(!1,{discrError:Ee.DiscrError.Tag,tag:u,tagName:i})),e.ok(d)}}},"__esModule",{value:!0}),Se={$schema:"http://json-schema.org/draft-07/schema#",$id:"http://json-schema.org/draft-07/schema#",title:"Core schema meta-schema",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0},ke=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;const r=["/properties"],s="http://json-schema.org/draft-07/schema";class a extends R.default{_addVocabularies(){super._addVocabularies(),be.default.forEach(e=>this.addVocabulary(e)),this.opts.discriminator&&this.addKeyword(Pe.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;const e=this.opts.$data?this.$dataMetaSchema(Se,r):Se;this.addMetaSchema(e,s,!1),this.refs["http://json-schema.org/schema"]=s}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(s)?s:void 0)}}e.exports=t=a,Object.defineProperty(t,"__esModule",{value:!0}),t.default=a,Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return P.KeywordCxt}}),Object.defineProperty(t,"_",{enumerable:!0,get:function(){return c._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return c.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return c.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return c.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return c.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return c.CodeGen}})}),Ne=/*@__PURE__*/a(ke),je=/*@__PURE__*/a(o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const r="errorMessage",s=new ke.Name("emUsed"),a={required:"missingProperty",dependencies:"property",dependentRequired:"property"},o=/\$\{[^}]+\}/,i=/\$\{([^}]+)\}/g,l=/^""\s*\+\s*|\s*\+\s*""$/g;function d(e){return{keyword:r,schemaType:["string","object"],post:!0,code(t){const{gen:d,data:h,schema:p,schemaValue:m,it:y}=t;if(!1===y.createErrors)return;const v=p,g=c.strConcat(u.default.instancePath,y.errorPath);function $(e,t){return c.and(ke._`${e}.keyword !== ${r}`,ke._`!${e}.${s}`,ke._`${e}.instancePath === ${g}`,ke._`${e}.keyword in ${t}`,ke._`${e}.schemaPath.indexOf(${y.errSchemaPath}) === 0`,ke._`/^\\/[^\\/]*$/.test(${e}.schemaPath.slice(${y.errSchemaPath.length}))`)}function _(e,t){const r=[];for(const s in e){const e=t[s];o.test(e)&&r.push([s,b(e)])}return d.object(...r)}function w(e){return o.test(e)?new n._Code(n.safeStringify(e).replace(i,(e,t)=>`" + JSON.stringify(${P.getData(t,y)}) + "`).replace(l,"")):ke.stringify(e)}function b(e){return ke._`function(){return ${w(e)}}`}d.if(ke._`${u.default.errors} > 0`,()=>{if("object"==typeof v){const[o,n]=function(e){let t,r;for(const s in e){if("properties"===s||"items"===s)continue;const a=e[s];if("object"==typeof a){t||(t={});const e=t[s]={};for(const t in a)e[t]=[]}else r||(r={}),r[s]=[]}return[t,r]}(v);n&&function(r){const a=d.const("emErrors",ke.stringify(r)),o=d.const("templates",_(r,p));d.forOf("err",u.default.vErrors,e=>d.if($(e,a),()=>d.code(ke._`${a}[${e}.keyword].push(${e})`).assign(ke._`${e}.${s}`,!0)));const{singleError:n}=e;if(n){const e=d.let("message",ke._`""`),r=d.let("paramsErrors",ke._`[]`);i(t=>{d.if(e,()=>d.code(ke._`${e} += ${"string"==typeof n?n:";"}`)),d.code(ke._`${e} += ${c(t)}`),d.assign(r,ke._`${r}.concat(${a}[${t}])`)}),f.reportError(t,{message:e,params:ke._`{errors: ${r}}`})}else i(e=>f.reportError(t,{message:c(e),params:ke._`{errors: ${a}[${e}]}`}));function i(e){d.forIn("key",a,t=>d.if(ke._`${a}[${t}].length`,()=>e(t)))}function c(e){return ke._`${e} in ${o} ? ${o}[${e}]() : ${m}[${e}]`}}(n),o&&function(e){const r=d.const("emErrors",ke.stringify(e)),o=[];for(const t in e)o.push([t,_(e[t],p[t])]);const n=d.const("templates",d.object(...o)),i=d.scopeValue("obj",{ref:a,code:ke.stringify(a)}),c=d.let("emPropParams"),l=d.let("emParamsErrors");d.forOf("err",u.default.vErrors,e=>d.if($(e,r),()=>{d.assign(c,ke._`${i}[${e}.keyword]`),d.assign(l,ke._`${r}[${e}.keyword][${e}.params[${c}]]`),d.if(l,()=>d.code(ke._`${l}.push(${e})`).assign(ke._`${e}.${s}`,!0))})),d.forIn("key",r,e=>d.forIn("keyProp",ke._`${r}[${e}]`,s=>{d.assign(l,ke._`${r}[${e}][${s}]`),d.if(ke._`${l}.length`,()=>{const r=d.const("tmpl",ke._`${n}[${e}] && ${n}[${e}][${s}]`);f.reportError(t,{message:ke._`${r} ? ${r}() : ${m}[${e}][${s}]`,params:ke._`{errors: ${l}}`})})}))}(o),function(e){const{props:a,items:o}=e;if(!a&&!o)return;const n=ke._`typeof ${h} == "object"`,i=ke._`Array.isArray(${h})`,l=d.let("emErrors");let y,v;const $=d.let("templates");function w(e,t){d.assign(l,ke.stringify(e)),d.assign($,_(e,t))}a&&o?(y=d.let("emChildKwd"),d.if(n),d.if(i,()=>{w(o,p.items),d.assign(y,ke.str`items`)},()=>{w(a,p.properties),d.assign(y,ke.str`properties`)}),v=ke._`[${y}]`):o?(d.if(i),w(o,p.items),v=ke._`.items`):a&&(d.if(c.and(n,c.not(i))),w(a,p.properties),v=ke._`.properties`),d.forOf("err",u.default.vErrors,e=>function(e,t,a){d.if(c.and(ke._`${e}.keyword !== ${r}`,ke._`!${e}.${s}`,ke._`${e}.instancePath.indexOf(${g}) === 0`),()=>{const r=d.scopeValue("pattern",{ref:/^\/([^/]*)(?:\/|$)/,code:ke._`new RegExp("^\\\/([^/]*)(?:\\\/|$)")`}),s=d.const("emMatches",ke._`${r}.exec(${e}.instancePath.slice(${g}.length))`),o=d.const("emChild",ke._`${s} && ${s}[1].replace(/~1/g, "/").replace(/~0/g, "~")`);d.if(ke._`${o} !== undefined && ${o} in ${t}`,()=>a(o))})}(e,l,t=>d.code(ke._`${l}[${t}].push(${e})`).assign(ke._`${e}.${s}`,!0))),d.forIn("key",l,e=>d.if(ke._`${l}[${e}].length`,()=>{f.reportError(t,{message:ke._`${e} in ${$} ? ${$}[${e}]() : ${m}${v}[${e}]`,params:ke._`{errors: ${l}[${e}]}`}),d.assign(ke._`${u.default.vErrors}[${u.default.errors}-1].instancePath`,ke._`${g} + "/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`)})),d.endIf()}(function({properties:e,items:t}){const r={};if(e){r.props={};for(const t in e)r.props[t]=[]}if(t){r.items={};for(let e=0;e<t.length;e++)r.items[e]=[]}return r}(v))}const o="string"==typeof v?v:v._;o&&function(e){const a=d.const("emErrs",ke._`[]`);d.forOf("err",u.default.vErrors,e=>d.if(function(e){return c.and(ke._`${e}.keyword !== ${r}`,ke._`!${e}.${s}`,c.or(ke._`${e}.instancePath === ${g}`,c.and(ke._`${e}.instancePath.indexOf(${g}) === 0`,ke._`${e}.instancePath[${g}.length] === "/"`)),ke._`${e}.schemaPath.indexOf(${y.errSchemaPath}) === 0`,ke._`${e}.schemaPath[${y.errSchemaPath}.length] === "/"`)}(e),()=>d.code(ke._`${a}.push(${e})`).assign(ke._`${e}.${s}`,!0))),d.if(ke._`${a}.length`,()=>f.reportError(t,{message:w(e),params:ke._`{errors: ${a}}`}))}(o),e.keepErrors||function(){const e=d.const("emErrs",ke._`[]`);d.forOf("err",u.default.vErrors,t=>d.if(ke._`!${t}.${s}`,()=>d.code(ke._`${e}.push(${t})`))),d.assign(u.default.vErrors,e).assign(u.default.errors,ke._`${e}.length`)}()})},metaSchema:{anyOf:[{type:"string"},{type:"object",properties:{properties:{$ref:"#/$defs/stringMap"},items:{$ref:"#/$defs/stringList"},required:{$ref:"#/$defs/stringOrMap"},dependencies:{$ref:"#/$defs/stringOrMap"}},additionalProperties:{type:"string"}}],$defs:{stringMap:{type:"object",additionalProperties:{type:"string"}},stringOrMap:{anyOf:[{type:"string"},{$ref:"#/$defs/stringMap"}]},stringList:{type:"array",items:{type:"string"}}}}}}const h=(e,t={})=>{if(!e.opts.allErrors)throw new Error("ajv-errors: Ajv option allErrors must be true");if(e.opts.jsPropertySyntax)throw new Error("ajv-errors: ajv option jsPropertySyntax is not supported");return e.addKeyword(d(t))};t.default=h,e.exports=h,e.exports.default=h})),Ce=function(e,t){return e.forEach(function(e){"required"===e.keyword&&(e.instancePath+="/"+e.params.missingProperty)}),e.reduce(function(e,s){var a=s.instancePath.substring(1).replace(/\//g,".");if(e[a]||(e[a]={message:s.message,type:s.keyword}),t){var o=e[a].types,n=o&&o[s.keyword];e[a]=r(a,t,e,s.keyword,n?[].concat(n,s.message||""):s.message)}return e},{})},Oe=function(r,a,o){return void 0===o&&(o={}),function(n,i,c){try{var l,d=new Ne(s({allErrors:!0,validateSchema:!0},a));je(d);var u=d.compile(Object.assign({$async:"async"===(null==(l=o)?void 0:l.mode)},r));return u(n)?(c.shouldUseNativeValidation&&t({},c),Promise.resolve({values:n,errors:{}})):Promise.resolve({values:{},errors:e(Ce(u.errors,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)})}catch(e){return Promise.reject(e)}}};export{Oe as ajvResolver};
//# sourceMappingURL=ajv.module.js.map
