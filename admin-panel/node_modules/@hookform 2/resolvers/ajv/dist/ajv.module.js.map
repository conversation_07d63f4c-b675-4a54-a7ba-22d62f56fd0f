{"version": 3, "file": "ajv.module.js", "sources": ["../../node_modules/ajv/dist/compile/codegen/code.js", "../../node_modules/ajv/dist/compile/codegen/scope.js", "../../node_modules/ajv/dist/compile/codegen/index.js", "../../node_modules/ajv/dist/compile/util.js", "../../node_modules/ajv/dist/compile/names.js", "../../node_modules/ajv/dist/compile/errors.js", "../../node_modules/ajv/dist/compile/validate/boolSchema.js", "../../node_modules/ajv/dist/compile/rules.js", "../../node_modules/ajv/dist/compile/validate/applicability.js", "../../node_modules/ajv/dist/compile/validate/dataType.js", "../../node_modules/ajv/dist/compile/validate/defaults.js", "../../node_modules/ajv/dist/vocabularies/code.js", "../../node_modules/ajv/dist/compile/validate/keyword.js", "../../node_modules/ajv/dist/compile/validate/subschema.js", "../../node_modules/fast-deep-equal/index.js", "../../node_modules/json-schema-traverse/index.js", "../../node_modules/ajv/dist/compile/resolve.js", "../../node_modules/ajv/dist/compile/validate/index.js", "../../node_modules/ajv/dist/runtime/validation_error.js", "../../node_modules/ajv/dist/compile/ref_error.js", "../../node_modules/ajv/dist/compile/index.js", "../../node_modules/uri-js/dist/es5/uri.all.js", "../../node_modules/ajv/dist/runtime/uri.js", "../../node_modules/ajv/dist/core.js", "../../node_modules/ajv/dist/vocabularies/core/id.js", "../../node_modules/ajv/dist/vocabularies/core/ref.js", "../../node_modules/ajv/dist/vocabularies/core/index.js", "../../node_modules/ajv/dist/vocabularies/validation/limitNumber.js", "../../node_modules/ajv/dist/vocabularies/validation/multipleOf.js", "../../node_modules/ajv/dist/runtime/ucs2length.js", "../../node_modules/ajv/dist/vocabularies/validation/limitLength.js", "../../node_modules/ajv/dist/vocabularies/validation/pattern.js", "../../node_modules/ajv/dist/vocabularies/validation/limitProperties.js", "../../node_modules/ajv/dist/vocabularies/validation/required.js", "../../node_modules/ajv/dist/vocabularies/validation/limitItems.js", "../../node_modules/ajv/dist/runtime/equal.js", "../../node_modules/ajv/dist/vocabularies/validation/index.js", "../../node_modules/ajv/dist/vocabularies/validation/uniqueItems.js", "../../node_modules/ajv/dist/vocabularies/validation/const.js", "../../node_modules/ajv/dist/vocabularies/validation/enum.js", "../../node_modules/ajv/dist/vocabularies/applicator/additionalItems.js", "../../node_modules/ajv/dist/vocabularies/applicator/items.js", "../../node_modules/ajv/dist/vocabularies/applicator/prefixItems.js", "../../node_modules/ajv/dist/vocabularies/applicator/items2020.js", "../../node_modules/ajv/dist/vocabularies/applicator/contains.js", "../../node_modules/ajv/dist/vocabularies/applicator/dependencies.js", "../../node_modules/ajv/dist/vocabularies/applicator/propertyNames.js", "../../node_modules/ajv/dist/vocabularies/applicator/additionalProperties.js", "../../node_modules/ajv/dist/vocabularies/applicator/properties.js", "../../node_modules/ajv/dist/vocabularies/applicator/patternProperties.js", "../../node_modules/ajv/dist/vocabularies/applicator/not.js", "../../node_modules/ajv/dist/vocabularies/applicator/anyOf.js", "../../node_modules/ajv/dist/vocabularies/applicator/oneOf.js", "../../node_modules/ajv/dist/vocabularies/applicator/allOf.js", "../../node_modules/ajv/dist/vocabularies/applicator/if.js", "../../node_modules/ajv/dist/vocabularies/applicator/thenElse.js", "../../node_modules/ajv/dist/vocabularies/applicator/index.js", "../../node_modules/ajv/dist/vocabularies/format/index.js", "../../node_modules/ajv/dist/vocabularies/format/format.js", "../../node_modules/ajv/dist/vocabularies/metadata.js", "../../node_modules/ajv/dist/vocabularies/draft7.js", "../../node_modules/ajv/dist/vocabularies/discriminator/types.js", "../../node_modules/ajv/dist/vocabularies/discriminator/index.js", "../../node_modules/ajv/dist/ajv.js", "../../node_modules/ajv-errors/dist/index.js", "../src/ajv.ts"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.regexpCode = exports.getEsmExportName = exports.getProperty = exports.safeStringify = exports.stringify = exports.strConcat = exports.addCodeArg = exports.str = exports._ = exports.nil = exports._Code = exports.Name = exports.IDENTIFIER = exports._CodeOrName = void 0;\nclass _CodeOrName {\n}\nexports._CodeOrName = _CodeOrName;\nexports.IDENTIFIER = /^[a-z$_][a-z$_0-9]*$/i;\nclass Name extends _CodeOrName {\n    constructor(s) {\n        super();\n        if (!exports.IDENTIFIER.test(s))\n            throw new Error(\"CodeGen: name must be a valid identifier\");\n        this.str = s;\n    }\n    toString() {\n        return this.str;\n    }\n    emptyStr() {\n        return false;\n    }\n    get names() {\n        return { [this.str]: 1 };\n    }\n}\nexports.Name = Name;\nclass _Code extends _CodeOrName {\n    constructor(code) {\n        super();\n        this._items = typeof code === \"string\" ? [code] : code;\n    }\n    toString() {\n        return this.str;\n    }\n    emptyStr() {\n        if (this._items.length > 1)\n            return false;\n        const item = this._items[0];\n        return item === \"\" || item === '\"\"';\n    }\n    get str() {\n        var _a;\n        return ((_a = this._str) !== null && _a !== void 0 ? _a : (this._str = this._items.reduce((s, c) => `${s}${c}`, \"\")));\n    }\n    get names() {\n        var _a;\n        return ((_a = this._names) !== null && _a !== void 0 ? _a : (this._names = this._items.reduce((names, c) => {\n            if (c instanceof Name)\n                names[c.str] = (names[c.str] || 0) + 1;\n            return names;\n        }, {})));\n    }\n}\nexports._Code = _Code;\nexports.nil = new _Code(\"\");\nfunction _(strs, ...args) {\n    const code = [strs[0]];\n    let i = 0;\n    while (i < args.length) {\n        addCodeArg(code, args[i]);\n        code.push(strs[++i]);\n    }\n    return new _Code(code);\n}\nexports._ = _;\nconst plus = new _Code(\"+\");\nfunction str(strs, ...args) {\n    const expr = [safeStringify(strs[0])];\n    let i = 0;\n    while (i < args.length) {\n        expr.push(plus);\n        addCodeArg(expr, args[i]);\n        expr.push(plus, safeStringify(strs[++i]));\n    }\n    optimize(expr);\n    return new _Code(expr);\n}\nexports.str = str;\nfunction addCodeArg(code, arg) {\n    if (arg instanceof _Code)\n        code.push(...arg._items);\n    else if (arg instanceof Name)\n        code.push(arg);\n    else\n        code.push(interpolate(arg));\n}\nexports.addCodeArg = addCodeArg;\nfunction optimize(expr) {\n    let i = 1;\n    while (i < expr.length - 1) {\n        if (expr[i] === plus) {\n            const res = mergeExprItems(expr[i - 1], expr[i + 1]);\n            if (res !== undefined) {\n                expr.splice(i - 1, 3, res);\n                continue;\n            }\n            expr[i++] = \"+\";\n        }\n        i++;\n    }\n}\nfunction mergeExprItems(a, b) {\n    if (b === '\"\"')\n        return a;\n    if (a === '\"\"')\n        return b;\n    if (typeof a == \"string\") {\n        if (b instanceof Name || a[a.length - 1] !== '\"')\n            return;\n        if (typeof b != \"string\")\n            return `${a.slice(0, -1)}${b}\"`;\n        if (b[0] === '\"')\n            return a.slice(0, -1) + b.slice(1);\n        return;\n    }\n    if (typeof b == \"string\" && b[0] === '\"' && !(a instanceof Name))\n        return `\"${a}${b.slice(1)}`;\n    return;\n}\nfunction strConcat(c1, c2) {\n    return c2.emptyStr() ? c1 : c1.emptyStr() ? c2 : str `${c1}${c2}`;\n}\nexports.strConcat = strConcat;\n// TODO do not allow arrays here\nfunction interpolate(x) {\n    return typeof x == \"number\" || typeof x == \"boolean\" || x === null\n        ? x\n        : safeStringify(Array.isArray(x) ? x.join(\",\") : x);\n}\nfunction stringify(x) {\n    return new _Code(safeStringify(x));\n}\nexports.stringify = stringify;\nfunction safeStringify(x) {\n    return JSON.stringify(x)\n        .replace(/\\u2028/g, \"\\\\u2028\")\n        .replace(/\\u2029/g, \"\\\\u2029\");\n}\nexports.safeStringify = safeStringify;\nfunction getProperty(key) {\n    return typeof key == \"string\" && exports.IDENTIFIER.test(key) ? new _Code(`.${key}`) : _ `[${key}]`;\n}\nexports.getProperty = getProperty;\n//Does best effort to format the name properly\nfunction getEsmExportName(key) {\n    if (typeof key == \"string\" && exports.IDENTIFIER.test(key)) {\n        return new _Code(`${key}`);\n    }\n    throw new Error(`CodeGen: invalid export name: ${key}, use explicit $id name mapping`);\n}\nexports.getEsmExportName = getEsmExportName;\nfunction regexpCode(rx) {\n    return new _Code(rx.toString());\n}\nexports.regexpCode = regexpCode;\n//# sourceMappingURL=code.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ValueScope = exports.ValueScopeName = exports.Scope = exports.varKinds = exports.UsedValueState = void 0;\nconst code_1 = require(\"./code\");\nclass ValueError extends Error {\n    constructor(name) {\n        super(`CodeGen: \"code\" for ${name} not defined`);\n        this.value = name.value;\n    }\n}\nvar UsedValueState;\n(function (UsedValueState) {\n    UsedValueState[UsedValueState[\"Started\"] = 0] = \"Started\";\n    UsedValueState[UsedValueState[\"Completed\"] = 1] = \"Completed\";\n})(UsedValueState = exports.UsedValueState || (exports.UsedValueState = {}));\nexports.varKinds = {\n    const: new code_1.Name(\"const\"),\n    let: new code_1.Name(\"let\"),\n    var: new code_1.Name(\"var\"),\n};\nclass Scope {\n    constructor({ prefixes, parent } = {}) {\n        this._names = {};\n        this._prefixes = prefixes;\n        this._parent = parent;\n    }\n    toName(nameOrPrefix) {\n        return nameOrPrefix instanceof code_1.Name ? nameOrPrefix : this.name(nameOrPrefix);\n    }\n    name(prefix) {\n        return new code_1.Name(this._newName(prefix));\n    }\n    _newName(prefix) {\n        const ng = this._names[prefix] || this._nameGroup(prefix);\n        return `${prefix}${ng.index++}`;\n    }\n    _nameGroup(prefix) {\n        var _a, _b;\n        if (((_b = (_a = this._parent) === null || _a === void 0 ? void 0 : _a._prefixes) === null || _b === void 0 ? void 0 : _b.has(prefix)) || (this._prefixes && !this._prefixes.has(prefix))) {\n            throw new Error(`CodeGen: prefix \"${prefix}\" is not allowed in this scope`);\n        }\n        return (this._names[prefix] = { prefix, index: 0 });\n    }\n}\nexports.Scope = Scope;\nclass ValueScopeName extends code_1.Name {\n    constructor(prefix, nameStr) {\n        super(nameStr);\n        this.prefix = prefix;\n    }\n    setValue(value, { property, itemIndex }) {\n        this.value = value;\n        this.scopePath = (0, code_1._) `.${new code_1.Name(property)}[${itemIndex}]`;\n    }\n}\nexports.ValueScopeName = ValueScopeName;\nconst line = (0, code_1._) `\\n`;\nclass ValueScope extends Scope {\n    constructor(opts) {\n        super(opts);\n        this._values = {};\n        this._scope = opts.scope;\n        this.opts = { ...opts, _n: opts.lines ? line : code_1.nil };\n    }\n    get() {\n        return this._scope;\n    }\n    name(prefix) {\n        return new ValueScopeName(prefix, this._newName(prefix));\n    }\n    value(nameOrPrefix, value) {\n        var _a;\n        if (value.ref === undefined)\n            throw new Error(\"CodeGen: ref must be passed in value\");\n        const name = this.toName(nameOrPrefix);\n        const { prefix } = name;\n        const valueKey = (_a = value.key) !== null && _a !== void 0 ? _a : value.ref;\n        let vs = this._values[prefix];\n        if (vs) {\n            const _name = vs.get(valueKey);\n            if (_name)\n                return _name;\n        }\n        else {\n            vs = this._values[prefix] = new Map();\n        }\n        vs.set(valueKey, name);\n        const s = this._scope[prefix] || (this._scope[prefix] = []);\n        const itemIndex = s.length;\n        s[itemIndex] = value.ref;\n        name.setValue(value, { property: prefix, itemIndex });\n        return name;\n    }\n    getValue(prefix, keyOrRef) {\n        const vs = this._values[prefix];\n        if (!vs)\n            return;\n        return vs.get(keyOrRef);\n    }\n    scopeRefs(scopeName, values = this._values) {\n        return this._reduceValues(values, (name) => {\n            if (name.scopePath === undefined)\n                throw new Error(`CodeGen: name \"${name}\" has no value`);\n            return (0, code_1._) `${scopeName}${name.scopePath}`;\n        });\n    }\n    scopeCode(values = this._values, usedValues, getCode) {\n        return this._reduceValues(values, (name) => {\n            if (name.value === undefined)\n                throw new Error(`CodeGen: name \"${name}\" has no value`);\n            return name.value.code;\n        }, usedValues, getCode);\n    }\n    _reduceValues(values, valueCode, usedValues = {}, getCode) {\n        let code = code_1.nil;\n        for (const prefix in values) {\n            const vs = values[prefix];\n            if (!vs)\n                continue;\n            const nameSet = (usedValues[prefix] = usedValues[prefix] || new Map());\n            vs.forEach((name) => {\n                if (nameSet.has(name))\n                    return;\n                nameSet.set(name, UsedValueState.Started);\n                let c = valueCode(name);\n                if (c) {\n                    const def = this.opts.es5 ? exports.varKinds.var : exports.varKinds.const;\n                    code = (0, code_1._) `${code}${def} ${name} = ${c};${this.opts._n}`;\n                }\n                else if ((c = getCode === null || getCode === void 0 ? void 0 : getCode(name))) {\n                    code = (0, code_1._) `${code}${c}${this.opts._n}`;\n                }\n                else {\n                    throw new ValueError(name);\n                }\n                nameSet.set(name, UsedValueState.Completed);\n            });\n        }\n        return code;\n    }\n}\nexports.ValueScope = ValueScope;\n//# sourceMappingURL=scope.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.or = exports.and = exports.not = exports.CodeGen = exports.operators = exports.varKinds = exports.ValueScopeName = exports.ValueScope = exports.Scope = exports.Name = exports.regexpCode = exports.stringify = exports.getProperty = exports.nil = exports.strConcat = exports.str = exports._ = void 0;\nconst code_1 = require(\"./code\");\nconst scope_1 = require(\"./scope\");\nvar code_2 = require(\"./code\");\nObject.defineProperty(exports, \"_\", { enumerable: true, get: function () { return code_2._; } });\nObject.defineProperty(exports, \"str\", { enumerable: true, get: function () { return code_2.str; } });\nObject.defineProperty(exports, \"strConcat\", { enumerable: true, get: function () { return code_2.strConcat; } });\nObject.defineProperty(exports, \"nil\", { enumerable: true, get: function () { return code_2.nil; } });\nObject.defineProperty(exports, \"getProperty\", { enumerable: true, get: function () { return code_2.getProperty; } });\nObject.defineProperty(exports, \"stringify\", { enumerable: true, get: function () { return code_2.stringify; } });\nObject.defineProperty(exports, \"regexpCode\", { enumerable: true, get: function () { return code_2.regexpCode; } });\nObject.defineProperty(exports, \"Name\", { enumerable: true, get: function () { return code_2.Name; } });\nvar scope_2 = require(\"./scope\");\nObject.defineProperty(exports, \"Scope\", { enumerable: true, get: function () { return scope_2.Scope; } });\nObject.defineProperty(exports, \"ValueScope\", { enumerable: true, get: function () { return scope_2.ValueScope; } });\nObject.defineProperty(exports, \"ValueScopeName\", { enumerable: true, get: function () { return scope_2.ValueScopeName; } });\nObject.defineProperty(exports, \"varKinds\", { enumerable: true, get: function () { return scope_2.varKinds; } });\nexports.operators = {\n    GT: new code_1._Code(\">\"),\n    GTE: new code_1._Code(\">=\"),\n    LT: new code_1._Code(\"<\"),\n    LTE: new code_1._Code(\"<=\"),\n    EQ: new code_1._Code(\"===\"),\n    NEQ: new code_1._Code(\"!==\"),\n    NOT: new code_1._Code(\"!\"),\n    OR: new code_1._Code(\"||\"),\n    AND: new code_1._Code(\"&&\"),\n    ADD: new code_1._Code(\"+\"),\n};\nclass Node {\n    optimizeNodes() {\n        return this;\n    }\n    optimizeNames(_names, _constants) {\n        return this;\n    }\n}\nclass Def extends Node {\n    constructor(varKind, name, rhs) {\n        super();\n        this.varKind = varKind;\n        this.name = name;\n        this.rhs = rhs;\n    }\n    render({ es5, _n }) {\n        const varKind = es5 ? scope_1.varKinds.var : this.varKind;\n        const rhs = this.rhs === undefined ? \"\" : ` = ${this.rhs}`;\n        return `${varKind} ${this.name}${rhs};` + _n;\n    }\n    optimizeNames(names, constants) {\n        if (!names[this.name.str])\n            return;\n        if (this.rhs)\n            this.rhs = optimizeExpr(this.rhs, names, constants);\n        return this;\n    }\n    get names() {\n        return this.rhs instanceof code_1._CodeOrName ? this.rhs.names : {};\n    }\n}\nclass Assign extends Node {\n    constructor(lhs, rhs, sideEffects) {\n        super();\n        this.lhs = lhs;\n        this.rhs = rhs;\n        this.sideEffects = sideEffects;\n    }\n    render({ _n }) {\n        return `${this.lhs} = ${this.rhs};` + _n;\n    }\n    optimizeNames(names, constants) {\n        if (this.lhs instanceof code_1.Name && !names[this.lhs.str] && !this.sideEffects)\n            return;\n        this.rhs = optimizeExpr(this.rhs, names, constants);\n        return this;\n    }\n    get names() {\n        const names = this.lhs instanceof code_1.Name ? {} : { ...this.lhs.names };\n        return addExprNames(names, this.rhs);\n    }\n}\nclass AssignOp extends Assign {\n    constructor(lhs, op, rhs, sideEffects) {\n        super(lhs, rhs, sideEffects);\n        this.op = op;\n    }\n    render({ _n }) {\n        return `${this.lhs} ${this.op}= ${this.rhs};` + _n;\n    }\n}\nclass Label extends Node {\n    constructor(label) {\n        super();\n        this.label = label;\n        this.names = {};\n    }\n    render({ _n }) {\n        return `${this.label}:` + _n;\n    }\n}\nclass Break extends Node {\n    constructor(label) {\n        super();\n        this.label = label;\n        this.names = {};\n    }\n    render({ _n }) {\n        const label = this.label ? ` ${this.label}` : \"\";\n        return `break${label};` + _n;\n    }\n}\nclass Throw extends Node {\n    constructor(error) {\n        super();\n        this.error = error;\n    }\n    render({ _n }) {\n        return `throw ${this.error};` + _n;\n    }\n    get names() {\n        return this.error.names;\n    }\n}\nclass AnyCode extends Node {\n    constructor(code) {\n        super();\n        this.code = code;\n    }\n    render({ _n }) {\n        return `${this.code};` + _n;\n    }\n    optimizeNodes() {\n        return `${this.code}` ? this : undefined;\n    }\n    optimizeNames(names, constants) {\n        this.code = optimizeExpr(this.code, names, constants);\n        return this;\n    }\n    get names() {\n        return this.code instanceof code_1._CodeOrName ? this.code.names : {};\n    }\n}\nclass ParentNode extends Node {\n    constructor(nodes = []) {\n        super();\n        this.nodes = nodes;\n    }\n    render(opts) {\n        return this.nodes.reduce((code, n) => code + n.render(opts), \"\");\n    }\n    optimizeNodes() {\n        const { nodes } = this;\n        let i = nodes.length;\n        while (i--) {\n            const n = nodes[i].optimizeNodes();\n            if (Array.isArray(n))\n                nodes.splice(i, 1, ...n);\n            else if (n)\n                nodes[i] = n;\n            else\n                nodes.splice(i, 1);\n        }\n        return nodes.length > 0 ? this : undefined;\n    }\n    optimizeNames(names, constants) {\n        const { nodes } = this;\n        let i = nodes.length;\n        while (i--) {\n            // iterating backwards improves 1-pass optimization\n            const n = nodes[i];\n            if (n.optimizeNames(names, constants))\n                continue;\n            subtractNames(names, n.names);\n            nodes.splice(i, 1);\n        }\n        return nodes.length > 0 ? this : undefined;\n    }\n    get names() {\n        return this.nodes.reduce((names, n) => addNames(names, n.names), {});\n    }\n}\nclass BlockNode extends ParentNode {\n    render(opts) {\n        return \"{\" + opts._n + super.render(opts) + \"}\" + opts._n;\n    }\n}\nclass Root extends ParentNode {\n}\nclass Else extends BlockNode {\n}\nElse.kind = \"else\";\nclass If extends BlockNode {\n    constructor(condition, nodes) {\n        super(nodes);\n        this.condition = condition;\n    }\n    render(opts) {\n        let code = `if(${this.condition})` + super.render(opts);\n        if (this.else)\n            code += \"else \" + this.else.render(opts);\n        return code;\n    }\n    optimizeNodes() {\n        super.optimizeNodes();\n        const cond = this.condition;\n        if (cond === true)\n            return this.nodes; // else is ignored here\n        let e = this.else;\n        if (e) {\n            const ns = e.optimizeNodes();\n            e = this.else = Array.isArray(ns) ? new Else(ns) : ns;\n        }\n        if (e) {\n            if (cond === false)\n                return e instanceof If ? e : e.nodes;\n            if (this.nodes.length)\n                return this;\n            return new If(not(cond), e instanceof If ? [e] : e.nodes);\n        }\n        if (cond === false || !this.nodes.length)\n            return undefined;\n        return this;\n    }\n    optimizeNames(names, constants) {\n        var _a;\n        this.else = (_a = this.else) === null || _a === void 0 ? void 0 : _a.optimizeNames(names, constants);\n        if (!(super.optimizeNames(names, constants) || this.else))\n            return;\n        this.condition = optimizeExpr(this.condition, names, constants);\n        return this;\n    }\n    get names() {\n        const names = super.names;\n        addExprNames(names, this.condition);\n        if (this.else)\n            addNames(names, this.else.names);\n        return names;\n    }\n}\nIf.kind = \"if\";\nclass For extends BlockNode {\n}\nFor.kind = \"for\";\nclass ForLoop extends For {\n    constructor(iteration) {\n        super();\n        this.iteration = iteration;\n    }\n    render(opts) {\n        return `for(${this.iteration})` + super.render(opts);\n    }\n    optimizeNames(names, constants) {\n        if (!super.optimizeNames(names, constants))\n            return;\n        this.iteration = optimizeExpr(this.iteration, names, constants);\n        return this;\n    }\n    get names() {\n        return addNames(super.names, this.iteration.names);\n    }\n}\nclass ForRange extends For {\n    constructor(varKind, name, from, to) {\n        super();\n        this.varKind = varKind;\n        this.name = name;\n        this.from = from;\n        this.to = to;\n    }\n    render(opts) {\n        const varKind = opts.es5 ? scope_1.varKinds.var : this.varKind;\n        const { name, from, to } = this;\n        return `for(${varKind} ${name}=${from}; ${name}<${to}; ${name}++)` + super.render(opts);\n    }\n    get names() {\n        const names = addExprNames(super.names, this.from);\n        return addExprNames(names, this.to);\n    }\n}\nclass ForIter extends For {\n    constructor(loop, varKind, name, iterable) {\n        super();\n        this.loop = loop;\n        this.varKind = varKind;\n        this.name = name;\n        this.iterable = iterable;\n    }\n    render(opts) {\n        return `for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})` + super.render(opts);\n    }\n    optimizeNames(names, constants) {\n        if (!super.optimizeNames(names, constants))\n            return;\n        this.iterable = optimizeExpr(this.iterable, names, constants);\n        return this;\n    }\n    get names() {\n        return addNames(super.names, this.iterable.names);\n    }\n}\nclass Func extends BlockNode {\n    constructor(name, args, async) {\n        super();\n        this.name = name;\n        this.args = args;\n        this.async = async;\n    }\n    render(opts) {\n        const _async = this.async ? \"async \" : \"\";\n        return `${_async}function ${this.name}(${this.args})` + super.render(opts);\n    }\n}\nFunc.kind = \"func\";\nclass Return extends ParentNode {\n    render(opts) {\n        return \"return \" + super.render(opts);\n    }\n}\nReturn.kind = \"return\";\nclass Try extends BlockNode {\n    render(opts) {\n        let code = \"try\" + super.render(opts);\n        if (this.catch)\n            code += this.catch.render(opts);\n        if (this.finally)\n            code += this.finally.render(opts);\n        return code;\n    }\n    optimizeNodes() {\n        var _a, _b;\n        super.optimizeNodes();\n        (_a = this.catch) === null || _a === void 0 ? void 0 : _a.optimizeNodes();\n        (_b = this.finally) === null || _b === void 0 ? void 0 : _b.optimizeNodes();\n        return this;\n    }\n    optimizeNames(names, constants) {\n        var _a, _b;\n        super.optimizeNames(names, constants);\n        (_a = this.catch) === null || _a === void 0 ? void 0 : _a.optimizeNames(names, constants);\n        (_b = this.finally) === null || _b === void 0 ? void 0 : _b.optimizeNames(names, constants);\n        return this;\n    }\n    get names() {\n        const names = super.names;\n        if (this.catch)\n            addNames(names, this.catch.names);\n        if (this.finally)\n            addNames(names, this.finally.names);\n        return names;\n    }\n}\nclass Catch extends BlockNode {\n    constructor(error) {\n        super();\n        this.error = error;\n    }\n    render(opts) {\n        return `catch(${this.error})` + super.render(opts);\n    }\n}\nCatch.kind = \"catch\";\nclass Finally extends BlockNode {\n    render(opts) {\n        return \"finally\" + super.render(opts);\n    }\n}\nFinally.kind = \"finally\";\nclass CodeGen {\n    constructor(extScope, opts = {}) {\n        this._values = {};\n        this._blockStarts = [];\n        this._constants = {};\n        this.opts = { ...opts, _n: opts.lines ? \"\\n\" : \"\" };\n        this._extScope = extScope;\n        this._scope = new scope_1.Scope({ parent: extScope });\n        this._nodes = [new Root()];\n    }\n    toString() {\n        return this._root.render(this.opts);\n    }\n    // returns unique name in the internal scope\n    name(prefix) {\n        return this._scope.name(prefix);\n    }\n    // reserves unique name in the external scope\n    scopeName(prefix) {\n        return this._extScope.name(prefix);\n    }\n    // reserves unique name in the external scope and assigns value to it\n    scopeValue(prefixOrName, value) {\n        const name = this._extScope.value(prefixOrName, value);\n        const vs = this._values[name.prefix] || (this._values[name.prefix] = new Set());\n        vs.add(name);\n        return name;\n    }\n    getScopeValue(prefix, keyOrRef) {\n        return this._extScope.getValue(prefix, keyOrRef);\n    }\n    // return code that assigns values in the external scope to the names that are used internally\n    // (same names that were returned by gen.scopeName or gen.scopeValue)\n    scopeRefs(scopeName) {\n        return this._extScope.scopeRefs(scopeName, this._values);\n    }\n    scopeCode() {\n        return this._extScope.scopeCode(this._values);\n    }\n    _def(varKind, nameOrPrefix, rhs, constant) {\n        const name = this._scope.toName(nameOrPrefix);\n        if (rhs !== undefined && constant)\n            this._constants[name.str] = rhs;\n        this._leafNode(new Def(varKind, name, rhs));\n        return name;\n    }\n    // `const` declaration (`var` in es5 mode)\n    const(nameOrPrefix, rhs, _constant) {\n        return this._def(scope_1.varKinds.const, nameOrPrefix, rhs, _constant);\n    }\n    // `let` declaration with optional assignment (`var` in es5 mode)\n    let(nameOrPrefix, rhs, _constant) {\n        return this._def(scope_1.varKinds.let, nameOrPrefix, rhs, _constant);\n    }\n    // `var` declaration with optional assignment\n    var(nameOrPrefix, rhs, _constant) {\n        return this._def(scope_1.varKinds.var, nameOrPrefix, rhs, _constant);\n    }\n    // assignment code\n    assign(lhs, rhs, sideEffects) {\n        return this._leafNode(new Assign(lhs, rhs, sideEffects));\n    }\n    // `+=` code\n    add(lhs, rhs) {\n        return this._leafNode(new AssignOp(lhs, exports.operators.ADD, rhs));\n    }\n    // appends passed SafeExpr to code or executes Block\n    code(c) {\n        if (typeof c == \"function\")\n            c();\n        else if (c !== code_1.nil)\n            this._leafNode(new AnyCode(c));\n        return this;\n    }\n    // returns code for object literal for the passed argument list of key-value pairs\n    object(...keyValues) {\n        const code = [\"{\"];\n        for (const [key, value] of keyValues) {\n            if (code.length > 1)\n                code.push(\",\");\n            code.push(key);\n            if (key !== value || this.opts.es5) {\n                code.push(\":\");\n                (0, code_1.addCodeArg)(code, value);\n            }\n        }\n        code.push(\"}\");\n        return new code_1._Code(code);\n    }\n    // `if` clause (or statement if `thenBody` and, optionally, `elseBody` are passed)\n    if(condition, thenBody, elseBody) {\n        this._blockNode(new If(condition));\n        if (thenBody && elseBody) {\n            this.code(thenBody).else().code(elseBody).endIf();\n        }\n        else if (thenBody) {\n            this.code(thenBody).endIf();\n        }\n        else if (elseBody) {\n            throw new Error('CodeGen: \"else\" body without \"then\" body');\n        }\n        return this;\n    }\n    // `else if` clause - invalid without `if` or after `else` clauses\n    elseIf(condition) {\n        return this._elseNode(new If(condition));\n    }\n    // `else` clause - only valid after `if` or `else if` clauses\n    else() {\n        return this._elseNode(new Else());\n    }\n    // end `if` statement (needed if gen.if was used only with condition)\n    endIf() {\n        return this._endBlockNode(If, Else);\n    }\n    _for(node, forBody) {\n        this._blockNode(node);\n        if (forBody)\n            this.code(forBody).endFor();\n        return this;\n    }\n    // a generic `for` clause (or statement if `forBody` is passed)\n    for(iteration, forBody) {\n        return this._for(new ForLoop(iteration), forBody);\n    }\n    // `for` statement for a range of values\n    forRange(nameOrPrefix, from, to, forBody, varKind = this.opts.es5 ? scope_1.varKinds.var : scope_1.varKinds.let) {\n        const name = this._scope.toName(nameOrPrefix);\n        return this._for(new ForRange(varKind, name, from, to), () => forBody(name));\n    }\n    // `for-of` statement (in es5 mode replace with a normal for loop)\n    forOf(nameOrPrefix, iterable, forBody, varKind = scope_1.varKinds.const) {\n        const name = this._scope.toName(nameOrPrefix);\n        if (this.opts.es5) {\n            const arr = iterable instanceof code_1.Name ? iterable : this.var(\"_arr\", iterable);\n            return this.forRange(\"_i\", 0, (0, code_1._) `${arr}.length`, (i) => {\n                this.var(name, (0, code_1._) `${arr}[${i}]`);\n                forBody(name);\n            });\n        }\n        return this._for(new ForIter(\"of\", varKind, name, iterable), () => forBody(name));\n    }\n    // `for-in` statement.\n    // With option `ownProperties` replaced with a `for-of` loop for object keys\n    forIn(nameOrPrefix, obj, forBody, varKind = this.opts.es5 ? scope_1.varKinds.var : scope_1.varKinds.const) {\n        if (this.opts.ownProperties) {\n            return this.forOf(nameOrPrefix, (0, code_1._) `Object.keys(${obj})`, forBody);\n        }\n        const name = this._scope.toName(nameOrPrefix);\n        return this._for(new ForIter(\"in\", varKind, name, obj), () => forBody(name));\n    }\n    // end `for` loop\n    endFor() {\n        return this._endBlockNode(For);\n    }\n    // `label` statement\n    label(label) {\n        return this._leafNode(new Label(label));\n    }\n    // `break` statement\n    break(label) {\n        return this._leafNode(new Break(label));\n    }\n    // `return` statement\n    return(value) {\n        const node = new Return();\n        this._blockNode(node);\n        this.code(value);\n        if (node.nodes.length !== 1)\n            throw new Error('CodeGen: \"return\" should have one node');\n        return this._endBlockNode(Return);\n    }\n    // `try` statement\n    try(tryBody, catchCode, finallyCode) {\n        if (!catchCode && !finallyCode)\n            throw new Error('CodeGen: \"try\" without \"catch\" and \"finally\"');\n        const node = new Try();\n        this._blockNode(node);\n        this.code(tryBody);\n        if (catchCode) {\n            const error = this.name(\"e\");\n            this._currNode = node.catch = new Catch(error);\n            catchCode(error);\n        }\n        if (finallyCode) {\n            this._currNode = node.finally = new Finally();\n            this.code(finallyCode);\n        }\n        return this._endBlockNode(Catch, Finally);\n    }\n    // `throw` statement\n    throw(error) {\n        return this._leafNode(new Throw(error));\n    }\n    // start self-balancing block\n    block(body, nodeCount) {\n        this._blockStarts.push(this._nodes.length);\n        if (body)\n            this.code(body).endBlock(nodeCount);\n        return this;\n    }\n    // end the current self-balancing block\n    endBlock(nodeCount) {\n        const len = this._blockStarts.pop();\n        if (len === undefined)\n            throw new Error(\"CodeGen: not in self-balancing block\");\n        const toClose = this._nodes.length - len;\n        if (toClose < 0 || (nodeCount !== undefined && toClose !== nodeCount)) {\n            throw new Error(`CodeGen: wrong number of nodes: ${toClose} vs ${nodeCount} expected`);\n        }\n        this._nodes.length = len;\n        return this;\n    }\n    // `function` heading (or definition if funcBody is passed)\n    func(name, args = code_1.nil, async, funcBody) {\n        this._blockNode(new Func(name, args, async));\n        if (funcBody)\n            this.code(funcBody).endFunc();\n        return this;\n    }\n    // end function definition\n    endFunc() {\n        return this._endBlockNode(Func);\n    }\n    optimize(n = 1) {\n        while (n-- > 0) {\n            this._root.optimizeNodes();\n            this._root.optimizeNames(this._root.names, this._constants);\n        }\n    }\n    _leafNode(node) {\n        this._currNode.nodes.push(node);\n        return this;\n    }\n    _blockNode(node) {\n        this._currNode.nodes.push(node);\n        this._nodes.push(node);\n    }\n    _endBlockNode(N1, N2) {\n        const n = this._currNode;\n        if (n instanceof N1 || (N2 && n instanceof N2)) {\n            this._nodes.pop();\n            return this;\n        }\n        throw new Error(`CodeGen: not in block \"${N2 ? `${N1.kind}/${N2.kind}` : N1.kind}\"`);\n    }\n    _elseNode(node) {\n        const n = this._currNode;\n        if (!(n instanceof If)) {\n            throw new Error('CodeGen: \"else\" without \"if\"');\n        }\n        this._currNode = n.else = node;\n        return this;\n    }\n    get _root() {\n        return this._nodes[0];\n    }\n    get _currNode() {\n        const ns = this._nodes;\n        return ns[ns.length - 1];\n    }\n    set _currNode(node) {\n        const ns = this._nodes;\n        ns[ns.length - 1] = node;\n    }\n}\nexports.CodeGen = CodeGen;\nfunction addNames(names, from) {\n    for (const n in from)\n        names[n] = (names[n] || 0) + (from[n] || 0);\n    return names;\n}\nfunction addExprNames(names, from) {\n    return from instanceof code_1._CodeOrName ? addNames(names, from.names) : names;\n}\nfunction optimizeExpr(expr, names, constants) {\n    if (expr instanceof code_1.Name)\n        return replaceName(expr);\n    if (!canOptimize(expr))\n        return expr;\n    return new code_1._Code(expr._items.reduce((items, c) => {\n        if (c instanceof code_1.Name)\n            c = replaceName(c);\n        if (c instanceof code_1._Code)\n            items.push(...c._items);\n        else\n            items.push(c);\n        return items;\n    }, []));\n    function replaceName(n) {\n        const c = constants[n.str];\n        if (c === undefined || names[n.str] !== 1)\n            return n;\n        delete names[n.str];\n        return c;\n    }\n    function canOptimize(e) {\n        return (e instanceof code_1._Code &&\n            e._items.some((c) => c instanceof code_1.Name && names[c.str] === 1 && constants[c.str] !== undefined));\n    }\n}\nfunction subtractNames(names, from) {\n    for (const n in from)\n        names[n] = (names[n] || 0) - (from[n] || 0);\n}\nfunction not(x) {\n    return typeof x == \"boolean\" || typeof x == \"number\" || x === null ? !x : (0, code_1._) `!${par(x)}`;\n}\nexports.not = not;\nconst andCode = mappend(exports.operators.AND);\n// boolean AND (&&) expression with the passed arguments\nfunction and(...args) {\n    return args.reduce(andCode);\n}\nexports.and = and;\nconst orCode = mappend(exports.operators.OR);\n// boolean OR (||) expression with the passed arguments\nfunction or(...args) {\n    return args.reduce(orCode);\n}\nexports.or = or;\nfunction mappend(op) {\n    return (x, y) => (x === code_1.nil ? y : y === code_1.nil ? x : (0, code_1._) `${par(x)} ${op} ${par(y)}`);\n}\nfunction par(x) {\n    return x instanceof code_1.Name ? x : (0, code_1._) `(${x})`;\n}\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.checkStrictMode = exports.getErrorPath = exports.Type = exports.useFunc = exports.setEvaluated = exports.evaluatedPropsToName = exports.mergeEvaluated = exports.eachItem = exports.unescapeJsonPointer = exports.escapeJsonPointer = exports.escapeFragment = exports.unescapeFragment = exports.schemaRefOrVal = exports.schemaHasRulesButRef = exports.schemaHasRules = exports.checkUnknownRules = exports.alwaysValidSchema = exports.toHash = void 0;\nconst codegen_1 = require(\"./codegen\");\nconst code_1 = require(\"./codegen/code\");\n// TODO refactor to use Set\nfunction toHash(arr) {\n    const hash = {};\n    for (const item of arr)\n        hash[item] = true;\n    return hash;\n}\nexports.toHash = toHash;\nfunction alwaysValidSchema(it, schema) {\n    if (typeof schema == \"boolean\")\n        return schema;\n    if (Object.keys(schema).length === 0)\n        return true;\n    checkUnknownRules(it, schema);\n    return !schemaHasRules(schema, it.self.RULES.all);\n}\nexports.alwaysValidSchema = alwaysValidSchema;\nfunction checkUnknownRules(it, schema = it.schema) {\n    const { opts, self } = it;\n    if (!opts.strictSchema)\n        return;\n    if (typeof schema === \"boolean\")\n        return;\n    const rules = self.RULES.keywords;\n    for (const key in schema) {\n        if (!rules[key])\n            checkStrictMode(it, `unknown keyword: \"${key}\"`);\n    }\n}\nexports.checkUnknownRules = checkUnknownRules;\nfunction schemaHasRules(schema, rules) {\n    if (typeof schema == \"boolean\")\n        return !schema;\n    for (const key in schema)\n        if (rules[key])\n            return true;\n    return false;\n}\nexports.schemaHasRules = schemaHasRules;\nfunction schemaHasRulesButRef(schema, RULES) {\n    if (typeof schema == \"boolean\")\n        return !schema;\n    for (const key in schema)\n        if (key !== \"$ref\" && RULES.all[key])\n            return true;\n    return false;\n}\nexports.schemaHasRulesButRef = schemaHasRulesButRef;\nfunction schemaRefOrVal({ topSchemaRef, schemaPath }, schema, keyword, $data) {\n    if (!$data) {\n        if (typeof schema == \"number\" || typeof schema == \"boolean\")\n            return schema;\n        if (typeof schema == \"string\")\n            return (0, codegen_1._) `${schema}`;\n    }\n    return (0, codegen_1._) `${topSchemaRef}${schemaPath}${(0, codegen_1.getProperty)(keyword)}`;\n}\nexports.schemaRefOrVal = schemaRefOrVal;\nfunction unescapeFragment(str) {\n    return unescapeJsonPointer(decodeURIComponent(str));\n}\nexports.unescapeFragment = unescapeFragment;\nfunction escapeFragment(str) {\n    return encodeURIComponent(escapeJsonPointer(str));\n}\nexports.escapeFragment = escapeFragment;\nfunction escapeJsonPointer(str) {\n    if (typeof str == \"number\")\n        return `${str}`;\n    return str.replace(/~/g, \"~0\").replace(/\\//g, \"~1\");\n}\nexports.escapeJsonPointer = escapeJsonPointer;\nfunction unescapeJsonPointer(str) {\n    return str.replace(/~1/g, \"/\").replace(/~0/g, \"~\");\n}\nexports.unescapeJsonPointer = unescapeJsonPointer;\nfunction eachItem(xs, f) {\n    if (Array.isArray(xs)) {\n        for (const x of xs)\n            f(x);\n    }\n    else {\n        f(xs);\n    }\n}\nexports.eachItem = eachItem;\nfunction makeMergeEvaluated({ mergeNames, mergeToName, mergeValues, resultToName, }) {\n    return (gen, from, to, toName) => {\n        const res = to === undefined\n            ? from\n            : to instanceof codegen_1.Name\n                ? (from instanceof codegen_1.Name ? mergeNames(gen, from, to) : mergeToName(gen, from, to), to)\n                : from instanceof codegen_1.Name\n                    ? (mergeToName(gen, to, from), from)\n                    : mergeValues(from, to);\n        return toName === codegen_1.Name && !(res instanceof codegen_1.Name) ? resultToName(gen, res) : res;\n    };\n}\nexports.mergeEvaluated = {\n    props: makeMergeEvaluated({\n        mergeNames: (gen, from, to) => gen.if((0, codegen_1._) `${to} !== true && ${from} !== undefined`, () => {\n            gen.if((0, codegen_1._) `${from} === true`, () => gen.assign(to, true), () => gen.assign(to, (0, codegen_1._) `${to} || {}`).code((0, codegen_1._) `Object.assign(${to}, ${from})`));\n        }),\n        mergeToName: (gen, from, to) => gen.if((0, codegen_1._) `${to} !== true`, () => {\n            if (from === true) {\n                gen.assign(to, true);\n            }\n            else {\n                gen.assign(to, (0, codegen_1._) `${to} || {}`);\n                setEvaluated(gen, to, from);\n            }\n        }),\n        mergeValues: (from, to) => (from === true ? true : { ...from, ...to }),\n        resultToName: evaluatedPropsToName,\n    }),\n    items: makeMergeEvaluated({\n        mergeNames: (gen, from, to) => gen.if((0, codegen_1._) `${to} !== true && ${from} !== undefined`, () => gen.assign(to, (0, codegen_1._) `${from} === true ? true : ${to} > ${from} ? ${to} : ${from}`)),\n        mergeToName: (gen, from, to) => gen.if((0, codegen_1._) `${to} !== true`, () => gen.assign(to, from === true ? true : (0, codegen_1._) `${to} > ${from} ? ${to} : ${from}`)),\n        mergeValues: (from, to) => (from === true ? true : Math.max(from, to)),\n        resultToName: (gen, items) => gen.var(\"items\", items),\n    }),\n};\nfunction evaluatedPropsToName(gen, ps) {\n    if (ps === true)\n        return gen.var(\"props\", true);\n    const props = gen.var(\"props\", (0, codegen_1._) `{}`);\n    if (ps !== undefined)\n        setEvaluated(gen, props, ps);\n    return props;\n}\nexports.evaluatedPropsToName = evaluatedPropsToName;\nfunction setEvaluated(gen, props, ps) {\n    Object.keys(ps).forEach((p) => gen.assign((0, codegen_1._) `${props}${(0, codegen_1.getProperty)(p)}`, true));\n}\nexports.setEvaluated = setEvaluated;\nconst snippets = {};\nfunction useFunc(gen, f) {\n    return gen.scopeValue(\"func\", {\n        ref: f,\n        code: snippets[f.code] || (snippets[f.code] = new code_1._Code(f.code)),\n    });\n}\nexports.useFunc = useFunc;\nvar Type;\n(function (Type) {\n    Type[Type[\"Num\"] = 0] = \"Num\";\n    Type[Type[\"Str\"] = 1] = \"Str\";\n})(Type = exports.Type || (exports.Type = {}));\nfunction getErrorPath(dataProp, dataPropType, jsPropertySyntax) {\n    // let path\n    if (dataProp instanceof codegen_1.Name) {\n        const isNumber = dataPropType === Type.Num;\n        return jsPropertySyntax\n            ? isNumber\n                ? (0, codegen_1._) `\"[\" + ${dataProp} + \"]\"`\n                : (0, codegen_1._) `\"['\" + ${dataProp} + \"']\"`\n            : isNumber\n                ? (0, codegen_1._) `\"/\" + ${dataProp}`\n                : (0, codegen_1._) `\"/\" + ${dataProp}.replace(/~/g, \"~0\").replace(/\\\\//g, \"~1\")`; // TODO maybe use global escapePointer\n    }\n    return jsPropertySyntax ? (0, codegen_1.getProperty)(dataProp).toString() : \"/\" + escapeJsonPointer(dataProp);\n}\nexports.getErrorPath = getErrorPath;\nfunction checkStrictMode(it, msg, mode = it.opts.strictSchema) {\n    if (!mode)\n        return;\n    msg = `strict mode: ${msg}`;\n    if (mode === true)\n        throw new Error(msg);\n    it.self.logger.warn(msg);\n}\nexports.checkStrictMode = checkStrictMode;\n//# sourceMappingURL=util.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"./codegen\");\nconst names = {\n    // validation function arguments\n    data: new codegen_1.Name(\"data\"),\n    // args passed from referencing schema\n    valCxt: new codegen_1.Name(\"valCxt\"),\n    instancePath: new codegen_1.Name(\"instancePath\"),\n    parentData: new codegen_1.Name(\"parentData\"),\n    parentDataProperty: new codegen_1.Name(\"parentDataProperty\"),\n    rootData: new codegen_1.Name(\"rootData\"),\n    dynamicAnchors: new codegen_1.Name(\"dynamicAnchors\"),\n    // function scoped variables\n    vErrors: new codegen_1.Name(\"vErrors\"),\n    errors: new codegen_1.Name(\"errors\"),\n    this: new codegen_1.Name(\"this\"),\n    // \"globals\"\n    self: new codegen_1.Name(\"self\"),\n    scope: new codegen_1.Name(\"scope\"),\n    // JTD serialize/parse name for JSON string and position\n    json: new codegen_1.Name(\"json\"),\n    jsonPos: new codegen_1.Name(\"jsonPos\"),\n    jsonLen: new codegen_1.Name(\"jsonLen\"),\n    jsonPart: new codegen_1.Name(\"jsonPart\"),\n};\nexports.default = names;\n//# sourceMappingURL=names.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.extendErrors = exports.resetErrorsCount = exports.reportExtraError = exports.reportError = exports.keyword$DataError = exports.keywordError = void 0;\nconst codegen_1 = require(\"./codegen\");\nconst util_1 = require(\"./util\");\nconst names_1 = require(\"./names\");\nexports.keywordError = {\n    message: ({ keyword }) => (0, codegen_1.str) `must pass \"${keyword}\" keyword validation`,\n};\nexports.keyword$DataError = {\n    message: ({ keyword, schemaType }) => schemaType\n        ? (0, codegen_1.str) `\"${keyword}\" keyword must be ${schemaType} ($data)`\n        : (0, codegen_1.str) `\"${keyword}\" keyword is invalid ($data)`,\n};\nfunction reportError(cxt, error = exports.keywordError, errorPaths, overrideAllErrors) {\n    const { it } = cxt;\n    const { gen, compositeRule, allErrors } = it;\n    const errObj = errorObjectCode(cxt, error, errorPaths);\n    if (overrideAllErrors !== null && overrideAllErrors !== void 0 ? overrideAllErrors : (compositeRule || allErrors)) {\n        addError(gen, errObj);\n    }\n    else {\n        returnErrors(it, (0, codegen_1._) `[${errObj}]`);\n    }\n}\nexports.reportError = reportError;\nfunction reportExtraError(cxt, error = exports.keywordError, errorPaths) {\n    const { it } = cxt;\n    const { gen, compositeRule, allErrors } = it;\n    const errObj = errorObjectCode(cxt, error, errorPaths);\n    addError(gen, errObj);\n    if (!(compositeRule || allErrors)) {\n        returnErrors(it, names_1.default.vErrors);\n    }\n}\nexports.reportExtraError = reportExtraError;\nfunction resetErrorsCount(gen, errsCount) {\n    gen.assign(names_1.default.errors, errsCount);\n    gen.if((0, codegen_1._) `${names_1.default.vErrors} !== null`, () => gen.if(errsCount, () => gen.assign((0, codegen_1._) `${names_1.default.vErrors}.length`, errsCount), () => gen.assign(names_1.default.vErrors, null)));\n}\nexports.resetErrorsCount = resetErrorsCount;\nfunction extendErrors({ gen, keyword, schemaValue, data, errsCount, it, }) {\n    /* istanbul ignore if */\n    if (errsCount === undefined)\n        throw new Error(\"ajv implementation error\");\n    const err = gen.name(\"err\");\n    gen.forRange(\"i\", errsCount, names_1.default.errors, (i) => {\n        gen.const(err, (0, codegen_1._) `${names_1.default.vErrors}[${i}]`);\n        gen.if((0, codegen_1._) `${err}.instancePath === undefined`, () => gen.assign((0, codegen_1._) `${err}.instancePath`, (0, codegen_1.strConcat)(names_1.default.instancePath, it.errorPath)));\n        gen.assign((0, codegen_1._) `${err}.schemaPath`, (0, codegen_1.str) `${it.errSchemaPath}/${keyword}`);\n        if (it.opts.verbose) {\n            gen.assign((0, codegen_1._) `${err}.schema`, schemaValue);\n            gen.assign((0, codegen_1._) `${err}.data`, data);\n        }\n    });\n}\nexports.extendErrors = extendErrors;\nfunction addError(gen, errObj) {\n    const err = gen.const(\"err\", errObj);\n    gen.if((0, codegen_1._) `${names_1.default.vErrors} === null`, () => gen.assign(names_1.default.vErrors, (0, codegen_1._) `[${err}]`), (0, codegen_1._) `${names_1.default.vErrors}.push(${err})`);\n    gen.code((0, codegen_1._) `${names_1.default.errors}++`);\n}\nfunction returnErrors(it, errs) {\n    const { gen, validateName, schemaEnv } = it;\n    if (schemaEnv.$async) {\n        gen.throw((0, codegen_1._) `new ${it.ValidationError}(${errs})`);\n    }\n    else {\n        gen.assign((0, codegen_1._) `${validateName}.errors`, errs);\n        gen.return(false);\n    }\n}\nconst E = {\n    keyword: new codegen_1.Name(\"keyword\"),\n    schemaPath: new codegen_1.Name(\"schemaPath\"),\n    params: new codegen_1.Name(\"params\"),\n    propertyName: new codegen_1.Name(\"propertyName\"),\n    message: new codegen_1.Name(\"message\"),\n    schema: new codegen_1.Name(\"schema\"),\n    parentSchema: new codegen_1.Name(\"parentSchema\"),\n};\nfunction errorObjectCode(cxt, error, errorPaths) {\n    const { createErrors } = cxt.it;\n    if (createErrors === false)\n        return (0, codegen_1._) `{}`;\n    return errorObject(cxt, error, errorPaths);\n}\nfunction errorObject(cxt, error, errorPaths = {}) {\n    const { gen, it } = cxt;\n    const keyValues = [\n        errorInstancePath(it, errorPaths),\n        errorSchemaPath(cxt, errorPaths),\n    ];\n    extraErrorProps(cxt, error, keyValues);\n    return gen.object(...keyValues);\n}\nfunction errorInstancePath({ errorPath }, { instancePath }) {\n    const instPath = instancePath\n        ? (0, codegen_1.str) `${errorPath}${(0, util_1.getErrorPath)(instancePath, util_1.Type.Str)}`\n        : errorPath;\n    return [names_1.default.instancePath, (0, codegen_1.strConcat)(names_1.default.instancePath, instPath)];\n}\nfunction errorSchemaPath({ keyword, it: { errSchemaPath } }, { schemaPath, parentSchema }) {\n    let schPath = parentSchema ? errSchemaPath : (0, codegen_1.str) `${errSchemaPath}/${keyword}`;\n    if (schemaPath) {\n        schPath = (0, codegen_1.str) `${schPath}${(0, util_1.getErrorPath)(schemaPath, util_1.Type.Str)}`;\n    }\n    return [E.schemaPath, schPath];\n}\nfunction extraErrorProps(cxt, { params, message }, keyValues) {\n    const { keyword, data, schemaValue, it } = cxt;\n    const { opts, propertyName, topSchemaRef, schemaPath } = it;\n    keyValues.push([E.keyword, keyword], [E.params, typeof params == \"function\" ? params(cxt) : params || (0, codegen_1._) `{}`]);\n    if (opts.messages) {\n        keyValues.push([E.message, typeof message == \"function\" ? message(cxt) : message]);\n    }\n    if (opts.verbose) {\n        keyValues.push([E.schema, schemaValue], [E.parentSchema, (0, codegen_1._) `${topSchemaRef}${schemaPath}`], [names_1.default.data, data]);\n    }\n    if (propertyName)\n        keyValues.push([E.propertyName, propertyName]);\n}\n//# sourceMappingURL=errors.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.boolOrEmptySchema = exports.topBoolOrEmptySchema = void 0;\nconst errors_1 = require(\"../errors\");\nconst codegen_1 = require(\"../codegen\");\nconst names_1 = require(\"../names\");\nconst boolError = {\n    message: \"boolean schema is false\",\n};\nfunction topBoolOrEmptySchema(it) {\n    const { gen, schema, validateName } = it;\n    if (schema === false) {\n        falseSchemaError(it, false);\n    }\n    else if (typeof schema == \"object\" && schema.$async === true) {\n        gen.return(names_1.default.data);\n    }\n    else {\n        gen.assign((0, codegen_1._) `${validateName}.errors`, null);\n        gen.return(true);\n    }\n}\nexports.topBoolOrEmptySchema = topBoolOrEmptySchema;\nfunction boolOrEmptySchema(it, valid) {\n    const { gen, schema } = it;\n    if (schema === false) {\n        gen.var(valid, false); // TODO var\n        falseSchemaError(it);\n    }\n    else {\n        gen.var(valid, true); // TODO var\n    }\n}\nexports.boolOrEmptySchema = boolOrEmptySchema;\nfunction falseSchemaError(it, overrideAllErrors) {\n    const { gen, data } = it;\n    // TODO maybe some other interface should be used for non-keyword validation errors...\n    const cxt = {\n        gen,\n        keyword: \"false schema\",\n        data,\n        schema: false,\n        schemaCode: false,\n        schemaValue: false,\n        params: {},\n        it,\n    };\n    (0, errors_1.reportError)(cxt, boolError, undefined, overrideAllErrors);\n}\n//# sourceMappingURL=boolSchema.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getRules = exports.isJSONType = void 0;\nconst _jsonTypes = [\"string\", \"number\", \"integer\", \"boolean\", \"null\", \"object\", \"array\"];\nconst jsonTypes = new Set(_jsonTypes);\nfunction isJSONType(x) {\n    return typeof x == \"string\" && jsonTypes.has(x);\n}\nexports.isJSONType = isJSONType;\nfunction getRules() {\n    const groups = {\n        number: { type: \"number\", rules: [] },\n        string: { type: \"string\", rules: [] },\n        array: { type: \"array\", rules: [] },\n        object: { type: \"object\", rules: [] },\n    };\n    return {\n        types: { ...groups, integer: true, boolean: true, null: true },\n        rules: [{ rules: [] }, groups.number, groups.string, groups.array, groups.object],\n        post: { rules: [] },\n        all: {},\n        keywords: {},\n    };\n}\nexports.getRules = getRules;\n//# sourceMappingURL=rules.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.shouldUseRule = exports.shouldUseGroup = exports.schemaHasRulesForType = void 0;\nfunction schemaHasRulesForType({ schema, self }, type) {\n    const group = self.RULES.types[type];\n    return group && group !== true && shouldUseGroup(schema, group);\n}\nexports.schemaHasRulesForType = schemaHasRulesForType;\nfunction shouldUseGroup(schema, group) {\n    return group.rules.some((rule) => shouldUseRule(schema, rule));\n}\nexports.shouldUseGroup = shouldUseGroup;\nfunction shouldUseRule(schema, rule) {\n    var _a;\n    return (schema[rule.keyword] !== undefined ||\n        ((_a = rule.definition.implements) === null || _a === void 0 ? void 0 : _a.some((kwd) => schema[kwd] !== undefined)));\n}\nexports.shouldUseRule = shouldUseRule;\n//# sourceMappingURL=applicability.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.reportTypeError = exports.checkDataTypes = exports.checkDataType = exports.coerceAndCheckDataType = exports.getJSONTypes = exports.getSchemaTypes = exports.DataType = void 0;\nconst rules_1 = require(\"../rules\");\nconst applicability_1 = require(\"./applicability\");\nconst errors_1 = require(\"../errors\");\nconst codegen_1 = require(\"../codegen\");\nconst util_1 = require(\"../util\");\nvar DataType;\n(function (DataType) {\n    DataType[DataType[\"Correct\"] = 0] = \"Correct\";\n    DataType[DataType[\"Wrong\"] = 1] = \"Wrong\";\n})(DataType = exports.DataType || (exports.DataType = {}));\nfunction getSchemaTypes(schema) {\n    const types = getJSONTypes(schema.type);\n    const hasNull = types.includes(\"null\");\n    if (hasNull) {\n        if (schema.nullable === false)\n            throw new Error(\"type: null contradicts nullable: false\");\n    }\n    else {\n        if (!types.length && schema.nullable !== undefined) {\n            throw new Error('\"nullable\" cannot be used without \"type\"');\n        }\n        if (schema.nullable === true)\n            types.push(\"null\");\n    }\n    return types;\n}\nexports.getSchemaTypes = getSchemaTypes;\nfunction getJSONTypes(ts) {\n    const types = Array.isArray(ts) ? ts : ts ? [ts] : [];\n    if (types.every(rules_1.isJSONType))\n        return types;\n    throw new Error(\"type must be JSONType or JSONType[]: \" + types.join(\",\"));\n}\nexports.getJSONTypes = getJSONTypes;\nfunction coerceAndCheckDataType(it, types) {\n    const { gen, data, opts } = it;\n    const coerceTo = coerceToTypes(types, opts.coerceTypes);\n    const checkTypes = types.length > 0 &&\n        !(coerceTo.length === 0 && types.length === 1 && (0, applicability_1.schemaHasRulesForType)(it, types[0]));\n    if (checkTypes) {\n        const wrongType = checkDataTypes(types, data, opts.strictNumbers, DataType.Wrong);\n        gen.if(wrongType, () => {\n            if (coerceTo.length)\n                coerceData(it, types, coerceTo);\n            else\n                reportTypeError(it);\n        });\n    }\n    return checkTypes;\n}\nexports.coerceAndCheckDataType = coerceAndCheckDataType;\nconst COERCIBLE = new Set([\"string\", \"number\", \"integer\", \"boolean\", \"null\"]);\nfunction coerceToTypes(types, coerceTypes) {\n    return coerceTypes\n        ? types.filter((t) => COERCIBLE.has(t) || (coerceTypes === \"array\" && t === \"array\"))\n        : [];\n}\nfunction coerceData(it, types, coerceTo) {\n    const { gen, data, opts } = it;\n    const dataType = gen.let(\"dataType\", (0, codegen_1._) `typeof ${data}`);\n    const coerced = gen.let(\"coerced\", (0, codegen_1._) `undefined`);\n    if (opts.coerceTypes === \"array\") {\n        gen.if((0, codegen_1._) `${dataType} == 'object' && Array.isArray(${data}) && ${data}.length == 1`, () => gen\n            .assign(data, (0, codegen_1._) `${data}[0]`)\n            .assign(dataType, (0, codegen_1._) `typeof ${data}`)\n            .if(checkDataTypes(types, data, opts.strictNumbers), () => gen.assign(coerced, data)));\n    }\n    gen.if((0, codegen_1._) `${coerced} !== undefined`);\n    for (const t of coerceTo) {\n        if (COERCIBLE.has(t) || (t === \"array\" && opts.coerceTypes === \"array\")) {\n            coerceSpecificType(t);\n        }\n    }\n    gen.else();\n    reportTypeError(it);\n    gen.endIf();\n    gen.if((0, codegen_1._) `${coerced} !== undefined`, () => {\n        gen.assign(data, coerced);\n        assignParentData(it, coerced);\n    });\n    function coerceSpecificType(t) {\n        switch (t) {\n            case \"string\":\n                gen\n                    .elseIf((0, codegen_1._) `${dataType} == \"number\" || ${dataType} == \"boolean\"`)\n                    .assign(coerced, (0, codegen_1._) `\"\" + ${data}`)\n                    .elseIf((0, codegen_1._) `${data} === null`)\n                    .assign(coerced, (0, codegen_1._) `\"\"`);\n                return;\n            case \"number\":\n                gen\n                    .elseIf((0, codegen_1._) `${dataType} == \"boolean\" || ${data} === null\n              || (${dataType} == \"string\" && ${data} && ${data} == +${data})`)\n                    .assign(coerced, (0, codegen_1._) `+${data}`);\n                return;\n            case \"integer\":\n                gen\n                    .elseIf((0, codegen_1._) `${dataType} === \"boolean\" || ${data} === null\n              || (${dataType} === \"string\" && ${data} && ${data} == +${data} && !(${data} % 1))`)\n                    .assign(coerced, (0, codegen_1._) `+${data}`);\n                return;\n            case \"boolean\":\n                gen\n                    .elseIf((0, codegen_1._) `${data} === \"false\" || ${data} === 0 || ${data} === null`)\n                    .assign(coerced, false)\n                    .elseIf((0, codegen_1._) `${data} === \"true\" || ${data} === 1`)\n                    .assign(coerced, true);\n                return;\n            case \"null\":\n                gen.elseIf((0, codegen_1._) `${data} === \"\" || ${data} === 0 || ${data} === false`);\n                gen.assign(coerced, null);\n                return;\n            case \"array\":\n                gen\n                    .elseIf((0, codegen_1._) `${dataType} === \"string\" || ${dataType} === \"number\"\n              || ${dataType} === \"boolean\" || ${data} === null`)\n                    .assign(coerced, (0, codegen_1._) `[${data}]`);\n        }\n    }\n}\nfunction assignParentData({ gen, parentData, parentDataProperty }, expr) {\n    // TODO use gen.property\n    gen.if((0, codegen_1._) `${parentData} !== undefined`, () => gen.assign((0, codegen_1._) `${parentData}[${parentDataProperty}]`, expr));\n}\nfunction checkDataType(dataType, data, strictNums, correct = DataType.Correct) {\n    const EQ = correct === DataType.Correct ? codegen_1.operators.EQ : codegen_1.operators.NEQ;\n    let cond;\n    switch (dataType) {\n        case \"null\":\n            return (0, codegen_1._) `${data} ${EQ} null`;\n        case \"array\":\n            cond = (0, codegen_1._) `Array.isArray(${data})`;\n            break;\n        case \"object\":\n            cond = (0, codegen_1._) `${data} && typeof ${data} == \"object\" && !Array.isArray(${data})`;\n            break;\n        case \"integer\":\n            cond = numCond((0, codegen_1._) `!(${data} % 1) && !isNaN(${data})`);\n            break;\n        case \"number\":\n            cond = numCond();\n            break;\n        default:\n            return (0, codegen_1._) `typeof ${data} ${EQ} ${dataType}`;\n    }\n    return correct === DataType.Correct ? cond : (0, codegen_1.not)(cond);\n    function numCond(_cond = codegen_1.nil) {\n        return (0, codegen_1.and)((0, codegen_1._) `typeof ${data} == \"number\"`, _cond, strictNums ? (0, codegen_1._) `isFinite(${data})` : codegen_1.nil);\n    }\n}\nexports.checkDataType = checkDataType;\nfunction checkDataTypes(dataTypes, data, strictNums, correct) {\n    if (dataTypes.length === 1) {\n        return checkDataType(dataTypes[0], data, strictNums, correct);\n    }\n    let cond;\n    const types = (0, util_1.toHash)(dataTypes);\n    if (types.array && types.object) {\n        const notObj = (0, codegen_1._) `typeof ${data} != \"object\"`;\n        cond = types.null ? notObj : (0, codegen_1._) `!${data} || ${notObj}`;\n        delete types.null;\n        delete types.array;\n        delete types.object;\n    }\n    else {\n        cond = codegen_1.nil;\n    }\n    if (types.number)\n        delete types.integer;\n    for (const t in types)\n        cond = (0, codegen_1.and)(cond, checkDataType(t, data, strictNums, correct));\n    return cond;\n}\nexports.checkDataTypes = checkDataTypes;\nconst typeError = {\n    message: ({ schema }) => `must be ${schema}`,\n    params: ({ schema, schemaValue }) => typeof schema == \"string\" ? (0, codegen_1._) `{type: ${schema}}` : (0, codegen_1._) `{type: ${schemaValue}}`,\n};\nfunction reportTypeError(it) {\n    const cxt = getTypeErrorContext(it);\n    (0, errors_1.reportError)(cxt, typeError);\n}\nexports.reportTypeError = reportTypeError;\nfunction getTypeErrorContext(it) {\n    const { gen, data, schema } = it;\n    const schemaCode = (0, util_1.schemaRefOrVal)(it, schema, \"type\");\n    return {\n        gen,\n        keyword: \"type\",\n        data,\n        schema: schema.type,\n        schemaCode,\n        schemaValue: schemaCode,\n        parentSchema: schema,\n        params: {},\n        it,\n    };\n}\n//# sourceMappingURL=dataType.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.assignDefaults = void 0;\nconst codegen_1 = require(\"../codegen\");\nconst util_1 = require(\"../util\");\nfunction assignDefaults(it, ty) {\n    const { properties, items } = it.schema;\n    if (ty === \"object\" && properties) {\n        for (const key in properties) {\n            assignDefault(it, key, properties[key].default);\n        }\n    }\n    else if (ty === \"array\" && Array.isArray(items)) {\n        items.forEach((sch, i) => assignDefault(it, i, sch.default));\n    }\n}\nexports.assignDefaults = assignDefaults;\nfunction assignDefault(it, prop, defaultValue) {\n    const { gen, compositeRule, data, opts } = it;\n    if (defaultValue === undefined)\n        return;\n    const childData = (0, codegen_1._) `${data}${(0, codegen_1.getProperty)(prop)}`;\n    if (compositeRule) {\n        (0, util_1.checkStrictMode)(it, `default is ignored for: ${childData}`);\n        return;\n    }\n    let condition = (0, codegen_1._) `${childData} === undefined`;\n    if (opts.useDefaults === \"empty\") {\n        condition = (0, codegen_1._) `${condition} || ${childData} === null || ${childData} === \"\"`;\n    }\n    // `${childData} === undefined` +\n    // (opts.useDefaults === \"empty\" ? ` || ${childData} === null || ${childData} === \"\"` : \"\")\n    gen.if(condition, (0, codegen_1._) `${childData} = ${(0, codegen_1.stringify)(defaultValue)}`);\n}\n//# sourceMappingURL=defaults.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateUnion = exports.validateArray = exports.usePattern = exports.callValidateCode = exports.schemaProperties = exports.allSchemaProperties = exports.noPropertyInData = exports.propertyInData = exports.isOwnProperty = exports.hasPropFunc = exports.reportMissingProp = exports.checkMissingProp = exports.checkReportMissingProp = void 0;\nconst codegen_1 = require(\"../compile/codegen\");\nconst util_1 = require(\"../compile/util\");\nconst names_1 = require(\"../compile/names\");\nconst util_2 = require(\"../compile/util\");\nfunction checkReportMissingProp(cxt, prop) {\n    const { gen, data, it } = cxt;\n    gen.if(noPropertyInData(gen, data, prop, it.opts.ownProperties), () => {\n        cxt.setParams({ missingProperty: (0, codegen_1._) `${prop}` }, true);\n        cxt.error();\n    });\n}\nexports.checkReportMissingProp = checkReportMissingProp;\nfunction checkMissingProp({ gen, data, it: { opts } }, properties, missing) {\n    return (0, codegen_1.or)(...properties.map((prop) => (0, codegen_1.and)(noPropertyInData(gen, data, prop, opts.ownProperties), (0, codegen_1._) `${missing} = ${prop}`)));\n}\nexports.checkMissingProp = checkMissingProp;\nfunction reportMissingProp(cxt, missing) {\n    cxt.setParams({ missingProperty: missing }, true);\n    cxt.error();\n}\nexports.reportMissingProp = reportMissingProp;\nfunction hasPropFunc(gen) {\n    return gen.scopeValue(\"func\", {\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        ref: Object.prototype.hasOwnProperty,\n        code: (0, codegen_1._) `Object.prototype.hasOwnProperty`,\n    });\n}\nexports.hasPropFunc = hasPropFunc;\nfunction isOwnProperty(gen, data, property) {\n    return (0, codegen_1._) `${hasPropFunc(gen)}.call(${data}, ${property})`;\n}\nexports.isOwnProperty = isOwnProperty;\nfunction propertyInData(gen, data, property, ownProperties) {\n    const cond = (0, codegen_1._) `${data}${(0, codegen_1.getProperty)(property)} !== undefined`;\n    return ownProperties ? (0, codegen_1._) `${cond} && ${isOwnProperty(gen, data, property)}` : cond;\n}\nexports.propertyInData = propertyInData;\nfunction noPropertyInData(gen, data, property, ownProperties) {\n    const cond = (0, codegen_1._) `${data}${(0, codegen_1.getProperty)(property)} === undefined`;\n    return ownProperties ? (0, codegen_1.or)(cond, (0, codegen_1.not)(isOwnProperty(gen, data, property))) : cond;\n}\nexports.noPropertyInData = noPropertyInData;\nfunction allSchemaProperties(schemaMap) {\n    return schemaMap ? Object.keys(schemaMap).filter((p) => p !== \"__proto__\") : [];\n}\nexports.allSchemaProperties = allSchemaProperties;\nfunction schemaProperties(it, schemaMap) {\n    return allSchemaProperties(schemaMap).filter((p) => !(0, util_1.alwaysValidSchema)(it, schemaMap[p]));\n}\nexports.schemaProperties = schemaProperties;\nfunction callValidateCode({ schemaCode, data, it: { gen, topSchemaRef, schemaPath, errorPath }, it }, func, context, passSchema) {\n    const dataAndSchema = passSchema ? (0, codegen_1._) `${schemaCode}, ${data}, ${topSchemaRef}${schemaPath}` : data;\n    const valCxt = [\n        [names_1.default.instancePath, (0, codegen_1.strConcat)(names_1.default.instancePath, errorPath)],\n        [names_1.default.parentData, it.parentData],\n        [names_1.default.parentDataProperty, it.parentDataProperty],\n        [names_1.default.rootData, names_1.default.rootData],\n    ];\n    if (it.opts.dynamicRef)\n        valCxt.push([names_1.default.dynamicAnchors, names_1.default.dynamicAnchors]);\n    const args = (0, codegen_1._) `${dataAndSchema}, ${gen.object(...valCxt)}`;\n    return context !== codegen_1.nil ? (0, codegen_1._) `${func}.call(${context}, ${args})` : (0, codegen_1._) `${func}(${args})`;\n}\nexports.callValidateCode = callValidateCode;\nconst newRegExp = (0, codegen_1._) `new RegExp`;\nfunction usePattern({ gen, it: { opts } }, pattern) {\n    const u = opts.unicodeRegExp ? \"u\" : \"\";\n    const { regExp } = opts.code;\n    const rx = regExp(pattern, u);\n    return gen.scopeValue(\"pattern\", {\n        key: rx.toString(),\n        ref: rx,\n        code: (0, codegen_1._) `${regExp.code === \"new RegExp\" ? newRegExp : (0, util_2.useFunc)(gen, regExp)}(${pattern}, ${u})`,\n    });\n}\nexports.usePattern = usePattern;\nfunction validateArray(cxt) {\n    const { gen, data, keyword, it } = cxt;\n    const valid = gen.name(\"valid\");\n    if (it.allErrors) {\n        const validArr = gen.let(\"valid\", true);\n        validateItems(() => gen.assign(validArr, false));\n        return validArr;\n    }\n    gen.var(valid, true);\n    validateItems(() => gen.break());\n    return valid;\n    function validateItems(notValid) {\n        const len = gen.const(\"len\", (0, codegen_1._) `${data}.length`);\n        gen.forRange(\"i\", 0, len, (i) => {\n            cxt.subschema({\n                keyword,\n                dataProp: i,\n                dataPropType: util_1.Type.Num,\n            }, valid);\n            gen.if((0, codegen_1.not)(valid), notValid);\n        });\n    }\n}\nexports.validateArray = validateArray;\nfunction validateUnion(cxt) {\n    const { gen, schema, keyword, it } = cxt;\n    /* istanbul ignore if */\n    if (!Array.isArray(schema))\n        throw new Error(\"ajv implementation error\");\n    const alwaysValid = schema.some((sch) => (0, util_1.alwaysValidSchema)(it, sch));\n    if (alwaysValid && !it.opts.unevaluated)\n        return;\n    const valid = gen.let(\"valid\", false);\n    const schValid = gen.name(\"_valid\");\n    gen.block(() => schema.forEach((_sch, i) => {\n        const schCxt = cxt.subschema({\n            keyword,\n            schemaProp: i,\n            compositeRule: true,\n        }, schValid);\n        gen.assign(valid, (0, codegen_1._) `${valid} || ${schValid}`);\n        const merged = cxt.mergeValidEvaluated(schCxt, schValid);\n        // can short-circuit if `unevaluatedProperties/Items` not supported (opts.unevaluated !== true)\n        // or if all properties and items were evaluated (it.props === true && it.items === true)\n        if (!merged)\n            gen.if((0, codegen_1.not)(valid));\n    }));\n    cxt.result(valid, () => cxt.reset(), () => cxt.error(true));\n}\nexports.validateUnion = validateUnion;\n//# sourceMappingURL=code.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateKeywordUsage = exports.validSchemaType = exports.funcKeywordCode = exports.macroKeywordCode = void 0;\nconst codegen_1 = require(\"../codegen\");\nconst names_1 = require(\"../names\");\nconst code_1 = require(\"../../vocabularies/code\");\nconst errors_1 = require(\"../errors\");\nfunction macroKeywordCode(cxt, def) {\n    const { gen, keyword, schema, parentSchema, it } = cxt;\n    const macroSchema = def.macro.call(it.self, schema, parentSchema, it);\n    const schemaRef = useKeyword(gen, keyword, macroSchema);\n    if (it.opts.validateSchema !== false)\n        it.self.validateSchema(macroSchema, true);\n    const valid = gen.name(\"valid\");\n    cxt.subschema({\n        schema: macroSchema,\n        schemaPath: codegen_1.nil,\n        errSchemaPath: `${it.errSchemaPath}/${keyword}`,\n        topSchemaRef: schemaRef,\n        compositeRule: true,\n    }, valid);\n    cxt.pass(valid, () => cxt.error(true));\n}\nexports.macroKeywordCode = macroKeywordCode;\nfunction funcKeywordCode(cxt, def) {\n    var _a;\n    const { gen, keyword, schema, parentSchema, $data, it } = cxt;\n    checkAsyncKeyword(it, def);\n    const validate = !$data && def.compile ? def.compile.call(it.self, schema, parentSchema, it) : def.validate;\n    const validateRef = useKeyword(gen, keyword, validate);\n    const valid = gen.let(\"valid\");\n    cxt.block$data(valid, validateKeyword);\n    cxt.ok((_a = def.valid) !== null && _a !== void 0 ? _a : valid);\n    function validateKeyword() {\n        if (def.errors === false) {\n            assignValid();\n            if (def.modifying)\n                modifyData(cxt);\n            reportErrs(() => cxt.error());\n        }\n        else {\n            const ruleErrs = def.async ? validateAsync() : validateSync();\n            if (def.modifying)\n                modifyData(cxt);\n            reportErrs(() => addErrs(cxt, ruleErrs));\n        }\n    }\n    function validateAsync() {\n        const ruleErrs = gen.let(\"ruleErrs\", null);\n        gen.try(() => assignValid((0, codegen_1._) `await `), (e) => gen.assign(valid, false).if((0, codegen_1._) `${e} instanceof ${it.ValidationError}`, () => gen.assign(ruleErrs, (0, codegen_1._) `${e}.errors`), () => gen.throw(e)));\n        return ruleErrs;\n    }\n    function validateSync() {\n        const validateErrs = (0, codegen_1._) `${validateRef}.errors`;\n        gen.assign(validateErrs, null);\n        assignValid(codegen_1.nil);\n        return validateErrs;\n    }\n    function assignValid(_await = def.async ? (0, codegen_1._) `await ` : codegen_1.nil) {\n        const passCxt = it.opts.passContext ? names_1.default.this : names_1.default.self;\n        const passSchema = !((\"compile\" in def && !$data) || def.schema === false);\n        gen.assign(valid, (0, codegen_1._) `${_await}${(0, code_1.callValidateCode)(cxt, validateRef, passCxt, passSchema)}`, def.modifying);\n    }\n    function reportErrs(errors) {\n        var _a;\n        gen.if((0, codegen_1.not)((_a = def.valid) !== null && _a !== void 0 ? _a : valid), errors);\n    }\n}\nexports.funcKeywordCode = funcKeywordCode;\nfunction modifyData(cxt) {\n    const { gen, data, it } = cxt;\n    gen.if(it.parentData, () => gen.assign(data, (0, codegen_1._) `${it.parentData}[${it.parentDataProperty}]`));\n}\nfunction addErrs(cxt, errs) {\n    const { gen } = cxt;\n    gen.if((0, codegen_1._) `Array.isArray(${errs})`, () => {\n        gen\n            .assign(names_1.default.vErrors, (0, codegen_1._) `${names_1.default.vErrors} === null ? ${errs} : ${names_1.default.vErrors}.concat(${errs})`)\n            .assign(names_1.default.errors, (0, codegen_1._) `${names_1.default.vErrors}.length`);\n        (0, errors_1.extendErrors)(cxt);\n    }, () => cxt.error());\n}\nfunction checkAsyncKeyword({ schemaEnv }, def) {\n    if (def.async && !schemaEnv.$async)\n        throw new Error(\"async keyword in sync schema\");\n}\nfunction useKeyword(gen, keyword, result) {\n    if (result === undefined)\n        throw new Error(`keyword \"${keyword}\" failed to compile`);\n    return gen.scopeValue(\"keyword\", typeof result == \"function\" ? { ref: result } : { ref: result, code: (0, codegen_1.stringify)(result) });\n}\nfunction validSchemaType(schema, schemaType, allowUndefined = false) {\n    // TODO add tests\n    return (!schemaType.length ||\n        schemaType.some((st) => st === \"array\"\n            ? Array.isArray(schema)\n            : st === \"object\"\n                ? schema && typeof schema == \"object\" && !Array.isArray(schema)\n                : typeof schema == st || (allowUndefined && typeof schema == \"undefined\")));\n}\nexports.validSchemaType = validSchemaType;\nfunction validateKeywordUsage({ schema, opts, self, errSchemaPath }, def, keyword) {\n    /* istanbul ignore if */\n    if (Array.isArray(def.keyword) ? !def.keyword.includes(keyword) : def.keyword !== keyword) {\n        throw new Error(\"ajv implementation error\");\n    }\n    const deps = def.dependencies;\n    if (deps === null || deps === void 0 ? void 0 : deps.some((kwd) => !Object.prototype.hasOwnProperty.call(schema, kwd))) {\n        throw new Error(`parent schema must have dependencies of ${keyword}: ${deps.join(\",\")}`);\n    }\n    if (def.validateSchema) {\n        const valid = def.validateSchema(schema[keyword]);\n        if (!valid) {\n            const msg = `keyword \"${keyword}\" value is invalid at path \"${errSchemaPath}\": ` +\n                self.errorsText(def.validateSchema.errors);\n            if (opts.validateSchema === \"log\")\n                self.logger.error(msg);\n            else\n                throw new Error(msg);\n        }\n    }\n}\nexports.validateKeywordUsage = validateKeywordUsage;\n//# sourceMappingURL=keyword.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.extendSubschemaMode = exports.extendSubschemaData = exports.getSubschema = void 0;\nconst codegen_1 = require(\"../codegen\");\nconst util_1 = require(\"../util\");\nfunction getSubschema(it, { keyword, schemaProp, schema, schemaPath, errSchemaPath, topSchemaRef }) {\n    if (keyword !== undefined && schema !== undefined) {\n        throw new Error('both \"keyword\" and \"schema\" passed, only one allowed');\n    }\n    if (keyword !== undefined) {\n        const sch = it.schema[keyword];\n        return schemaProp === undefined\n            ? {\n                schema: sch,\n                schemaPath: (0, codegen_1._) `${it.schemaPath}${(0, codegen_1.getProperty)(keyword)}`,\n                errSchemaPath: `${it.errSchemaPath}/${keyword}`,\n            }\n            : {\n                schema: sch[schemaProp],\n                schemaPath: (0, codegen_1._) `${it.schemaPath}${(0, codegen_1.getProperty)(keyword)}${(0, codegen_1.getProperty)(schemaProp)}`,\n                errSchemaPath: `${it.errSchemaPath}/${keyword}/${(0, util_1.escapeFragment)(schemaProp)}`,\n            };\n    }\n    if (schema !== undefined) {\n        if (schemaPath === undefined || errSchemaPath === undefined || topSchemaRef === undefined) {\n            throw new Error('\"schemaPath\", \"errSchemaPath\" and \"topSchemaRef\" are required with \"schema\"');\n        }\n        return {\n            schema,\n            schemaPath,\n            topSchemaRef,\n            errSchemaPath,\n        };\n    }\n    throw new Error('either \"keyword\" or \"schema\" must be passed');\n}\nexports.getSubschema = getSubschema;\nfunction extendSubschemaData(subschema, it, { dataProp, dataPropType: dpType, data, dataTypes, propertyName }) {\n    if (data !== undefined && dataProp !== undefined) {\n        throw new Error('both \"data\" and \"dataProp\" passed, only one allowed');\n    }\n    const { gen } = it;\n    if (dataProp !== undefined) {\n        const { errorPath, dataPathArr, opts } = it;\n        const nextData = gen.let(\"data\", (0, codegen_1._) `${it.data}${(0, codegen_1.getProperty)(dataProp)}`, true);\n        dataContextProps(nextData);\n        subschema.errorPath = (0, codegen_1.str) `${errorPath}${(0, util_1.getErrorPath)(dataProp, dpType, opts.jsPropertySyntax)}`;\n        subschema.parentDataProperty = (0, codegen_1._) `${dataProp}`;\n        subschema.dataPathArr = [...dataPathArr, subschema.parentDataProperty];\n    }\n    if (data !== undefined) {\n        const nextData = data instanceof codegen_1.Name ? data : gen.let(\"data\", data, true); // replaceable if used once?\n        dataContextProps(nextData);\n        if (propertyName !== undefined)\n            subschema.propertyName = propertyName;\n        // TODO something is possibly wrong here with not changing parentDataProperty and not appending dataPathArr\n    }\n    if (dataTypes)\n        subschema.dataTypes = dataTypes;\n    function dataContextProps(_nextData) {\n        subschema.data = _nextData;\n        subschema.dataLevel = it.dataLevel + 1;\n        subschema.dataTypes = [];\n        it.definedProperties = new Set();\n        subschema.parentData = it.data;\n        subschema.dataNames = [...it.dataNames, _nextData];\n    }\n}\nexports.extendSubschemaData = extendSubschemaData;\nfunction extendSubschemaMode(subschema, { jtdDiscriminator, jtdMetadata, compositeRule, createErrors, allErrors }) {\n    if (compositeRule !== undefined)\n        subschema.compositeRule = compositeRule;\n    if (createErrors !== undefined)\n        subschema.createErrors = createErrors;\n    if (allErrors !== undefined)\n        subschema.allErrors = allErrors;\n    subschema.jtdDiscriminator = jtdDiscriminator; // not inherited\n    subschema.jtdMetadata = jtdMetadata; // not inherited\n}\nexports.extendSubschemaMode = extendSubschemaMode;\n//# sourceMappingURL=subschema.js.map", "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "'use strict';\n\nvar traverse = module.exports = function (schema, opts, cb) {\n  // Legacy support for v0.3.1 and earlier.\n  if (typeof opts == 'function') {\n    cb = opts;\n    opts = {};\n  }\n\n  cb = opts.cb || cb;\n  var pre = (typeof cb == 'function') ? cb : cb.pre || function() {};\n  var post = cb.post || function() {};\n\n  _traverse(opts, pre, post, schema, '', schema);\n};\n\n\ntraverse.keywords = {\n  additionalItems: true,\n  items: true,\n  contains: true,\n  additionalProperties: true,\n  propertyNames: true,\n  not: true,\n  if: true,\n  then: true,\n  else: true\n};\n\ntraverse.arrayKeywords = {\n  items: true,\n  allOf: true,\n  anyOf: true,\n  oneOf: true\n};\n\ntraverse.propsKeywords = {\n  $defs: true,\n  definitions: true,\n  properties: true,\n  patternProperties: true,\n  dependencies: true\n};\n\ntraverse.skipKeywords = {\n  default: true,\n  enum: true,\n  const: true,\n  required: true,\n  maximum: true,\n  minimum: true,\n  exclusiveMaximum: true,\n  exclusiveMinimum: true,\n  multipleOf: true,\n  maxLength: true,\n  minLength: true,\n  pattern: true,\n  format: true,\n  maxItems: true,\n  minItems: true,\n  uniqueItems: true,\n  maxProperties: true,\n  minProperties: true\n};\n\n\nfunction _traverse(opts, pre, post, schema, jsonPtr, rootSchema, parentJsonPtr, parentKeyword, parentSchema, keyIndex) {\n  if (schema && typeof schema == 'object' && !Array.isArray(schema)) {\n    pre(schema, jsonPtr, rootSchema, parentJsonPtr, parentKeyword, parentSchema, keyIndex);\n    for (var key in schema) {\n      var sch = schema[key];\n      if (Array.isArray(sch)) {\n        if (key in traverse.arrayKeywords) {\n          for (var i=0; i<sch.length; i++)\n            _traverse(opts, pre, post, sch[i], jsonPtr + '/' + key + '/' + i, rootSchema, jsonPtr, key, schema, i);\n        }\n      } else if (key in traverse.propsKeywords) {\n        if (sch && typeof sch == 'object') {\n          for (var prop in sch)\n            _traverse(opts, pre, post, sch[prop], jsonPtr + '/' + key + '/' + escapeJsonPtr(prop), rootSchema, jsonPtr, key, schema, prop);\n        }\n      } else if (key in traverse.keywords || (opts.allKeys && !(key in traverse.skipKeywords))) {\n        _traverse(opts, pre, post, sch, jsonPtr + '/' + key, rootSchema, jsonPtr, key, schema);\n      }\n    }\n    post(schema, jsonPtr, rootSchema, parentJsonPtr, parentKeyword, parentSchema, keyIndex);\n  }\n}\n\n\nfunction escapeJsonPtr(str) {\n  return str.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getSchemaRefs = exports.resolveUrl = exports.normalizeId = exports._getFullPath = exports.getFullPath = exports.inlineRef = void 0;\nconst util_1 = require(\"./util\");\nconst equal = require(\"fast-deep-equal\");\nconst traverse = require(\"json-schema-traverse\");\n// TODO refactor to use keyword definitions\nconst SIMPLE_INLINED = new Set([\n    \"type\",\n    \"format\",\n    \"pattern\",\n    \"maxLength\",\n    \"minLength\",\n    \"maxProperties\",\n    \"minProperties\",\n    \"maxItems\",\n    \"minItems\",\n    \"maximum\",\n    \"minimum\",\n    \"uniqueItems\",\n    \"multipleOf\",\n    \"required\",\n    \"enum\",\n    \"const\",\n]);\nfunction inlineRef(schema, limit = true) {\n    if (typeof schema == \"boolean\")\n        return true;\n    if (limit === true)\n        return !hasRef(schema);\n    if (!limit)\n        return false;\n    return countKeys(schema) <= limit;\n}\nexports.inlineRef = inlineRef;\nconst REF_KEYWORDS = new Set([\n    \"$ref\",\n    \"$recursiveRef\",\n    \"$recursiveAnchor\",\n    \"$dynamicRef\",\n    \"$dynamicAnchor\",\n]);\nfunction hasRef(schema) {\n    for (const key in schema) {\n        if (REF_KEYWORDS.has(key))\n            return true;\n        const sch = schema[key];\n        if (Array.isArray(sch) && sch.some(hasRef))\n            return true;\n        if (typeof sch == \"object\" && hasRef(sch))\n            return true;\n    }\n    return false;\n}\nfunction countKeys(schema) {\n    let count = 0;\n    for (const key in schema) {\n        if (key === \"$ref\")\n            return Infinity;\n        count++;\n        if (SIMPLE_INLINED.has(key))\n            continue;\n        if (typeof schema[key] == \"object\") {\n            (0, util_1.eachItem)(schema[key], (sch) => (count += countKeys(sch)));\n        }\n        if (count === Infinity)\n            return Infinity;\n    }\n    return count;\n}\nfunction getFullPath(resolver, id = \"\", normalize) {\n    if (normalize !== false)\n        id = normalizeId(id);\n    const p = resolver.parse(id);\n    return _getFullPath(resolver, p);\n}\nexports.getFullPath = getFullPath;\nfunction _getFullPath(resolver, p) {\n    const serialized = resolver.serialize(p);\n    return serialized.split(\"#\")[0] + \"#\";\n}\nexports._getFullPath = _getFullPath;\nconst TRAILING_SLASH_HASH = /#\\/?$/;\nfunction normalizeId(id) {\n    return id ? id.replace(TRAILING_SLASH_HASH, \"\") : \"\";\n}\nexports.normalizeId = normalizeId;\nfunction resolveUrl(resolver, baseId, id) {\n    id = normalizeId(id);\n    return resolver.resolve(baseId, id);\n}\nexports.resolveUrl = resolveUrl;\nconst ANCHOR = /^[a-z_][-a-z0-9._]*$/i;\nfunction getSchemaRefs(schema, baseId) {\n    if (typeof schema == \"boolean\")\n        return {};\n    const { schemaId, uriResolver } = this.opts;\n    const schId = normalizeId(schema[schemaId] || baseId);\n    const baseIds = { \"\": schId };\n    const pathPrefix = getFullPath(uriResolver, schId, false);\n    const localRefs = {};\n    const schemaRefs = new Set();\n    traverse(schema, { allKeys: true }, (sch, jsonPtr, _, parentJsonPtr) => {\n        if (parentJsonPtr === undefined)\n            return;\n        const fullPath = pathPrefix + jsonPtr;\n        let baseId = baseIds[parentJsonPtr];\n        if (typeof sch[schemaId] == \"string\")\n            baseId = addRef.call(this, sch[schemaId]);\n        addAnchor.call(this, sch.$anchor);\n        addAnchor.call(this, sch.$dynamicAnchor);\n        baseIds[jsonPtr] = baseId;\n        function addRef(ref) {\n            // eslint-disable-next-line @typescript-eslint/unbound-method\n            const _resolve = this.opts.uriResolver.resolve;\n            ref = normalizeId(baseId ? _resolve(baseId, ref) : ref);\n            if (schemaRefs.has(ref))\n                throw ambiguos(ref);\n            schemaRefs.add(ref);\n            let schOrRef = this.refs[ref];\n            if (typeof schOrRef == \"string\")\n                schOrRef = this.refs[schOrRef];\n            if (typeof schOrRef == \"object\") {\n                checkAmbiguosRef(sch, schOrRef.schema, ref);\n            }\n            else if (ref !== normalizeId(fullPath)) {\n                if (ref[0] === \"#\") {\n                    checkAmbiguosRef(sch, localRefs[ref], ref);\n                    localRefs[ref] = sch;\n                }\n                else {\n                    this.refs[ref] = fullPath;\n                }\n            }\n            return ref;\n        }\n        function addAnchor(anchor) {\n            if (typeof anchor == \"string\") {\n                if (!ANCHOR.test(anchor))\n                    throw new Error(`invalid anchor \"${anchor}\"`);\n                addRef.call(this, `#${anchor}`);\n            }\n        }\n    });\n    return localRefs;\n    function checkAmbiguosRef(sch1, sch2, ref) {\n        if (sch2 !== undefined && !equal(sch1, sch2))\n            throw ambiguos(ref);\n    }\n    function ambiguos(ref) {\n        return new Error(`reference \"${ref}\" resolves to more than one schema`);\n    }\n}\nexports.getSchemaRefs = getSchemaRefs;\n//# sourceMappingURL=resolve.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getData = exports.KeywordCxt = exports.validateFunctionCode = void 0;\nconst boolSchema_1 = require(\"./boolSchema\");\nconst dataType_1 = require(\"./dataType\");\nconst applicability_1 = require(\"./applicability\");\nconst dataType_2 = require(\"./dataType\");\nconst defaults_1 = require(\"./defaults\");\nconst keyword_1 = require(\"./keyword\");\nconst subschema_1 = require(\"./subschema\");\nconst codegen_1 = require(\"../codegen\");\nconst names_1 = require(\"../names\");\nconst resolve_1 = require(\"../resolve\");\nconst util_1 = require(\"../util\");\nconst errors_1 = require(\"../errors\");\n// schema compilation - generates validation function, subschemaCode (below) is used for subschemas\nfunction validateFunctionCode(it) {\n    if (isSchemaObj(it)) {\n        checkKeywords(it);\n        if (schemaCxtHasRules(it)) {\n            topSchemaObjCode(it);\n            return;\n        }\n    }\n    validateFunction(it, () => (0, boolSchema_1.topBoolOrEmptySchema)(it));\n}\nexports.validateFunctionCode = validateFunctionCode;\nfunction validateFunction({ gen, validateName, schema, schemaEnv, opts }, body) {\n    if (opts.code.es5) {\n        gen.func(validateName, (0, codegen_1._) `${names_1.default.data}, ${names_1.default.valCxt}`, schemaEnv.$async, () => {\n            gen.code((0, codegen_1._) `\"use strict\"; ${funcSourceUrl(schema, opts)}`);\n            destructureValCxtES5(gen, opts);\n            gen.code(body);\n        });\n    }\n    else {\n        gen.func(validateName, (0, codegen_1._) `${names_1.default.data}, ${destructureValCxt(opts)}`, schemaEnv.$async, () => gen.code(funcSourceUrl(schema, opts)).code(body));\n    }\n}\nfunction destructureValCxt(opts) {\n    return (0, codegen_1._) `{${names_1.default.instancePath}=\"\", ${names_1.default.parentData}, ${names_1.default.parentDataProperty}, ${names_1.default.rootData}=${names_1.default.data}${opts.dynamicRef ? (0, codegen_1._) `, ${names_1.default.dynamicAnchors}={}` : codegen_1.nil}}={}`;\n}\nfunction destructureValCxtES5(gen, opts) {\n    gen.if(names_1.default.valCxt, () => {\n        gen.var(names_1.default.instancePath, (0, codegen_1._) `${names_1.default.valCxt}.${names_1.default.instancePath}`);\n        gen.var(names_1.default.parentData, (0, codegen_1._) `${names_1.default.valCxt}.${names_1.default.parentData}`);\n        gen.var(names_1.default.parentDataProperty, (0, codegen_1._) `${names_1.default.valCxt}.${names_1.default.parentDataProperty}`);\n        gen.var(names_1.default.rootData, (0, codegen_1._) `${names_1.default.valCxt}.${names_1.default.rootData}`);\n        if (opts.dynamicRef)\n            gen.var(names_1.default.dynamicAnchors, (0, codegen_1._) `${names_1.default.valCxt}.${names_1.default.dynamicAnchors}`);\n    }, () => {\n        gen.var(names_1.default.instancePath, (0, codegen_1._) `\"\"`);\n        gen.var(names_1.default.parentData, (0, codegen_1._) `undefined`);\n        gen.var(names_1.default.parentDataProperty, (0, codegen_1._) `undefined`);\n        gen.var(names_1.default.rootData, names_1.default.data);\n        if (opts.dynamicRef)\n            gen.var(names_1.default.dynamicAnchors, (0, codegen_1._) `{}`);\n    });\n}\nfunction topSchemaObjCode(it) {\n    const { schema, opts, gen } = it;\n    validateFunction(it, () => {\n        if (opts.$comment && schema.$comment)\n            commentKeyword(it);\n        checkNoDefault(it);\n        gen.let(names_1.default.vErrors, null);\n        gen.let(names_1.default.errors, 0);\n        if (opts.unevaluated)\n            resetEvaluated(it);\n        typeAndKeywords(it);\n        returnResults(it);\n    });\n    return;\n}\nfunction resetEvaluated(it) {\n    // TODO maybe some hook to execute it in the end to check whether props/items are Name, as in assignEvaluated\n    const { gen, validateName } = it;\n    it.evaluated = gen.const(\"evaluated\", (0, codegen_1._) `${validateName}.evaluated`);\n    gen.if((0, codegen_1._) `${it.evaluated}.dynamicProps`, () => gen.assign((0, codegen_1._) `${it.evaluated}.props`, (0, codegen_1._) `undefined`));\n    gen.if((0, codegen_1._) `${it.evaluated}.dynamicItems`, () => gen.assign((0, codegen_1._) `${it.evaluated}.items`, (0, codegen_1._) `undefined`));\n}\nfunction funcSourceUrl(schema, opts) {\n    const schId = typeof schema == \"object\" && schema[opts.schemaId];\n    return schId && (opts.code.source || opts.code.process) ? (0, codegen_1._) `/*# sourceURL=${schId} */` : codegen_1.nil;\n}\n// schema compilation - this function is used recursively to generate code for sub-schemas\nfunction subschemaCode(it, valid) {\n    if (isSchemaObj(it)) {\n        checkKeywords(it);\n        if (schemaCxtHasRules(it)) {\n            subSchemaObjCode(it, valid);\n            return;\n        }\n    }\n    (0, boolSchema_1.boolOrEmptySchema)(it, valid);\n}\nfunction schemaCxtHasRules({ schema, self }) {\n    if (typeof schema == \"boolean\")\n        return !schema;\n    for (const key in schema)\n        if (self.RULES.all[key])\n            return true;\n    return false;\n}\nfunction isSchemaObj(it) {\n    return typeof it.schema != \"boolean\";\n}\nfunction subSchemaObjCode(it, valid) {\n    const { schema, gen, opts } = it;\n    if (opts.$comment && schema.$comment)\n        commentKeyword(it);\n    updateContext(it);\n    checkAsyncSchema(it);\n    const errsCount = gen.const(\"_errs\", names_1.default.errors);\n    typeAndKeywords(it, errsCount);\n    // TODO var\n    gen.var(valid, (0, codegen_1._) `${errsCount} === ${names_1.default.errors}`);\n}\nfunction checkKeywords(it) {\n    (0, util_1.checkUnknownRules)(it);\n    checkRefsAndKeywords(it);\n}\nfunction typeAndKeywords(it, errsCount) {\n    if (it.opts.jtd)\n        return schemaKeywords(it, [], false, errsCount);\n    const types = (0, dataType_1.getSchemaTypes)(it.schema);\n    const checkedTypes = (0, dataType_1.coerceAndCheckDataType)(it, types);\n    schemaKeywords(it, types, !checkedTypes, errsCount);\n}\nfunction checkRefsAndKeywords(it) {\n    const { schema, errSchemaPath, opts, self } = it;\n    if (schema.$ref && opts.ignoreKeywordsWithRef && (0, util_1.schemaHasRulesButRef)(schema, self.RULES)) {\n        self.logger.warn(`$ref: keywords ignored in schema at path \"${errSchemaPath}\"`);\n    }\n}\nfunction checkNoDefault(it) {\n    const { schema, opts } = it;\n    if (schema.default !== undefined && opts.useDefaults && opts.strictSchema) {\n        (0, util_1.checkStrictMode)(it, \"default is ignored in the schema root\");\n    }\n}\nfunction updateContext(it) {\n    const schId = it.schema[it.opts.schemaId];\n    if (schId)\n        it.baseId = (0, resolve_1.resolveUrl)(it.opts.uriResolver, it.baseId, schId);\n}\nfunction checkAsyncSchema(it) {\n    if (it.schema.$async && !it.schemaEnv.$async)\n        throw new Error(\"async schema in sync schema\");\n}\nfunction commentKeyword({ gen, schemaEnv, schema, errSchemaPath, opts }) {\n    const msg = schema.$comment;\n    if (opts.$comment === true) {\n        gen.code((0, codegen_1._) `${names_1.default.self}.logger.log(${msg})`);\n    }\n    else if (typeof opts.$comment == \"function\") {\n        const schemaPath = (0, codegen_1.str) `${errSchemaPath}/$comment`;\n        const rootName = gen.scopeValue(\"root\", { ref: schemaEnv.root });\n        gen.code((0, codegen_1._) `${names_1.default.self}.opts.$comment(${msg}, ${schemaPath}, ${rootName}.schema)`);\n    }\n}\nfunction returnResults(it) {\n    const { gen, schemaEnv, validateName, ValidationError, opts } = it;\n    if (schemaEnv.$async) {\n        // TODO assign unevaluated\n        gen.if((0, codegen_1._) `${names_1.default.errors} === 0`, () => gen.return(names_1.default.data), () => gen.throw((0, codegen_1._) `new ${ValidationError}(${names_1.default.vErrors})`));\n    }\n    else {\n        gen.assign((0, codegen_1._) `${validateName}.errors`, names_1.default.vErrors);\n        if (opts.unevaluated)\n            assignEvaluated(it);\n        gen.return((0, codegen_1._) `${names_1.default.errors} === 0`);\n    }\n}\nfunction assignEvaluated({ gen, evaluated, props, items }) {\n    if (props instanceof codegen_1.Name)\n        gen.assign((0, codegen_1._) `${evaluated}.props`, props);\n    if (items instanceof codegen_1.Name)\n        gen.assign((0, codegen_1._) `${evaluated}.items`, items);\n}\nfunction schemaKeywords(it, types, typeErrors, errsCount) {\n    const { gen, schema, data, allErrors, opts, self } = it;\n    const { RULES } = self;\n    if (schema.$ref && (opts.ignoreKeywordsWithRef || !(0, util_1.schemaHasRulesButRef)(schema, RULES))) {\n        gen.block(() => keywordCode(it, \"$ref\", RULES.all.$ref.definition)); // TODO typecast\n        return;\n    }\n    if (!opts.jtd)\n        checkStrictTypes(it, types);\n    gen.block(() => {\n        for (const group of RULES.rules)\n            groupKeywords(group);\n        groupKeywords(RULES.post);\n    });\n    function groupKeywords(group) {\n        if (!(0, applicability_1.shouldUseGroup)(schema, group))\n            return;\n        if (group.type) {\n            gen.if((0, dataType_2.checkDataType)(group.type, data, opts.strictNumbers));\n            iterateKeywords(it, group);\n            if (types.length === 1 && types[0] === group.type && typeErrors) {\n                gen.else();\n                (0, dataType_2.reportTypeError)(it);\n            }\n            gen.endIf();\n        }\n        else {\n            iterateKeywords(it, group);\n        }\n        // TODO make it \"ok\" call?\n        if (!allErrors)\n            gen.if((0, codegen_1._) `${names_1.default.errors} === ${errsCount || 0}`);\n    }\n}\nfunction iterateKeywords(it, group) {\n    const { gen, schema, opts: { useDefaults }, } = it;\n    if (useDefaults)\n        (0, defaults_1.assignDefaults)(it, group.type);\n    gen.block(() => {\n        for (const rule of group.rules) {\n            if ((0, applicability_1.shouldUseRule)(schema, rule)) {\n                keywordCode(it, rule.keyword, rule.definition, group.type);\n            }\n        }\n    });\n}\nfunction checkStrictTypes(it, types) {\n    if (it.schemaEnv.meta || !it.opts.strictTypes)\n        return;\n    checkContextTypes(it, types);\n    if (!it.opts.allowUnionTypes)\n        checkMultipleTypes(it, types);\n    checkKeywordTypes(it, it.dataTypes);\n}\nfunction checkContextTypes(it, types) {\n    if (!types.length)\n        return;\n    if (!it.dataTypes.length) {\n        it.dataTypes = types;\n        return;\n    }\n    types.forEach((t) => {\n        if (!includesType(it.dataTypes, t)) {\n            strictTypesError(it, `type \"${t}\" not allowed by context \"${it.dataTypes.join(\",\")}\"`);\n        }\n    });\n    it.dataTypes = it.dataTypes.filter((t) => includesType(types, t));\n}\nfunction checkMultipleTypes(it, ts) {\n    if (ts.length > 1 && !(ts.length === 2 && ts.includes(\"null\"))) {\n        strictTypesError(it, \"use allowUnionTypes to allow union type keyword\");\n    }\n}\nfunction checkKeywordTypes(it, ts) {\n    const rules = it.self.RULES.all;\n    for (const keyword in rules) {\n        const rule = rules[keyword];\n        if (typeof rule == \"object\" && (0, applicability_1.shouldUseRule)(it.schema, rule)) {\n            const { type } = rule.definition;\n            if (type.length && !type.some((t) => hasApplicableType(ts, t))) {\n                strictTypesError(it, `missing type \"${type.join(\",\")}\" for keyword \"${keyword}\"`);\n            }\n        }\n    }\n}\nfunction hasApplicableType(schTs, kwdT) {\n    return schTs.includes(kwdT) || (kwdT === \"number\" && schTs.includes(\"integer\"));\n}\nfunction includesType(ts, t) {\n    return ts.includes(t) || (t === \"integer\" && ts.includes(\"number\"));\n}\nfunction strictTypesError(it, msg) {\n    const schemaPath = it.schemaEnv.baseId + it.errSchemaPath;\n    msg += ` at \"${schemaPath}\" (strictTypes)`;\n    (0, util_1.checkStrictMode)(it, msg, it.opts.strictTypes);\n}\nclass KeywordCxt {\n    constructor(it, def, keyword) {\n        (0, keyword_1.validateKeywordUsage)(it, def, keyword);\n        this.gen = it.gen;\n        this.allErrors = it.allErrors;\n        this.keyword = keyword;\n        this.data = it.data;\n        this.schema = it.schema[keyword];\n        this.$data = def.$data && it.opts.$data && this.schema && this.schema.$data;\n        this.schemaValue = (0, util_1.schemaRefOrVal)(it, this.schema, keyword, this.$data);\n        this.schemaType = def.schemaType;\n        this.parentSchema = it.schema;\n        this.params = {};\n        this.it = it;\n        this.def = def;\n        if (this.$data) {\n            this.schemaCode = it.gen.const(\"vSchema\", getData(this.$data, it));\n        }\n        else {\n            this.schemaCode = this.schemaValue;\n            if (!(0, keyword_1.validSchemaType)(this.schema, def.schemaType, def.allowUndefined)) {\n                throw new Error(`${keyword} value must be ${JSON.stringify(def.schemaType)}`);\n            }\n        }\n        if (\"code\" in def ? def.trackErrors : def.errors !== false) {\n            this.errsCount = it.gen.const(\"_errs\", names_1.default.errors);\n        }\n    }\n    result(condition, successAction, failAction) {\n        this.failResult((0, codegen_1.not)(condition), successAction, failAction);\n    }\n    failResult(condition, successAction, failAction) {\n        this.gen.if(condition);\n        if (failAction)\n            failAction();\n        else\n            this.error();\n        if (successAction) {\n            this.gen.else();\n            successAction();\n            if (this.allErrors)\n                this.gen.endIf();\n        }\n        else {\n            if (this.allErrors)\n                this.gen.endIf();\n            else\n                this.gen.else();\n        }\n    }\n    pass(condition, failAction) {\n        this.failResult((0, codegen_1.not)(condition), undefined, failAction);\n    }\n    fail(condition) {\n        if (condition === undefined) {\n            this.error();\n            if (!this.allErrors)\n                this.gen.if(false); // this branch will be removed by gen.optimize\n            return;\n        }\n        this.gen.if(condition);\n        this.error();\n        if (this.allErrors)\n            this.gen.endIf();\n        else\n            this.gen.else();\n    }\n    fail$data(condition) {\n        if (!this.$data)\n            return this.fail(condition);\n        const { schemaCode } = this;\n        this.fail((0, codegen_1._) `${schemaCode} !== undefined && (${(0, codegen_1.or)(this.invalid$data(), condition)})`);\n    }\n    error(append, errorParams, errorPaths) {\n        if (errorParams) {\n            this.setParams(errorParams);\n            this._error(append, errorPaths);\n            this.setParams({});\n            return;\n        }\n        this._error(append, errorPaths);\n    }\n    _error(append, errorPaths) {\n        ;\n        (append ? errors_1.reportExtraError : errors_1.reportError)(this, this.def.error, errorPaths);\n    }\n    $dataError() {\n        (0, errors_1.reportError)(this, this.def.$dataError || errors_1.keyword$DataError);\n    }\n    reset() {\n        if (this.errsCount === undefined)\n            throw new Error('add \"trackErrors\" to keyword definition');\n        (0, errors_1.resetErrorsCount)(this.gen, this.errsCount);\n    }\n    ok(cond) {\n        if (!this.allErrors)\n            this.gen.if(cond);\n    }\n    setParams(obj, assign) {\n        if (assign)\n            Object.assign(this.params, obj);\n        else\n            this.params = obj;\n    }\n    block$data(valid, codeBlock, $dataValid = codegen_1.nil) {\n        this.gen.block(() => {\n            this.check$data(valid, $dataValid);\n            codeBlock();\n        });\n    }\n    check$data(valid = codegen_1.nil, $dataValid = codegen_1.nil) {\n        if (!this.$data)\n            return;\n        const { gen, schemaCode, schemaType, def } = this;\n        gen.if((0, codegen_1.or)((0, codegen_1._) `${schemaCode} === undefined`, $dataValid));\n        if (valid !== codegen_1.nil)\n            gen.assign(valid, true);\n        if (schemaType.length || def.validateSchema) {\n            gen.elseIf(this.invalid$data());\n            this.$dataError();\n            if (valid !== codegen_1.nil)\n                gen.assign(valid, false);\n        }\n        gen.else();\n    }\n    invalid$data() {\n        const { gen, schemaCode, schemaType, def, it } = this;\n        return (0, codegen_1.or)(wrong$DataType(), invalid$DataSchema());\n        function wrong$DataType() {\n            if (schemaType.length) {\n                /* istanbul ignore if */\n                if (!(schemaCode instanceof codegen_1.Name))\n                    throw new Error(\"ajv implementation error\");\n                const st = Array.isArray(schemaType) ? schemaType : [schemaType];\n                return (0, codegen_1._) `${(0, dataType_2.checkDataTypes)(st, schemaCode, it.opts.strictNumbers, dataType_2.DataType.Wrong)}`;\n            }\n            return codegen_1.nil;\n        }\n        function invalid$DataSchema() {\n            if (def.validateSchema) {\n                const validateSchemaRef = gen.scopeValue(\"validate$data\", { ref: def.validateSchema }); // TODO value.code for standalone\n                return (0, codegen_1._) `!${validateSchemaRef}(${schemaCode})`;\n            }\n            return codegen_1.nil;\n        }\n    }\n    subschema(appl, valid) {\n        const subschema = (0, subschema_1.getSubschema)(this.it, appl);\n        (0, subschema_1.extendSubschemaData)(subschema, this.it, appl);\n        (0, subschema_1.extendSubschemaMode)(subschema, appl);\n        const nextContext = { ...this.it, ...subschema, items: undefined, props: undefined };\n        subschemaCode(nextContext, valid);\n        return nextContext;\n    }\n    mergeEvaluated(schemaCxt, toName) {\n        const { it, gen } = this;\n        if (!it.opts.unevaluated)\n            return;\n        if (it.props !== true && schemaCxt.props !== undefined) {\n            it.props = util_1.mergeEvaluated.props(gen, schemaCxt.props, it.props, toName);\n        }\n        if (it.items !== true && schemaCxt.items !== undefined) {\n            it.items = util_1.mergeEvaluated.items(gen, schemaCxt.items, it.items, toName);\n        }\n    }\n    mergeValidEvaluated(schemaCxt, valid) {\n        const { it, gen } = this;\n        if (it.opts.unevaluated && (it.props !== true || it.items !== true)) {\n            gen.if(valid, () => this.mergeEvaluated(schemaCxt, codegen_1.Name));\n            return true;\n        }\n    }\n}\nexports.KeywordCxt = KeywordCxt;\nfunction keywordCode(it, keyword, def, ruleType) {\n    const cxt = new KeywordCxt(it, def, keyword);\n    if (\"code\" in def) {\n        def.code(cxt, ruleType);\n    }\n    else if (cxt.$data && def.validate) {\n        (0, keyword_1.funcKeywordCode)(cxt, def);\n    }\n    else if (\"macro\" in def) {\n        (0, keyword_1.macroKeywordCode)(cxt, def);\n    }\n    else if (def.compile || def.validate) {\n        (0, keyword_1.funcKeywordCode)(cxt, def);\n    }\n}\nconst JSON_POINTER = /^\\/(?:[^~]|~0|~1)*$/;\nconst RELATIVE_JSON_POINTER = /^([0-9]+)(#|\\/(?:[^~]|~0|~1)*)?$/;\nfunction getData($data, { dataLevel, dataNames, dataPathArr }) {\n    let jsonPointer;\n    let data;\n    if ($data === \"\")\n        return names_1.default.rootData;\n    if ($data[0] === \"/\") {\n        if (!JSON_POINTER.test($data))\n            throw new Error(`Invalid JSON-pointer: ${$data}`);\n        jsonPointer = $data;\n        data = names_1.default.rootData;\n    }\n    else {\n        const matches = RELATIVE_JSON_POINTER.exec($data);\n        if (!matches)\n            throw new Error(`Invalid JSON-pointer: ${$data}`);\n        const up = +matches[1];\n        jsonPointer = matches[2];\n        if (jsonPointer === \"#\") {\n            if (up >= dataLevel)\n                throw new Error(errorMsg(\"property/index\", up));\n            return dataPathArr[dataLevel - up];\n        }\n        if (up > dataLevel)\n            throw new Error(errorMsg(\"data\", up));\n        data = dataNames[dataLevel - up];\n        if (!jsonPointer)\n            return data;\n    }\n    let expr = data;\n    const segments = jsonPointer.split(\"/\");\n    for (const segment of segments) {\n        if (segment) {\n            data = (0, codegen_1._) `${data}${(0, codegen_1.getProperty)((0, util_1.unescapeJsonPointer)(segment))}`;\n            expr = (0, codegen_1._) `${expr} && ${data}`;\n        }\n    }\n    return expr;\n    function errorMsg(pointerType, up) {\n        return `Cannot access ${pointerType} ${up} levels up, current level is ${dataLevel}`;\n    }\n}\nexports.getData = getData;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nclass ValidationError extends Error {\n    constructor(errors) {\n        super(\"validation failed\");\n        this.errors = errors;\n        this.ajv = this.validation = true;\n    }\n}\nexports.default = ValidationError;\n//# sourceMappingURL=validation_error.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst resolve_1 = require(\"./resolve\");\nclass MissingRefError extends Error {\n    constructor(resolver, baseId, ref, msg) {\n        super(msg || `can't resolve reference ${ref} from id ${baseId}`);\n        this.missingRef = (0, resolve_1.resolveUrl)(resolver, baseId, ref);\n        this.missingSchema = (0, resolve_1.normalizeId)((0, resolve_1.getFullPath)(resolver, this.missingRef));\n    }\n}\nexports.default = MissingRefError;\n//# sourceMappingURL=ref_error.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.resolveSchema = exports.getCompilingSchema = exports.resolveRef = exports.compileSchema = exports.SchemaEnv = void 0;\nconst codegen_1 = require(\"./codegen\");\nconst validation_error_1 = require(\"../runtime/validation_error\");\nconst names_1 = require(\"./names\");\nconst resolve_1 = require(\"./resolve\");\nconst util_1 = require(\"./util\");\nconst validate_1 = require(\"./validate\");\nclass SchemaEnv {\n    constructor(env) {\n        var _a;\n        this.refs = {};\n        this.dynamicAnchors = {};\n        let schema;\n        if (typeof env.schema == \"object\")\n            schema = env.schema;\n        this.schema = env.schema;\n        this.schemaId = env.schemaId;\n        this.root = env.root || this;\n        this.baseId = (_a = env.baseId) !== null && _a !== void 0 ? _a : (0, resolve_1.normalizeId)(schema === null || schema === void 0 ? void 0 : schema[env.schemaId || \"$id\"]);\n        this.schemaPath = env.schemaPath;\n        this.localRefs = env.localRefs;\n        this.meta = env.meta;\n        this.$async = schema === null || schema === void 0 ? void 0 : schema.$async;\n        this.refs = {};\n    }\n}\nexports.SchemaEnv = SchemaEnv;\n// let codeSize = 0\n// let nodeCount = 0\n// Compiles schema in SchemaEnv\nfunction compileSchema(sch) {\n    // TODO refactor - remove compilations\n    const _sch = getCompilingSchema.call(this, sch);\n    if (_sch)\n        return _sch;\n    const rootId = (0, resolve_1.getFullPath)(this.opts.uriResolver, sch.root.baseId); // TODO if getFullPath removed 1 tests fails\n    const { es5, lines } = this.opts.code;\n    const { ownProperties } = this.opts;\n    const gen = new codegen_1.CodeGen(this.scope, { es5, lines, ownProperties });\n    let _ValidationError;\n    if (sch.$async) {\n        _ValidationError = gen.scopeValue(\"Error\", {\n            ref: validation_error_1.default,\n            code: (0, codegen_1._) `require(\"ajv/dist/runtime/validation_error\").default`,\n        });\n    }\n    const validateName = gen.scopeName(\"validate\");\n    sch.validateName = validateName;\n    const schemaCxt = {\n        gen,\n        allErrors: this.opts.allErrors,\n        data: names_1.default.data,\n        parentData: names_1.default.parentData,\n        parentDataProperty: names_1.default.parentDataProperty,\n        dataNames: [names_1.default.data],\n        dataPathArr: [codegen_1.nil],\n        dataLevel: 0,\n        dataTypes: [],\n        definedProperties: new Set(),\n        topSchemaRef: gen.scopeValue(\"schema\", this.opts.code.source === true\n            ? { ref: sch.schema, code: (0, codegen_1.stringify)(sch.schema) }\n            : { ref: sch.schema }),\n        validateName,\n        ValidationError: _ValidationError,\n        schema: sch.schema,\n        schemaEnv: sch,\n        rootId,\n        baseId: sch.baseId || rootId,\n        schemaPath: codegen_1.nil,\n        errSchemaPath: sch.schemaPath || (this.opts.jtd ? \"\" : \"#\"),\n        errorPath: (0, codegen_1._) `\"\"`,\n        opts: this.opts,\n        self: this,\n    };\n    let sourceCode;\n    try {\n        this._compilations.add(sch);\n        (0, validate_1.validateFunctionCode)(schemaCxt);\n        gen.optimize(this.opts.code.optimize);\n        // gen.optimize(1)\n        const validateCode = gen.toString();\n        sourceCode = `${gen.scopeRefs(names_1.default.scope)}return ${validateCode}`;\n        // console.log((codeSize += sourceCode.length), (nodeCount += gen.nodeCount))\n        if (this.opts.code.process)\n            sourceCode = this.opts.code.process(sourceCode, sch);\n        // console.log(\"\\n\\n\\n *** \\n\", sourceCode)\n        const makeValidate = new Function(`${names_1.default.self}`, `${names_1.default.scope}`, sourceCode);\n        const validate = makeValidate(this, this.scope.get());\n        this.scope.value(validateName, { ref: validate });\n        validate.errors = null;\n        validate.schema = sch.schema;\n        validate.schemaEnv = sch;\n        if (sch.$async)\n            validate.$async = true;\n        if (this.opts.code.source === true) {\n            validate.source = { validateName, validateCode, scopeValues: gen._values };\n        }\n        if (this.opts.unevaluated) {\n            const { props, items } = schemaCxt;\n            validate.evaluated = {\n                props: props instanceof codegen_1.Name ? undefined : props,\n                items: items instanceof codegen_1.Name ? undefined : items,\n                dynamicProps: props instanceof codegen_1.Name,\n                dynamicItems: items instanceof codegen_1.Name,\n            };\n            if (validate.source)\n                validate.source.evaluated = (0, codegen_1.stringify)(validate.evaluated);\n        }\n        sch.validate = validate;\n        return sch;\n    }\n    catch (e) {\n        delete sch.validate;\n        delete sch.validateName;\n        if (sourceCode)\n            this.logger.error(\"Error compiling schema, function code:\", sourceCode);\n        // console.log(\"\\n\\n\\n *** \\n\", sourceCode, this.opts)\n        throw e;\n    }\n    finally {\n        this._compilations.delete(sch);\n    }\n}\nexports.compileSchema = compileSchema;\nfunction resolveRef(root, baseId, ref) {\n    var _a;\n    ref = (0, resolve_1.resolveUrl)(this.opts.uriResolver, baseId, ref);\n    const schOrFunc = root.refs[ref];\n    if (schOrFunc)\n        return schOrFunc;\n    let _sch = resolve.call(this, root, ref);\n    if (_sch === undefined) {\n        const schema = (_a = root.localRefs) === null || _a === void 0 ? void 0 : _a[ref]; // TODO maybe localRefs should hold SchemaEnv\n        const { schemaId } = this.opts;\n        if (schema)\n            _sch = new SchemaEnv({ schema, schemaId, root, baseId });\n    }\n    if (_sch === undefined)\n        return;\n    return (root.refs[ref] = inlineOrCompile.call(this, _sch));\n}\nexports.resolveRef = resolveRef;\nfunction inlineOrCompile(sch) {\n    if ((0, resolve_1.inlineRef)(sch.schema, this.opts.inlineRefs))\n        return sch.schema;\n    return sch.validate ? sch : compileSchema.call(this, sch);\n}\n// Index of schema compilation in the currently compiled list\nfunction getCompilingSchema(schEnv) {\n    for (const sch of this._compilations) {\n        if (sameSchemaEnv(sch, schEnv))\n            return sch;\n    }\n}\nexports.getCompilingSchema = getCompilingSchema;\nfunction sameSchemaEnv(s1, s2) {\n    return s1.schema === s2.schema && s1.root === s2.root && s1.baseId === s2.baseId;\n}\n// resolve and compile the references ($ref)\n// TODO returns AnySchemaObject (if the schema can be inlined) or validation function\nfunction resolve(root, // information about the root schema for the current schema\nref // reference to resolve\n) {\n    let sch;\n    while (typeof (sch = this.refs[ref]) == \"string\")\n        ref = sch;\n    return sch || this.schemas[ref] || resolveSchema.call(this, root, ref);\n}\n// Resolve schema, its root and baseId\nfunction resolveSchema(root, // root object with properties schema, refs TODO below SchemaEnv is assigned to it\nref // reference to resolve\n) {\n    const p = this.opts.uriResolver.parse(ref);\n    const refPath = (0, resolve_1._getFullPath)(this.opts.uriResolver, p);\n    let baseId = (0, resolve_1.getFullPath)(this.opts.uriResolver, root.baseId, undefined);\n    // TODO `Object.keys(root.schema).length > 0` should not be needed - but removing breaks 2 tests\n    if (Object.keys(root.schema).length > 0 && refPath === baseId) {\n        return getJsonPointer.call(this, p, root);\n    }\n    const id = (0, resolve_1.normalizeId)(refPath);\n    const schOrRef = this.refs[id] || this.schemas[id];\n    if (typeof schOrRef == \"string\") {\n        const sch = resolveSchema.call(this, root, schOrRef);\n        if (typeof (sch === null || sch === void 0 ? void 0 : sch.schema) !== \"object\")\n            return;\n        return getJsonPointer.call(this, p, sch);\n    }\n    if (typeof (schOrRef === null || schOrRef === void 0 ? void 0 : schOrRef.schema) !== \"object\")\n        return;\n    if (!schOrRef.validate)\n        compileSchema.call(this, schOrRef);\n    if (id === (0, resolve_1.normalizeId)(ref)) {\n        const { schema } = schOrRef;\n        const { schemaId } = this.opts;\n        const schId = schema[schemaId];\n        if (schId)\n            baseId = (0, resolve_1.resolveUrl)(this.opts.uriResolver, baseId, schId);\n        return new SchemaEnv({ schema, schemaId, root, baseId });\n    }\n    return getJsonPointer.call(this, p, schOrRef);\n}\nexports.resolveSchema = resolveSchema;\nconst PREVENT_SCOPE_CHANGE = new Set([\n    \"properties\",\n    \"patternProperties\",\n    \"enum\",\n    \"dependencies\",\n    \"definitions\",\n]);\nfunction getJsonPointer(parsedRef, { baseId, schema, root }) {\n    var _a;\n    if (((_a = parsedRef.fragment) === null || _a === void 0 ? void 0 : _a[0]) !== \"/\")\n        return;\n    for (const part of parsedRef.fragment.slice(1).split(\"/\")) {\n        if (typeof schema === \"boolean\")\n            return;\n        const partSchema = schema[(0, util_1.unescapeFragment)(part)];\n        if (partSchema === undefined)\n            return;\n        schema = partSchema;\n        // TODO PREVENT_SCOPE_CHANGE could be defined in keyword def?\n        const schId = typeof schema === \"object\" && schema[this.opts.schemaId];\n        if (!PREVENT_SCOPE_CHANGE.has(part) && schId) {\n            baseId = (0, resolve_1.resolveUrl)(this.opts.uriResolver, baseId, schId);\n        }\n    }\n    let env;\n    if (typeof schema != \"boolean\" && schema.$ref && !(0, util_1.schemaHasRulesButRef)(schema, this.RULES)) {\n        const $ref = (0, resolve_1.resolveUrl)(this.opts.uriResolver, baseId, schema.$ref);\n        env = resolveSchema.call(this, root, $ref);\n    }\n    // even though resolution failed we need to return SchemaEnv to throw exception\n    // so that compileAsync loads missing schema.\n    const { schemaId } = this.opts;\n    env = env || new SchemaEnv({ schema, schemaId, root, baseId });\n    if (env.schema !== env.root.schema)\n        return env;\n    return undefined;\n}\n//# sourceMappingURL=index.js.map", "/** @license URI.js v4.4.1 (c) 2011 Gary Court. License: http://github.com/garycourt/uri-js */\n(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\n\ttypeof define === 'function' && define.amd ? define(['exports'], factory) :\n\t(factory((global.URI = global.URI || {})));\n}(this, (function (exports) { 'use strict';\n\nfunction merge() {\n    for (var _len = arguments.length, sets = Array(_len), _key = 0; _key < _len; _key++) {\n        sets[_key] = arguments[_key];\n    }\n\n    if (sets.length > 1) {\n        sets[0] = sets[0].slice(0, -1);\n        var xl = sets.length - 1;\n        for (var x = 1; x < xl; ++x) {\n            sets[x] = sets[x].slice(1, -1);\n        }\n        sets[xl] = sets[xl].slice(1);\n        return sets.join('');\n    } else {\n        return sets[0];\n    }\n}\nfunction subexp(str) {\n    return \"(?:\" + str + \")\";\n}\nfunction typeOf(o) {\n    return o === undefined ? \"undefined\" : o === null ? \"null\" : Object.prototype.toString.call(o).split(\" \").pop().split(\"]\").shift().toLowerCase();\n}\nfunction toUpperCase(str) {\n    return str.toUpperCase();\n}\nfunction toArray(obj) {\n    return obj !== undefined && obj !== null ? obj instanceof Array ? obj : typeof obj.length !== \"number\" || obj.split || obj.setInterval || obj.call ? [obj] : Array.prototype.slice.call(obj) : [];\n}\nfunction assign(target, source) {\n    var obj = target;\n    if (source) {\n        for (var key in source) {\n            obj[key] = source[key];\n        }\n    }\n    return obj;\n}\n\nfunction buildExps(isIRI) {\n    var ALPHA$$ = \"[A-Za-z]\",\n        CR$ = \"[\\\\x0D]\",\n        DIGIT$$ = \"[0-9]\",\n        DQUOTE$$ = \"[\\\\x22]\",\n        HEXDIG$$ = merge(DIGIT$$, \"[A-Fa-f]\"),\n        //case-insensitive\n    LF$$ = \"[\\\\x0A]\",\n        SP$$ = \"[\\\\x20]\",\n        PCT_ENCODED$ = subexp(subexp(\"%[EFef]\" + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$) + \"|\" + subexp(\"%[89A-Fa-f]\" + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$) + \"|\" + subexp(\"%\" + HEXDIG$$ + HEXDIG$$)),\n        //expanded\n    GEN_DELIMS$$ = \"[\\\\:\\\\/\\\\?\\\\#\\\\[\\\\]\\\\@]\",\n        SUB_DELIMS$$ = \"[\\\\!\\\\$\\\\&\\\\'\\\\(\\\\)\\\\*\\\\+\\\\,\\\\;\\\\=]\",\n        RESERVED$$ = merge(GEN_DELIMS$$, SUB_DELIMS$$),\n        UCSCHAR$$ = isIRI ? \"[\\\\xA0-\\\\u200D\\\\u2010-\\\\u2029\\\\u202F-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFEF]\" : \"[]\",\n        //subset, excludes bidi control characters\n    IPRIVATE$$ = isIRI ? \"[\\\\uE000-\\\\uF8FF]\" : \"[]\",\n        //subset\n    UNRESERVED$$ = merge(ALPHA$$, DIGIT$$, \"[\\\\-\\\\.\\\\_\\\\~]\", UCSCHAR$$),\n        SCHEME$ = subexp(ALPHA$$ + merge(ALPHA$$, DIGIT$$, \"[\\\\+\\\\-\\\\.]\") + \"*\"),\n        USERINFO$ = subexp(subexp(PCT_ENCODED$ + \"|\" + merge(UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:]\")) + \"*\"),\n        DEC_OCTET$ = subexp(subexp(\"25[0-5]\") + \"|\" + subexp(\"2[0-4]\" + DIGIT$$) + \"|\" + subexp(\"1\" + DIGIT$$ + DIGIT$$) + \"|\" + subexp(\"[1-9]\" + DIGIT$$) + \"|\" + DIGIT$$),\n        DEC_OCTET_RELAXED$ = subexp(subexp(\"25[0-5]\") + \"|\" + subexp(\"2[0-4]\" + DIGIT$$) + \"|\" + subexp(\"1\" + DIGIT$$ + DIGIT$$) + \"|\" + subexp(\"0?[1-9]\" + DIGIT$$) + \"|0?0?\" + DIGIT$$),\n        //relaxed parsing rules\n    IPV4ADDRESS$ = subexp(DEC_OCTET_RELAXED$ + \"\\\\.\" + DEC_OCTET_RELAXED$ + \"\\\\.\" + DEC_OCTET_RELAXED$ + \"\\\\.\" + DEC_OCTET_RELAXED$),\n        H16$ = subexp(HEXDIG$$ + \"{1,4}\"),\n        LS32$ = subexp(subexp(H16$ + \"\\\\:\" + H16$) + \"|\" + IPV4ADDRESS$),\n        IPV6ADDRESS1$ = subexp(subexp(H16$ + \"\\\\:\") + \"{6}\" + LS32$),\n        //                           6( h16 \":\" ) ls32\n    IPV6ADDRESS2$ = subexp(\"\\\\:\\\\:\" + subexp(H16$ + \"\\\\:\") + \"{5}\" + LS32$),\n        //                      \"::\" 5( h16 \":\" ) ls32\n    IPV6ADDRESS3$ = subexp(subexp(H16$) + \"?\\\\:\\\\:\" + subexp(H16$ + \"\\\\:\") + \"{4}\" + LS32$),\n        //[               h16 ] \"::\" 4( h16 \":\" ) ls32\n    IPV6ADDRESS4$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,1}\" + H16$) + \"?\\\\:\\\\:\" + subexp(H16$ + \"\\\\:\") + \"{3}\" + LS32$),\n        //[ *1( h16 \":\" ) h16 ] \"::\" 3( h16 \":\" ) ls32\n    IPV6ADDRESS5$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,2}\" + H16$) + \"?\\\\:\\\\:\" + subexp(H16$ + \"\\\\:\") + \"{2}\" + LS32$),\n        //[ *2( h16 \":\" ) h16 ] \"::\" 2( h16 \":\" ) ls32\n    IPV6ADDRESS6$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,3}\" + H16$) + \"?\\\\:\\\\:\" + H16$ + \"\\\\:\" + LS32$),\n        //[ *3( h16 \":\" ) h16 ] \"::\"    h16 \":\"   ls32\n    IPV6ADDRESS7$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,4}\" + H16$) + \"?\\\\:\\\\:\" + LS32$),\n        //[ *4( h16 \":\" ) h16 ] \"::\"              ls32\n    IPV6ADDRESS8$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,5}\" + H16$) + \"?\\\\:\\\\:\" + H16$),\n        //[ *5( h16 \":\" ) h16 ] \"::\"              h16\n    IPV6ADDRESS9$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,6}\" + H16$) + \"?\\\\:\\\\:\"),\n        //[ *6( h16 \":\" ) h16 ] \"::\"\n    IPV6ADDRESS$ = subexp([IPV6ADDRESS1$, IPV6ADDRESS2$, IPV6ADDRESS3$, IPV6ADDRESS4$, IPV6ADDRESS5$, IPV6ADDRESS6$, IPV6ADDRESS7$, IPV6ADDRESS8$, IPV6ADDRESS9$].join(\"|\")),\n        ZONEID$ = subexp(subexp(UNRESERVED$$ + \"|\" + PCT_ENCODED$) + \"+\"),\n        //RFC 6874\n    IPV6ADDRZ$ = subexp(IPV6ADDRESS$ + \"\\\\%25\" + ZONEID$),\n        //RFC 6874\n    IPV6ADDRZ_RELAXED$ = subexp(IPV6ADDRESS$ + subexp(\"\\\\%25|\\\\%(?!\" + HEXDIG$$ + \"{2})\") + ZONEID$),\n        //RFC 6874, with relaxed parsing rules\n    IPVFUTURE$ = subexp(\"[vV]\" + HEXDIG$$ + \"+\\\\.\" + merge(UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:]\") + \"+\"),\n        IP_LITERAL$ = subexp(\"\\\\[\" + subexp(IPV6ADDRZ_RELAXED$ + \"|\" + IPV6ADDRESS$ + \"|\" + IPVFUTURE$) + \"\\\\]\"),\n        //RFC 6874\n    REG_NAME$ = subexp(subexp(PCT_ENCODED$ + \"|\" + merge(UNRESERVED$$, SUB_DELIMS$$)) + \"*\"),\n        HOST$ = subexp(IP_LITERAL$ + \"|\" + IPV4ADDRESS$ + \"(?!\" + REG_NAME$ + \")\" + \"|\" + REG_NAME$),\n        PORT$ = subexp(DIGIT$$ + \"*\"),\n        AUTHORITY$ = subexp(subexp(USERINFO$ + \"@\") + \"?\" + HOST$ + subexp(\"\\\\:\" + PORT$) + \"?\"),\n        PCHAR$ = subexp(PCT_ENCODED$ + \"|\" + merge(UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:\\\\@]\")),\n        SEGMENT$ = subexp(PCHAR$ + \"*\"),\n        SEGMENT_NZ$ = subexp(PCHAR$ + \"+\"),\n        SEGMENT_NZ_NC$ = subexp(subexp(PCT_ENCODED$ + \"|\" + merge(UNRESERVED$$, SUB_DELIMS$$, \"[\\\\@]\")) + \"+\"),\n        PATH_ABEMPTY$ = subexp(subexp(\"\\\\/\" + SEGMENT$) + \"*\"),\n        PATH_ABSOLUTE$ = subexp(\"\\\\/\" + subexp(SEGMENT_NZ$ + PATH_ABEMPTY$) + \"?\"),\n        //simplified\n    PATH_NOSCHEME$ = subexp(SEGMENT_NZ_NC$ + PATH_ABEMPTY$),\n        //simplified\n    PATH_ROOTLESS$ = subexp(SEGMENT_NZ$ + PATH_ABEMPTY$),\n        //simplified\n    PATH_EMPTY$ = \"(?!\" + PCHAR$ + \")\",\n        PATH$ = subexp(PATH_ABEMPTY$ + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_NOSCHEME$ + \"|\" + PATH_ROOTLESS$ + \"|\" + PATH_EMPTY$),\n        QUERY$ = subexp(subexp(PCHAR$ + \"|\" + merge(\"[\\\\/\\\\?]\", IPRIVATE$$)) + \"*\"),\n        FRAGMENT$ = subexp(subexp(PCHAR$ + \"|[\\\\/\\\\?]\") + \"*\"),\n        HIER_PART$ = subexp(subexp(\"\\\\/\\\\/\" + AUTHORITY$ + PATH_ABEMPTY$) + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_ROOTLESS$ + \"|\" + PATH_EMPTY$),\n        URI$ = subexp(SCHEME$ + \"\\\\:\" + HIER_PART$ + subexp(\"\\\\?\" + QUERY$) + \"?\" + subexp(\"\\\\#\" + FRAGMENT$) + \"?\"),\n        RELATIVE_PART$ = subexp(subexp(\"\\\\/\\\\/\" + AUTHORITY$ + PATH_ABEMPTY$) + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_NOSCHEME$ + \"|\" + PATH_EMPTY$),\n        RELATIVE$ = subexp(RELATIVE_PART$ + subexp(\"\\\\?\" + QUERY$) + \"?\" + subexp(\"\\\\#\" + FRAGMENT$) + \"?\"),\n        URI_REFERENCE$ = subexp(URI$ + \"|\" + RELATIVE$),\n        ABSOLUTE_URI$ = subexp(SCHEME$ + \"\\\\:\" + HIER_PART$ + subexp(\"\\\\?\" + QUERY$) + \"?\"),\n        GENERIC_REF$ = \"^(\" + SCHEME$ + \")\\\\:\" + subexp(subexp(\"\\\\/\\\\/(\" + subexp(\"(\" + USERINFO$ + \")@\") + \"?(\" + HOST$ + \")\" + subexp(\"\\\\:(\" + PORT$ + \")\") + \"?)\") + \"?(\" + PATH_ABEMPTY$ + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_ROOTLESS$ + \"|\" + PATH_EMPTY$ + \")\") + subexp(\"\\\\?(\" + QUERY$ + \")\") + \"?\" + subexp(\"\\\\#(\" + FRAGMENT$ + \")\") + \"?$\",\n        RELATIVE_REF$ = \"^(){0}\" + subexp(subexp(\"\\\\/\\\\/(\" + subexp(\"(\" + USERINFO$ + \")@\") + \"?(\" + HOST$ + \")\" + subexp(\"\\\\:(\" + PORT$ + \")\") + \"?)\") + \"?(\" + PATH_ABEMPTY$ + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_NOSCHEME$ + \"|\" + PATH_EMPTY$ + \")\") + subexp(\"\\\\?(\" + QUERY$ + \")\") + \"?\" + subexp(\"\\\\#(\" + FRAGMENT$ + \")\") + \"?$\",\n        ABSOLUTE_REF$ = \"^(\" + SCHEME$ + \")\\\\:\" + subexp(subexp(\"\\\\/\\\\/(\" + subexp(\"(\" + USERINFO$ + \")@\") + \"?(\" + HOST$ + \")\" + subexp(\"\\\\:(\" + PORT$ + \")\") + \"?)\") + \"?(\" + PATH_ABEMPTY$ + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_ROOTLESS$ + \"|\" + PATH_EMPTY$ + \")\") + subexp(\"\\\\?(\" + QUERY$ + \")\") + \"?$\",\n        SAMEDOC_REF$ = \"^\" + subexp(\"\\\\#(\" + FRAGMENT$ + \")\") + \"?$\",\n        AUTHORITY_REF$ = \"^\" + subexp(\"(\" + USERINFO$ + \")@\") + \"?(\" + HOST$ + \")\" + subexp(\"\\\\:(\" + PORT$ + \")\") + \"?$\";\n    return {\n        NOT_SCHEME: new RegExp(merge(\"[^]\", ALPHA$$, DIGIT$$, \"[\\\\+\\\\-\\\\.]\"), \"g\"),\n        NOT_USERINFO: new RegExp(merge(\"[^\\\\%\\\\:]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n        NOT_HOST: new RegExp(merge(\"[^\\\\%\\\\[\\\\]\\\\:]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n        NOT_PATH: new RegExp(merge(\"[^\\\\%\\\\/\\\\:\\\\@]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n        NOT_PATH_NOSCHEME: new RegExp(merge(\"[^\\\\%\\\\/\\\\@]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n        NOT_QUERY: new RegExp(merge(\"[^\\\\%]\", UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:\\\\@\\\\/\\\\?]\", IPRIVATE$$), \"g\"),\n        NOT_FRAGMENT: new RegExp(merge(\"[^\\\\%]\", UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:\\\\@\\\\/\\\\?]\"), \"g\"),\n        ESCAPE: new RegExp(merge(\"[^]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n        UNRESERVED: new RegExp(UNRESERVED$$, \"g\"),\n        OTHER_CHARS: new RegExp(merge(\"[^\\\\%]\", UNRESERVED$$, RESERVED$$), \"g\"),\n        PCT_ENCODED: new RegExp(PCT_ENCODED$, \"g\"),\n        IPV4ADDRESS: new RegExp(\"^(\" + IPV4ADDRESS$ + \")$\"),\n        IPV6ADDRESS: new RegExp(\"^\\\\[?(\" + IPV6ADDRESS$ + \")\" + subexp(subexp(\"\\\\%25|\\\\%(?!\" + HEXDIG$$ + \"{2})\") + \"(\" + ZONEID$ + \")\") + \"?\\\\]?$\") //RFC 6874, with relaxed parsing rules\n    };\n}\nvar URI_PROTOCOL = buildExps(false);\n\nvar IRI_PROTOCOL = buildExps(true);\n\nvar slicedToArray = function () {\n  function sliceIterator(arr, i) {\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _e = undefined;\n\n    try {\n      for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n        _arr.push(_s.value);\n\n        if (i && _arr.length === i) break;\n      }\n    } catch (err) {\n      _d = true;\n      _e = err;\n    } finally {\n      try {\n        if (!_n && _i[\"return\"]) _i[\"return\"]();\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n\n    return _arr;\n  }\n\n  return function (arr, i) {\n    if (Array.isArray(arr)) {\n      return arr;\n    } else if (Symbol.iterator in Object(arr)) {\n      return sliceIterator(arr, i);\n    } else {\n      throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n    }\n  };\n}();\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar toConsumableArray = function (arr) {\n  if (Array.isArray(arr)) {\n    for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) arr2[i] = arr[i];\n\n    return arr2;\n  } else {\n    return Array.from(arr);\n  }\n};\n\n/** Highest positive signed 32-bit float value */\n\nvar maxInt = 2147483647; // aka. 0x7FFFFFFF or 2^31-1\n\n/** Bootstring parameters */\nvar base = 36;\nvar tMin = 1;\nvar tMax = 26;\nvar skew = 38;\nvar damp = 700;\nvar initialBias = 72;\nvar initialN = 128; // 0x80\nvar delimiter = '-'; // '\\x2D'\n\n/** Regular expressions */\nvar regexPunycode = /^xn--/;\nvar regexNonASCII = /[^\\0-\\x7E]/; // non-ASCII chars\nvar regexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g; // RFC 3490 separators\n\n/** Error messages */\nvar errors = {\n\t'overflow': 'Overflow: input needs wider integers to process',\n\t'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n\t'invalid-input': 'Invalid input'\n};\n\n/** Convenience shortcuts */\nvar baseMinusTMin = base - tMin;\nvar floor = Math.floor;\nvar stringFromCharCode = String.fromCharCode;\n\n/*--------------------------------------------------------------------------*/\n\n/**\n * A generic error utility function.\n * @private\n * @param {String} type The error type.\n * @returns {Error} Throws a `RangeError` with the applicable error message.\n */\nfunction error$1(type) {\n\tthrow new RangeError(errors[type]);\n}\n\n/**\n * A generic `Array#map` utility function.\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} callback The function that gets called for every array\n * item.\n * @returns {Array} A new array of values returned by the callback function.\n */\nfunction map(array, fn) {\n\tvar result = [];\n\tvar length = array.length;\n\twhile (length--) {\n\t\tresult[length] = fn(array[length]);\n\t}\n\treturn result;\n}\n\n/**\n * A simple `Array#map`-like wrapper to work with domain name strings or email\n * addresses.\n * @private\n * @param {String} domain The domain name or email address.\n * @param {Function} callback The function that gets called for every\n * character.\n * @returns {Array} A new string of characters returned by the callback\n * function.\n */\nfunction mapDomain(string, fn) {\n\tvar parts = string.split('@');\n\tvar result = '';\n\tif (parts.length > 1) {\n\t\t// In email addresses, only the domain name should be punycoded. Leave\n\t\t// the local part (i.e. everything up to `@`) intact.\n\t\tresult = parts[0] + '@';\n\t\tstring = parts[1];\n\t}\n\t// Avoid `split(regex)` for IE8 compatibility. See #17.\n\tstring = string.replace(regexSeparators, '\\x2E');\n\tvar labels = string.split('.');\n\tvar encoded = map(labels, fn).join('.');\n\treturn result + encoded;\n}\n\n/**\n * Creates an array containing the numeric code points of each Unicode\n * character in the string. While JavaScript uses UCS-2 internally,\n * this function will convert a pair of surrogate halves (each of which\n * UCS-2 exposes as separate characters) into a single code point,\n * matching UTF-16.\n * @see `punycode.ucs2.encode`\n * @see <https://mathiasbynens.be/notes/javascript-encoding>\n * @memberOf punycode.ucs2\n * @name decode\n * @param {String} string The Unicode input string (UCS-2).\n * @returns {Array} The new array of code points.\n */\nfunction ucs2decode(string) {\n\tvar output = [];\n\tvar counter = 0;\n\tvar length = string.length;\n\twhile (counter < length) {\n\t\tvar value = string.charCodeAt(counter++);\n\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t// It's a high surrogate, and there is a next character.\n\t\t\tvar extra = string.charCodeAt(counter++);\n\t\t\tif ((extra & 0xFC00) == 0xDC00) {\n\t\t\t\t// Low surrogate.\n\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t} else {\n\t\t\t\t// It's an unmatched surrogate; only append this code unit, in case the\n\t\t\t\t// next code unit is the high surrogate of a surrogate pair.\n\t\t\t\toutput.push(value);\n\t\t\t\tcounter--;\n\t\t\t}\n\t\t} else {\n\t\t\toutput.push(value);\n\t\t}\n\t}\n\treturn output;\n}\n\n/**\n * Creates a string based on an array of numeric code points.\n * @see `punycode.ucs2.decode`\n * @memberOf punycode.ucs2\n * @name encode\n * @param {Array} codePoints The array of numeric code points.\n * @returns {String} The new Unicode string (UCS-2).\n */\nvar ucs2encode = function ucs2encode(array) {\n\treturn String.fromCodePoint.apply(String, toConsumableArray(array));\n};\n\n/**\n * Converts a basic code point into a digit/integer.\n * @see `digitToBasic()`\n * @private\n * @param {Number} codePoint The basic numeric code point value.\n * @returns {Number} The numeric value of a basic code point (for use in\n * representing integers) in the range `0` to `base - 1`, or `base` if\n * the code point does not represent a value.\n */\nvar basicToDigit = function basicToDigit(codePoint) {\n\tif (codePoint - 0x30 < 0x0A) {\n\t\treturn codePoint - 0x16;\n\t}\n\tif (codePoint - 0x41 < 0x1A) {\n\t\treturn codePoint - 0x41;\n\t}\n\tif (codePoint - 0x61 < 0x1A) {\n\t\treturn codePoint - 0x61;\n\t}\n\treturn base;\n};\n\n/**\n * Converts a digit/integer into a basic code point.\n * @see `basicToDigit()`\n * @private\n * @param {Number} digit The numeric value of a basic code point.\n * @returns {Number} The basic code point whose value (when used for\n * representing integers) is `digit`, which needs to be in the range\n * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n * used; else, the lowercase form is used. The behavior is undefined\n * if `flag` is non-zero and `digit` has no uppercase form.\n */\nvar digitToBasic = function digitToBasic(digit, flag) {\n\t//  0..25 map to ASCII a..z or A..Z\n\t// 26..35 map to ASCII 0..9\n\treturn digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n};\n\n/**\n * Bias adaptation function as per section 3.4 of RFC 3492.\n * https://tools.ietf.org/html/rfc3492#section-3.4\n * @private\n */\nvar adapt = function adapt(delta, numPoints, firstTime) {\n\tvar k = 0;\n\tdelta = firstTime ? floor(delta / damp) : delta >> 1;\n\tdelta += floor(delta / numPoints);\n\tfor (; /* no initialization */delta > baseMinusTMin * tMax >> 1; k += base) {\n\t\tdelta = floor(delta / baseMinusTMin);\n\t}\n\treturn floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n};\n\n/**\n * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n * symbols.\n * @memberOf punycode\n * @param {String} input The Punycode string of ASCII-only symbols.\n * @returns {String} The resulting string of Unicode symbols.\n */\nvar decode = function decode(input) {\n\t// Don't use UCS-2.\n\tvar output = [];\n\tvar inputLength = input.length;\n\tvar i = 0;\n\tvar n = initialN;\n\tvar bias = initialBias;\n\n\t// Handle the basic code points: let `basic` be the number of input code\n\t// points before the last delimiter, or `0` if there is none, then copy\n\t// the first basic code points to the output.\n\n\tvar basic = input.lastIndexOf(delimiter);\n\tif (basic < 0) {\n\t\tbasic = 0;\n\t}\n\n\tfor (var j = 0; j < basic; ++j) {\n\t\t// if it's not a basic code point\n\t\tif (input.charCodeAt(j) >= 0x80) {\n\t\t\terror$1('not-basic');\n\t\t}\n\t\toutput.push(input.charCodeAt(j));\n\t}\n\n\t// Main decoding loop: start just after the last delimiter if any basic code\n\t// points were copied; start at the beginning otherwise.\n\n\tfor (var index = basic > 0 ? basic + 1 : 0; index < inputLength;) /* no final expression */{\n\n\t\t// `index` is the index of the next character to be consumed.\n\t\t// Decode a generalized variable-length integer into `delta`,\n\t\t// which gets added to `i`. The overflow checking is easier\n\t\t// if we increase `i` as we go, then subtract off its starting\n\t\t// value at the end to obtain `delta`.\n\t\tvar oldi = i;\n\t\tfor (var w = 1, k = base;; /* no condition */k += base) {\n\n\t\t\tif (index >= inputLength) {\n\t\t\t\terror$1('invalid-input');\n\t\t\t}\n\n\t\t\tvar digit = basicToDigit(input.charCodeAt(index++));\n\n\t\t\tif (digit >= base || digit > floor((maxInt - i) / w)) {\n\t\t\t\terror$1('overflow');\n\t\t\t}\n\n\t\t\ti += digit * w;\n\t\t\tvar t = k <= bias ? tMin : k >= bias + tMax ? tMax : k - bias;\n\n\t\t\tif (digit < t) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tvar baseMinusT = base - t;\n\t\t\tif (w > floor(maxInt / baseMinusT)) {\n\t\t\t\terror$1('overflow');\n\t\t\t}\n\n\t\t\tw *= baseMinusT;\n\t\t}\n\n\t\tvar out = output.length + 1;\n\t\tbias = adapt(i - oldi, out, oldi == 0);\n\n\t\t// `i` was supposed to wrap around from `out` to `0`,\n\t\t// incrementing `n` each time, so we'll fix that now:\n\t\tif (floor(i / out) > maxInt - n) {\n\t\t\terror$1('overflow');\n\t\t}\n\n\t\tn += floor(i / out);\n\t\ti %= out;\n\n\t\t// Insert `n` at position `i` of the output.\n\t\toutput.splice(i++, 0, n);\n\t}\n\n\treturn String.fromCodePoint.apply(String, output);\n};\n\n/**\n * Converts a string of Unicode symbols (e.g. a domain name label) to a\n * Punycode string of ASCII-only symbols.\n * @memberOf punycode\n * @param {String} input The string of Unicode symbols.\n * @returns {String} The resulting Punycode string of ASCII-only symbols.\n */\nvar encode = function encode(input) {\n\tvar output = [];\n\n\t// Convert the input in UCS-2 to an array of Unicode code points.\n\tinput = ucs2decode(input);\n\n\t// Cache the length.\n\tvar inputLength = input.length;\n\n\t// Initialize the state.\n\tvar n = initialN;\n\tvar delta = 0;\n\tvar bias = initialBias;\n\n\t// Handle the basic code points.\n\tvar _iteratorNormalCompletion = true;\n\tvar _didIteratorError = false;\n\tvar _iteratorError = undefined;\n\n\ttry {\n\t\tfor (var _iterator = input[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n\t\t\tvar _currentValue2 = _step.value;\n\n\t\t\tif (_currentValue2 < 0x80) {\n\t\t\t\toutput.push(stringFromCharCode(_currentValue2));\n\t\t\t}\n\t\t}\n\t} catch (err) {\n\t\t_didIteratorError = true;\n\t\t_iteratorError = err;\n\t} finally {\n\t\ttry {\n\t\t\tif (!_iteratorNormalCompletion && _iterator.return) {\n\t\t\t\t_iterator.return();\n\t\t\t}\n\t\t} finally {\n\t\t\tif (_didIteratorError) {\n\t\t\t\tthrow _iteratorError;\n\t\t\t}\n\t\t}\n\t}\n\n\tvar basicLength = output.length;\n\tvar handledCPCount = basicLength;\n\n\t// `handledCPCount` is the number of code points that have been handled;\n\t// `basicLength` is the number of basic code points.\n\n\t// Finish the basic string with a delimiter unless it's empty.\n\tif (basicLength) {\n\t\toutput.push(delimiter);\n\t}\n\n\t// Main encoding loop:\n\twhile (handledCPCount < inputLength) {\n\n\t\t// All non-basic code points < n have been handled already. Find the next\n\t\t// larger one:\n\t\tvar m = maxInt;\n\t\tvar _iteratorNormalCompletion2 = true;\n\t\tvar _didIteratorError2 = false;\n\t\tvar _iteratorError2 = undefined;\n\n\t\ttry {\n\t\t\tfor (var _iterator2 = input[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n\t\t\t\tvar currentValue = _step2.value;\n\n\t\t\t\tif (currentValue >= n && currentValue < m) {\n\t\t\t\t\tm = currentValue;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n\t\t\t// but guard against overflow.\n\t\t} catch (err) {\n\t\t\t_didIteratorError2 = true;\n\t\t\t_iteratorError2 = err;\n\t\t} finally {\n\t\t\ttry {\n\t\t\t\tif (!_iteratorNormalCompletion2 && _iterator2.return) {\n\t\t\t\t\t_iterator2.return();\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tif (_didIteratorError2) {\n\t\t\t\t\tthrow _iteratorError2;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tvar handledCPCountPlusOne = handledCPCount + 1;\n\t\tif (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n\t\t\terror$1('overflow');\n\t\t}\n\n\t\tdelta += (m - n) * handledCPCountPlusOne;\n\t\tn = m;\n\n\t\tvar _iteratorNormalCompletion3 = true;\n\t\tvar _didIteratorError3 = false;\n\t\tvar _iteratorError3 = undefined;\n\n\t\ttry {\n\t\t\tfor (var _iterator3 = input[Symbol.iterator](), _step3; !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done); _iteratorNormalCompletion3 = true) {\n\t\t\t\tvar _currentValue = _step3.value;\n\n\t\t\t\tif (_currentValue < n && ++delta > maxInt) {\n\t\t\t\t\terror$1('overflow');\n\t\t\t\t}\n\t\t\t\tif (_currentValue == n) {\n\t\t\t\t\t// Represent delta as a generalized variable-length integer.\n\t\t\t\t\tvar q = delta;\n\t\t\t\t\tfor (var k = base;; /* no condition */k += base) {\n\t\t\t\t\t\tvar t = k <= bias ? tMin : k >= bias + tMax ? tMax : k - bias;\n\t\t\t\t\t\tif (q < t) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar qMinusT = q - t;\n\t\t\t\t\t\tvar baseMinusT = base - t;\n\t\t\t\t\t\toutput.push(stringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0)));\n\t\t\t\t\t\tq = floor(qMinusT / baseMinusT);\n\t\t\t\t\t}\n\n\t\t\t\t\toutput.push(stringFromCharCode(digitToBasic(q, 0)));\n\t\t\t\t\tbias = adapt(delta, handledCPCountPlusOne, handledCPCount == basicLength);\n\t\t\t\t\tdelta = 0;\n\t\t\t\t\t++handledCPCount;\n\t\t\t\t}\n\t\t\t}\n\t\t} catch (err) {\n\t\t\t_didIteratorError3 = true;\n\t\t\t_iteratorError3 = err;\n\t\t} finally {\n\t\t\ttry {\n\t\t\t\tif (!_iteratorNormalCompletion3 && _iterator3.return) {\n\t\t\t\t\t_iterator3.return();\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tif (_didIteratorError3) {\n\t\t\t\t\tthrow _iteratorError3;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t++delta;\n\t\t++n;\n\t}\n\treturn output.join('');\n};\n\n/**\n * Converts a Punycode string representing a domain name or an email address\n * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n * it doesn't matter if you call it on a string that has already been\n * converted to Unicode.\n * @memberOf punycode\n * @param {String} input The Punycoded domain name or email address to\n * convert to Unicode.\n * @returns {String} The Unicode representation of the given Punycode\n * string.\n */\nvar toUnicode = function toUnicode(input) {\n\treturn mapDomain(input, function (string) {\n\t\treturn regexPunycode.test(string) ? decode(string.slice(4).toLowerCase()) : string;\n\t});\n};\n\n/**\n * Converts a Unicode string representing a domain name or an email address to\n * Punycode. Only the non-ASCII parts of the domain name will be converted,\n * i.e. it doesn't matter if you call it with a domain that's already in\n * ASCII.\n * @memberOf punycode\n * @param {String} input The domain name or email address to convert, as a\n * Unicode string.\n * @returns {String} The Punycode representation of the given domain name or\n * email address.\n */\nvar toASCII = function toASCII(input) {\n\treturn mapDomain(input, function (string) {\n\t\treturn regexNonASCII.test(string) ? 'xn--' + encode(string) : string;\n\t});\n};\n\n/*--------------------------------------------------------------------------*/\n\n/** Define the public API */\nvar punycode = {\n\t/**\n  * A string representing the current Punycode.js version number.\n  * @memberOf punycode\n  * @type String\n  */\n\t'version': '2.1.0',\n\t/**\n  * An object of methods to convert from JavaScript's internal character\n  * representation (UCS-2) to Unicode code points, and back.\n  * @see <https://mathiasbynens.be/notes/javascript-encoding>\n  * @memberOf punycode\n  * @type Object\n  */\n\t'ucs2': {\n\t\t'decode': ucs2decode,\n\t\t'encode': ucs2encode\n\t},\n\t'decode': decode,\n\t'encode': encode,\n\t'toASCII': toASCII,\n\t'toUnicode': toUnicode\n};\n\n/**\n * URI.js\n *\n * @fileoverview An RFC 3986 compliant, scheme extendable URI parsing/validating/resolving library for JavaScript.\n * <AUTHOR> href=\"mailto:<EMAIL>\">Gary Court</a>\n * @see http://github.com/garycourt/uri-js\n */\n/**\n * Copyright 2011 Gary Court. All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification, are\n * permitted provided that the following conditions are met:\n *\n *    1. Redistributions of source code must retain the above copyright notice, this list of\n *       conditions and the following disclaimer.\n *\n *    2. Redistributions in binary form must reproduce the above copyright notice, this list\n *       of conditions and the following disclaimer in the documentation and/or other materials\n *       provided with the distribution.\n *\n * THIS SOFTWARE IS PROVIDED BY GARY COURT ``AS IS'' AND ANY EXPRESS OR IMPLIED\n * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND\n * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL GARY COURT OR\n * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\n * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF\n * ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n *\n * The views and conclusions contained in the software and documentation are those of the\n * authors and should not be interpreted as representing official policies, either expressed\n * or implied, of Gary Court.\n */\nvar SCHEMES = {};\nfunction pctEncChar(chr) {\n    var c = chr.charCodeAt(0);\n    var e = void 0;\n    if (c < 16) e = \"%0\" + c.toString(16).toUpperCase();else if (c < 128) e = \"%\" + c.toString(16).toUpperCase();else if (c < 2048) e = \"%\" + (c >> 6 | 192).toString(16).toUpperCase() + \"%\" + (c & 63 | 128).toString(16).toUpperCase();else e = \"%\" + (c >> 12 | 224).toString(16).toUpperCase() + \"%\" + (c >> 6 & 63 | 128).toString(16).toUpperCase() + \"%\" + (c & 63 | 128).toString(16).toUpperCase();\n    return e;\n}\nfunction pctDecChars(str) {\n    var newStr = \"\";\n    var i = 0;\n    var il = str.length;\n    while (i < il) {\n        var c = parseInt(str.substr(i + 1, 2), 16);\n        if (c < 128) {\n            newStr += String.fromCharCode(c);\n            i += 3;\n        } else if (c >= 194 && c < 224) {\n            if (il - i >= 6) {\n                var c2 = parseInt(str.substr(i + 4, 2), 16);\n                newStr += String.fromCharCode((c & 31) << 6 | c2 & 63);\n            } else {\n                newStr += str.substr(i, 6);\n            }\n            i += 6;\n        } else if (c >= 224) {\n            if (il - i >= 9) {\n                var _c = parseInt(str.substr(i + 4, 2), 16);\n                var c3 = parseInt(str.substr(i + 7, 2), 16);\n                newStr += String.fromCharCode((c & 15) << 12 | (_c & 63) << 6 | c3 & 63);\n            } else {\n                newStr += str.substr(i, 9);\n            }\n            i += 9;\n        } else {\n            newStr += str.substr(i, 3);\n            i += 3;\n        }\n    }\n    return newStr;\n}\nfunction _normalizeComponentEncoding(components, protocol) {\n    function decodeUnreserved(str) {\n        var decStr = pctDecChars(str);\n        return !decStr.match(protocol.UNRESERVED) ? str : decStr;\n    }\n    if (components.scheme) components.scheme = String(components.scheme).replace(protocol.PCT_ENCODED, decodeUnreserved).toLowerCase().replace(protocol.NOT_SCHEME, \"\");\n    if (components.userinfo !== undefined) components.userinfo = String(components.userinfo).replace(protocol.PCT_ENCODED, decodeUnreserved).replace(protocol.NOT_USERINFO, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n    if (components.host !== undefined) components.host = String(components.host).replace(protocol.PCT_ENCODED, decodeUnreserved).toLowerCase().replace(protocol.NOT_HOST, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n    if (components.path !== undefined) components.path = String(components.path).replace(protocol.PCT_ENCODED, decodeUnreserved).replace(components.scheme ? protocol.NOT_PATH : protocol.NOT_PATH_NOSCHEME, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n    if (components.query !== undefined) components.query = String(components.query).replace(protocol.PCT_ENCODED, decodeUnreserved).replace(protocol.NOT_QUERY, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n    if (components.fragment !== undefined) components.fragment = String(components.fragment).replace(protocol.PCT_ENCODED, decodeUnreserved).replace(protocol.NOT_FRAGMENT, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n    return components;\n}\n\nfunction _stripLeadingZeros(str) {\n    return str.replace(/^0*(.*)/, \"$1\") || \"0\";\n}\nfunction _normalizeIPv4(host, protocol) {\n    var matches = host.match(protocol.IPV4ADDRESS) || [];\n\n    var _matches = slicedToArray(matches, 2),\n        address = _matches[1];\n\n    if (address) {\n        return address.split(\".\").map(_stripLeadingZeros).join(\".\");\n    } else {\n        return host;\n    }\n}\nfunction _normalizeIPv6(host, protocol) {\n    var matches = host.match(protocol.IPV6ADDRESS) || [];\n\n    var _matches2 = slicedToArray(matches, 3),\n        address = _matches2[1],\n        zone = _matches2[2];\n\n    if (address) {\n        var _address$toLowerCase$ = address.toLowerCase().split('::').reverse(),\n            _address$toLowerCase$2 = slicedToArray(_address$toLowerCase$, 2),\n            last = _address$toLowerCase$2[0],\n            first = _address$toLowerCase$2[1];\n\n        var firstFields = first ? first.split(\":\").map(_stripLeadingZeros) : [];\n        var lastFields = last.split(\":\").map(_stripLeadingZeros);\n        var isLastFieldIPv4Address = protocol.IPV4ADDRESS.test(lastFields[lastFields.length - 1]);\n        var fieldCount = isLastFieldIPv4Address ? 7 : 8;\n        var lastFieldsStart = lastFields.length - fieldCount;\n        var fields = Array(fieldCount);\n        for (var x = 0; x < fieldCount; ++x) {\n            fields[x] = firstFields[x] || lastFields[lastFieldsStart + x] || '';\n        }\n        if (isLastFieldIPv4Address) {\n            fields[fieldCount - 1] = _normalizeIPv4(fields[fieldCount - 1], protocol);\n        }\n        var allZeroFields = fields.reduce(function (acc, field, index) {\n            if (!field || field === \"0\") {\n                var lastLongest = acc[acc.length - 1];\n                if (lastLongest && lastLongest.index + lastLongest.length === index) {\n                    lastLongest.length++;\n                } else {\n                    acc.push({ index: index, length: 1 });\n                }\n            }\n            return acc;\n        }, []);\n        var longestZeroFields = allZeroFields.sort(function (a, b) {\n            return b.length - a.length;\n        })[0];\n        var newHost = void 0;\n        if (longestZeroFields && longestZeroFields.length > 1) {\n            var newFirst = fields.slice(0, longestZeroFields.index);\n            var newLast = fields.slice(longestZeroFields.index + longestZeroFields.length);\n            newHost = newFirst.join(\":\") + \"::\" + newLast.join(\":\");\n        } else {\n            newHost = fields.join(\":\");\n        }\n        if (zone) {\n            newHost += \"%\" + zone;\n        }\n        return newHost;\n    } else {\n        return host;\n    }\n}\nvar URI_PARSE = /^(?:([^:\\/?#]+):)?(?:\\/\\/((?:([^\\/?#@]*)@)?(\\[[^\\/?#\\]]+\\]|[^\\/?#:]*)(?:\\:(\\d*))?))?([^?#]*)(?:\\?([^#]*))?(?:#((?:.|\\n|\\r)*))?/i;\nvar NO_MATCH_IS_UNDEFINED = \"\".match(/(){0}/)[1] === undefined;\nfunction parse(uriString) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    var components = {};\n    var protocol = options.iri !== false ? IRI_PROTOCOL : URI_PROTOCOL;\n    if (options.reference === \"suffix\") uriString = (options.scheme ? options.scheme + \":\" : \"\") + \"//\" + uriString;\n    var matches = uriString.match(URI_PARSE);\n    if (matches) {\n        if (NO_MATCH_IS_UNDEFINED) {\n            //store each component\n            components.scheme = matches[1];\n            components.userinfo = matches[3];\n            components.host = matches[4];\n            components.port = parseInt(matches[5], 10);\n            components.path = matches[6] || \"\";\n            components.query = matches[7];\n            components.fragment = matches[8];\n            //fix port number\n            if (isNaN(components.port)) {\n                components.port = matches[5];\n            }\n        } else {\n            //IE FIX for improper RegExp matching\n            //store each component\n            components.scheme = matches[1] || undefined;\n            components.userinfo = uriString.indexOf(\"@\") !== -1 ? matches[3] : undefined;\n            components.host = uriString.indexOf(\"//\") !== -1 ? matches[4] : undefined;\n            components.port = parseInt(matches[5], 10);\n            components.path = matches[6] || \"\";\n            components.query = uriString.indexOf(\"?\") !== -1 ? matches[7] : undefined;\n            components.fragment = uriString.indexOf(\"#\") !== -1 ? matches[8] : undefined;\n            //fix port number\n            if (isNaN(components.port)) {\n                components.port = uriString.match(/\\/\\/(?:.|\\n)*\\:(?:\\/|\\?|\\#|$)/) ? matches[4] : undefined;\n            }\n        }\n        if (components.host) {\n            //normalize IP hosts\n            components.host = _normalizeIPv6(_normalizeIPv4(components.host, protocol), protocol);\n        }\n        //determine reference type\n        if (components.scheme === undefined && components.userinfo === undefined && components.host === undefined && components.port === undefined && !components.path && components.query === undefined) {\n            components.reference = \"same-document\";\n        } else if (components.scheme === undefined) {\n            components.reference = \"relative\";\n        } else if (components.fragment === undefined) {\n            components.reference = \"absolute\";\n        } else {\n            components.reference = \"uri\";\n        }\n        //check for reference errors\n        if (options.reference && options.reference !== \"suffix\" && options.reference !== components.reference) {\n            components.error = components.error || \"URI is not a \" + options.reference + \" reference.\";\n        }\n        //find scheme handler\n        var schemeHandler = SCHEMES[(options.scheme || components.scheme || \"\").toLowerCase()];\n        //check if scheme can't handle IRIs\n        if (!options.unicodeSupport && (!schemeHandler || !schemeHandler.unicodeSupport)) {\n            //if host component is a domain name\n            if (components.host && (options.domainHost || schemeHandler && schemeHandler.domainHost)) {\n                //convert Unicode IDN -> ASCII IDN\n                try {\n                    components.host = punycode.toASCII(components.host.replace(protocol.PCT_ENCODED, pctDecChars).toLowerCase());\n                } catch (e) {\n                    components.error = components.error || \"Host's domain name can not be converted to ASCII via punycode: \" + e;\n                }\n            }\n            //convert IRI -> URI\n            _normalizeComponentEncoding(components, URI_PROTOCOL);\n        } else {\n            //normalize encodings\n            _normalizeComponentEncoding(components, protocol);\n        }\n        //perform scheme specific parsing\n        if (schemeHandler && schemeHandler.parse) {\n            schemeHandler.parse(components, options);\n        }\n    } else {\n        components.error = components.error || \"URI can not be parsed.\";\n    }\n    return components;\n}\n\nfunction _recomposeAuthority(components, options) {\n    var protocol = options.iri !== false ? IRI_PROTOCOL : URI_PROTOCOL;\n    var uriTokens = [];\n    if (components.userinfo !== undefined) {\n        uriTokens.push(components.userinfo);\n        uriTokens.push(\"@\");\n    }\n    if (components.host !== undefined) {\n        //normalize IP hosts, add brackets and escape zone separator for IPv6\n        uriTokens.push(_normalizeIPv6(_normalizeIPv4(String(components.host), protocol), protocol).replace(protocol.IPV6ADDRESS, function (_, $1, $2) {\n            return \"[\" + $1 + ($2 ? \"%25\" + $2 : \"\") + \"]\";\n        }));\n    }\n    if (typeof components.port === \"number\" || typeof components.port === \"string\") {\n        uriTokens.push(\":\");\n        uriTokens.push(String(components.port));\n    }\n    return uriTokens.length ? uriTokens.join(\"\") : undefined;\n}\n\nvar RDS1 = /^\\.\\.?\\//;\nvar RDS2 = /^\\/\\.(\\/|$)/;\nvar RDS3 = /^\\/\\.\\.(\\/|$)/;\nvar RDS5 = /^\\/?(?:.|\\n)*?(?=\\/|$)/;\nfunction removeDotSegments(input) {\n    var output = [];\n    while (input.length) {\n        if (input.match(RDS1)) {\n            input = input.replace(RDS1, \"\");\n        } else if (input.match(RDS2)) {\n            input = input.replace(RDS2, \"/\");\n        } else if (input.match(RDS3)) {\n            input = input.replace(RDS3, \"/\");\n            output.pop();\n        } else if (input === \".\" || input === \"..\") {\n            input = \"\";\n        } else {\n            var im = input.match(RDS5);\n            if (im) {\n                var s = im[0];\n                input = input.slice(s.length);\n                output.push(s);\n            } else {\n                throw new Error(\"Unexpected dot segment condition\");\n            }\n        }\n    }\n    return output.join(\"\");\n}\n\nfunction serialize(components) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    var protocol = options.iri ? IRI_PROTOCOL : URI_PROTOCOL;\n    var uriTokens = [];\n    //find scheme handler\n    var schemeHandler = SCHEMES[(options.scheme || components.scheme || \"\").toLowerCase()];\n    //perform scheme specific serialization\n    if (schemeHandler && schemeHandler.serialize) schemeHandler.serialize(components, options);\n    if (components.host) {\n        //if host component is an IPv6 address\n        if (protocol.IPV6ADDRESS.test(components.host)) {}\n        //TODO: normalize IPv6 address as per RFC 5952\n\n        //if host component is a domain name\n        else if (options.domainHost || schemeHandler && schemeHandler.domainHost) {\n                //convert IDN via punycode\n                try {\n                    components.host = !options.iri ? punycode.toASCII(components.host.replace(protocol.PCT_ENCODED, pctDecChars).toLowerCase()) : punycode.toUnicode(components.host);\n                } catch (e) {\n                    components.error = components.error || \"Host's domain name can not be converted to \" + (!options.iri ? \"ASCII\" : \"Unicode\") + \" via punycode: \" + e;\n                }\n            }\n    }\n    //normalize encoding\n    _normalizeComponentEncoding(components, protocol);\n    if (options.reference !== \"suffix\" && components.scheme) {\n        uriTokens.push(components.scheme);\n        uriTokens.push(\":\");\n    }\n    var authority = _recomposeAuthority(components, options);\n    if (authority !== undefined) {\n        if (options.reference !== \"suffix\") {\n            uriTokens.push(\"//\");\n        }\n        uriTokens.push(authority);\n        if (components.path && components.path.charAt(0) !== \"/\") {\n            uriTokens.push(\"/\");\n        }\n    }\n    if (components.path !== undefined) {\n        var s = components.path;\n        if (!options.absolutePath && (!schemeHandler || !schemeHandler.absolutePath)) {\n            s = removeDotSegments(s);\n        }\n        if (authority === undefined) {\n            s = s.replace(/^\\/\\//, \"/%2F\"); //don't allow the path to start with \"//\"\n        }\n        uriTokens.push(s);\n    }\n    if (components.query !== undefined) {\n        uriTokens.push(\"?\");\n        uriTokens.push(components.query);\n    }\n    if (components.fragment !== undefined) {\n        uriTokens.push(\"#\");\n        uriTokens.push(components.fragment);\n    }\n    return uriTokens.join(\"\"); //merge tokens into a string\n}\n\nfunction resolveComponents(base, relative) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var skipNormalization = arguments[3];\n\n    var target = {};\n    if (!skipNormalization) {\n        base = parse(serialize(base, options), options); //normalize base components\n        relative = parse(serialize(relative, options), options); //normalize relative components\n    }\n    options = options || {};\n    if (!options.tolerant && relative.scheme) {\n        target.scheme = relative.scheme;\n        //target.authority = relative.authority;\n        target.userinfo = relative.userinfo;\n        target.host = relative.host;\n        target.port = relative.port;\n        target.path = removeDotSegments(relative.path || \"\");\n        target.query = relative.query;\n    } else {\n        if (relative.userinfo !== undefined || relative.host !== undefined || relative.port !== undefined) {\n            //target.authority = relative.authority;\n            target.userinfo = relative.userinfo;\n            target.host = relative.host;\n            target.port = relative.port;\n            target.path = removeDotSegments(relative.path || \"\");\n            target.query = relative.query;\n        } else {\n            if (!relative.path) {\n                target.path = base.path;\n                if (relative.query !== undefined) {\n                    target.query = relative.query;\n                } else {\n                    target.query = base.query;\n                }\n            } else {\n                if (relative.path.charAt(0) === \"/\") {\n                    target.path = removeDotSegments(relative.path);\n                } else {\n                    if ((base.userinfo !== undefined || base.host !== undefined || base.port !== undefined) && !base.path) {\n                        target.path = \"/\" + relative.path;\n                    } else if (!base.path) {\n                        target.path = relative.path;\n                    } else {\n                        target.path = base.path.slice(0, base.path.lastIndexOf(\"/\") + 1) + relative.path;\n                    }\n                    target.path = removeDotSegments(target.path);\n                }\n                target.query = relative.query;\n            }\n            //target.authority = base.authority;\n            target.userinfo = base.userinfo;\n            target.host = base.host;\n            target.port = base.port;\n        }\n        target.scheme = base.scheme;\n    }\n    target.fragment = relative.fragment;\n    return target;\n}\n\nfunction resolve(baseURI, relativeURI, options) {\n    var schemelessOptions = assign({ scheme: 'null' }, options);\n    return serialize(resolveComponents(parse(baseURI, schemelessOptions), parse(relativeURI, schemelessOptions), schemelessOptions, true), schemelessOptions);\n}\n\nfunction normalize(uri, options) {\n    if (typeof uri === \"string\") {\n        uri = serialize(parse(uri, options), options);\n    } else if (typeOf(uri) === \"object\") {\n        uri = parse(serialize(uri, options), options);\n    }\n    return uri;\n}\n\nfunction equal(uriA, uriB, options) {\n    if (typeof uriA === \"string\") {\n        uriA = serialize(parse(uriA, options), options);\n    } else if (typeOf(uriA) === \"object\") {\n        uriA = serialize(uriA, options);\n    }\n    if (typeof uriB === \"string\") {\n        uriB = serialize(parse(uriB, options), options);\n    } else if (typeOf(uriB) === \"object\") {\n        uriB = serialize(uriB, options);\n    }\n    return uriA === uriB;\n}\n\nfunction escapeComponent(str, options) {\n    return str && str.toString().replace(!options || !options.iri ? URI_PROTOCOL.ESCAPE : IRI_PROTOCOL.ESCAPE, pctEncChar);\n}\n\nfunction unescapeComponent(str, options) {\n    return str && str.toString().replace(!options || !options.iri ? URI_PROTOCOL.PCT_ENCODED : IRI_PROTOCOL.PCT_ENCODED, pctDecChars);\n}\n\nvar handler = {\n    scheme: \"http\",\n    domainHost: true,\n    parse: function parse(components, options) {\n        //report missing host\n        if (!components.host) {\n            components.error = components.error || \"HTTP URIs must have a host.\";\n        }\n        return components;\n    },\n    serialize: function serialize(components, options) {\n        var secure = String(components.scheme).toLowerCase() === \"https\";\n        //normalize the default port\n        if (components.port === (secure ? 443 : 80) || components.port === \"\") {\n            components.port = undefined;\n        }\n        //normalize the empty path\n        if (!components.path) {\n            components.path = \"/\";\n        }\n        //NOTE: We do not parse query strings for HTTP URIs\n        //as WWW Form Url Encoded query strings are part of the HTML4+ spec,\n        //and not the HTTP spec.\n        return components;\n    }\n};\n\nvar handler$1 = {\n    scheme: \"https\",\n    domainHost: handler.domainHost,\n    parse: handler.parse,\n    serialize: handler.serialize\n};\n\nfunction isSecure(wsComponents) {\n    return typeof wsComponents.secure === 'boolean' ? wsComponents.secure : String(wsComponents.scheme).toLowerCase() === \"wss\";\n}\n//RFC 6455\nvar handler$2 = {\n    scheme: \"ws\",\n    domainHost: true,\n    parse: function parse(components, options) {\n        var wsComponents = components;\n        //indicate if the secure flag is set\n        wsComponents.secure = isSecure(wsComponents);\n        //construct resouce name\n        wsComponents.resourceName = (wsComponents.path || '/') + (wsComponents.query ? '?' + wsComponents.query : '');\n        wsComponents.path = undefined;\n        wsComponents.query = undefined;\n        return wsComponents;\n    },\n    serialize: function serialize(wsComponents, options) {\n        //normalize the default port\n        if (wsComponents.port === (isSecure(wsComponents) ? 443 : 80) || wsComponents.port === \"\") {\n            wsComponents.port = undefined;\n        }\n        //ensure scheme matches secure flag\n        if (typeof wsComponents.secure === 'boolean') {\n            wsComponents.scheme = wsComponents.secure ? 'wss' : 'ws';\n            wsComponents.secure = undefined;\n        }\n        //reconstruct path from resource name\n        if (wsComponents.resourceName) {\n            var _wsComponents$resourc = wsComponents.resourceName.split('?'),\n                _wsComponents$resourc2 = slicedToArray(_wsComponents$resourc, 2),\n                path = _wsComponents$resourc2[0],\n                query = _wsComponents$resourc2[1];\n\n            wsComponents.path = path && path !== '/' ? path : undefined;\n            wsComponents.query = query;\n            wsComponents.resourceName = undefined;\n        }\n        //forbid fragment component\n        wsComponents.fragment = undefined;\n        return wsComponents;\n    }\n};\n\nvar handler$3 = {\n    scheme: \"wss\",\n    domainHost: handler$2.domainHost,\n    parse: handler$2.parse,\n    serialize: handler$2.serialize\n};\n\nvar O = {};\nvar isIRI = true;\n//RFC 3986\nvar UNRESERVED$$ = \"[A-Za-z0-9\\\\-\\\\.\\\\_\\\\~\" + (isIRI ? \"\\\\xA0-\\\\u200D\\\\u2010-\\\\u2029\\\\u202F-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFEF\" : \"\") + \"]\";\nvar HEXDIG$$ = \"[0-9A-Fa-f]\"; //case-insensitive\nvar PCT_ENCODED$ = subexp(subexp(\"%[EFef]\" + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$) + \"|\" + subexp(\"%[89A-Fa-f]\" + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$) + \"|\" + subexp(\"%\" + HEXDIG$$ + HEXDIG$$)); //expanded\n//RFC 5322, except these symbols as per RFC 6068: @ : / ? # [ ] & ; =\n//const ATEXT$$ = \"[A-Za-z0-9\\\\!\\\\#\\\\$\\\\%\\\\&\\\\'\\\\*\\\\+\\\\-\\\\/\\\\=\\\\?\\\\^\\\\_\\\\`\\\\{\\\\|\\\\}\\\\~]\";\n//const WSP$$ = \"[\\\\x20\\\\x09]\";\n//const OBS_QTEXT$$ = \"[\\\\x01-\\\\x08\\\\x0B\\\\x0C\\\\x0E-\\\\x1F\\\\x7F]\";  //(%d1-8 / %d11-12 / %d14-31 / %d127)\n//const QTEXT$$ = merge(\"[\\\\x21\\\\x23-\\\\x5B\\\\x5D-\\\\x7E]\", OBS_QTEXT$$);  //%d33 / %d35-91 / %d93-126 / obs-qtext\n//const VCHAR$$ = \"[\\\\x21-\\\\x7E]\";\n//const WSP$$ = \"[\\\\x20\\\\x09]\";\n//const OBS_QP$ = subexp(\"\\\\\\\\\" + merge(\"[\\\\x00\\\\x0D\\\\x0A]\", OBS_QTEXT$$));  //%d0 / CR / LF / obs-qtext\n//const FWS$ = subexp(subexp(WSP$$ + \"*\" + \"\\\\x0D\\\\x0A\") + \"?\" + WSP$$ + \"+\");\n//const QUOTED_PAIR$ = subexp(subexp(\"\\\\\\\\\" + subexp(VCHAR$$ + \"|\" + WSP$$)) + \"|\" + OBS_QP$);\n//const QUOTED_STRING$ = subexp('\\\\\"' + subexp(FWS$ + \"?\" + QCONTENT$) + \"*\" + FWS$ + \"?\" + '\\\\\"');\nvar ATEXT$$ = \"[A-Za-z0-9\\\\!\\\\$\\\\%\\\\'\\\\*\\\\+\\\\-\\\\^\\\\_\\\\`\\\\{\\\\|\\\\}\\\\~]\";\nvar QTEXT$$ = \"[\\\\!\\\\$\\\\%\\\\'\\\\(\\\\)\\\\*\\\\+\\\\,\\\\-\\\\.0-9\\\\<\\\\>A-Z\\\\x5E-\\\\x7E]\";\nvar VCHAR$$ = merge(QTEXT$$, \"[\\\\\\\"\\\\\\\\]\");\nvar SOME_DELIMS$$ = \"[\\\\!\\\\$\\\\'\\\\(\\\\)\\\\*\\\\+\\\\,\\\\;\\\\:\\\\@]\";\nvar UNRESERVED = new RegExp(UNRESERVED$$, \"g\");\nvar PCT_ENCODED = new RegExp(PCT_ENCODED$, \"g\");\nvar NOT_LOCAL_PART = new RegExp(merge(\"[^]\", ATEXT$$, \"[\\\\.]\", '[\\\\\"]', VCHAR$$), \"g\");\nvar NOT_HFNAME = new RegExp(merge(\"[^]\", UNRESERVED$$, SOME_DELIMS$$), \"g\");\nvar NOT_HFVALUE = NOT_HFNAME;\nfunction decodeUnreserved(str) {\n    var decStr = pctDecChars(str);\n    return !decStr.match(UNRESERVED) ? str : decStr;\n}\nvar handler$4 = {\n    scheme: \"mailto\",\n    parse: function parse$$1(components, options) {\n        var mailtoComponents = components;\n        var to = mailtoComponents.to = mailtoComponents.path ? mailtoComponents.path.split(\",\") : [];\n        mailtoComponents.path = undefined;\n        if (mailtoComponents.query) {\n            var unknownHeaders = false;\n            var headers = {};\n            var hfields = mailtoComponents.query.split(\"&\");\n            for (var x = 0, xl = hfields.length; x < xl; ++x) {\n                var hfield = hfields[x].split(\"=\");\n                switch (hfield[0]) {\n                    case \"to\":\n                        var toAddrs = hfield[1].split(\",\");\n                        for (var _x = 0, _xl = toAddrs.length; _x < _xl; ++_x) {\n                            to.push(toAddrs[_x]);\n                        }\n                        break;\n                    case \"subject\":\n                        mailtoComponents.subject = unescapeComponent(hfield[1], options);\n                        break;\n                    case \"body\":\n                        mailtoComponents.body = unescapeComponent(hfield[1], options);\n                        break;\n                    default:\n                        unknownHeaders = true;\n                        headers[unescapeComponent(hfield[0], options)] = unescapeComponent(hfield[1], options);\n                        break;\n                }\n            }\n            if (unknownHeaders) mailtoComponents.headers = headers;\n        }\n        mailtoComponents.query = undefined;\n        for (var _x2 = 0, _xl2 = to.length; _x2 < _xl2; ++_x2) {\n            var addr = to[_x2].split(\"@\");\n            addr[0] = unescapeComponent(addr[0]);\n            if (!options.unicodeSupport) {\n                //convert Unicode IDN -> ASCII IDN\n                try {\n                    addr[1] = punycode.toASCII(unescapeComponent(addr[1], options).toLowerCase());\n                } catch (e) {\n                    mailtoComponents.error = mailtoComponents.error || \"Email address's domain name can not be converted to ASCII via punycode: \" + e;\n                }\n            } else {\n                addr[1] = unescapeComponent(addr[1], options).toLowerCase();\n            }\n            to[_x2] = addr.join(\"@\");\n        }\n        return mailtoComponents;\n    },\n    serialize: function serialize$$1(mailtoComponents, options) {\n        var components = mailtoComponents;\n        var to = toArray(mailtoComponents.to);\n        if (to) {\n            for (var x = 0, xl = to.length; x < xl; ++x) {\n                var toAddr = String(to[x]);\n                var atIdx = toAddr.lastIndexOf(\"@\");\n                var localPart = toAddr.slice(0, atIdx).replace(PCT_ENCODED, decodeUnreserved).replace(PCT_ENCODED, toUpperCase).replace(NOT_LOCAL_PART, pctEncChar);\n                var domain = toAddr.slice(atIdx + 1);\n                //convert IDN via punycode\n                try {\n                    domain = !options.iri ? punycode.toASCII(unescapeComponent(domain, options).toLowerCase()) : punycode.toUnicode(domain);\n                } catch (e) {\n                    components.error = components.error || \"Email address's domain name can not be converted to \" + (!options.iri ? \"ASCII\" : \"Unicode\") + \" via punycode: \" + e;\n                }\n                to[x] = localPart + \"@\" + domain;\n            }\n            components.path = to.join(\",\");\n        }\n        var headers = mailtoComponents.headers = mailtoComponents.headers || {};\n        if (mailtoComponents.subject) headers[\"subject\"] = mailtoComponents.subject;\n        if (mailtoComponents.body) headers[\"body\"] = mailtoComponents.body;\n        var fields = [];\n        for (var name in headers) {\n            if (headers[name] !== O[name]) {\n                fields.push(name.replace(PCT_ENCODED, decodeUnreserved).replace(PCT_ENCODED, toUpperCase).replace(NOT_HFNAME, pctEncChar) + \"=\" + headers[name].replace(PCT_ENCODED, decodeUnreserved).replace(PCT_ENCODED, toUpperCase).replace(NOT_HFVALUE, pctEncChar));\n            }\n        }\n        if (fields.length) {\n            components.query = fields.join(\"&\");\n        }\n        return components;\n    }\n};\n\nvar URN_PARSE = /^([^\\:]+)\\:(.*)/;\n//RFC 2141\nvar handler$5 = {\n    scheme: \"urn\",\n    parse: function parse$$1(components, options) {\n        var matches = components.path && components.path.match(URN_PARSE);\n        var urnComponents = components;\n        if (matches) {\n            var scheme = options.scheme || urnComponents.scheme || \"urn\";\n            var nid = matches[1].toLowerCase();\n            var nss = matches[2];\n            var urnScheme = scheme + \":\" + (options.nid || nid);\n            var schemeHandler = SCHEMES[urnScheme];\n            urnComponents.nid = nid;\n            urnComponents.nss = nss;\n            urnComponents.path = undefined;\n            if (schemeHandler) {\n                urnComponents = schemeHandler.parse(urnComponents, options);\n            }\n        } else {\n            urnComponents.error = urnComponents.error || \"URN can not be parsed.\";\n        }\n        return urnComponents;\n    },\n    serialize: function serialize$$1(urnComponents, options) {\n        var scheme = options.scheme || urnComponents.scheme || \"urn\";\n        var nid = urnComponents.nid;\n        var urnScheme = scheme + \":\" + (options.nid || nid);\n        var schemeHandler = SCHEMES[urnScheme];\n        if (schemeHandler) {\n            urnComponents = schemeHandler.serialize(urnComponents, options);\n        }\n        var uriComponents = urnComponents;\n        var nss = urnComponents.nss;\n        uriComponents.path = (nid || options.nid) + \":\" + nss;\n        return uriComponents;\n    }\n};\n\nvar UUID = /^[0-9A-Fa-f]{8}(?:\\-[0-9A-Fa-f]{4}){3}\\-[0-9A-Fa-f]{12}$/;\n//RFC 4122\nvar handler$6 = {\n    scheme: \"urn:uuid\",\n    parse: function parse(urnComponents, options) {\n        var uuidComponents = urnComponents;\n        uuidComponents.uuid = uuidComponents.nss;\n        uuidComponents.nss = undefined;\n        if (!options.tolerant && (!uuidComponents.uuid || !uuidComponents.uuid.match(UUID))) {\n            uuidComponents.error = uuidComponents.error || \"UUID is not valid.\";\n        }\n        return uuidComponents;\n    },\n    serialize: function serialize(uuidComponents, options) {\n        var urnComponents = uuidComponents;\n        //normalize UUID\n        urnComponents.nss = (uuidComponents.uuid || \"\").toLowerCase();\n        return urnComponents;\n    }\n};\n\nSCHEMES[handler.scheme] = handler;\nSCHEMES[handler$1.scheme] = handler$1;\nSCHEMES[handler$2.scheme] = handler$2;\nSCHEMES[handler$3.scheme] = handler$3;\nSCHEMES[handler$4.scheme] = handler$4;\nSCHEMES[handler$5.scheme] = handler$5;\nSCHEMES[handler$6.scheme] = handler$6;\n\nexports.SCHEMES = SCHEMES;\nexports.pctEncChar = pctEncChar;\nexports.pctDecChars = pctDecChars;\nexports.parse = parse;\nexports.removeDotSegments = removeDotSegments;\nexports.serialize = serialize;\nexports.resolveComponents = resolveComponents;\nexports.resolve = resolve;\nexports.normalize = normalize;\nexports.equal = equal;\nexports.escapeComponent = escapeComponent;\nexports.unescapeComponent = unescapeComponent;\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\n})));\n//# sourceMappingURL=uri.all.js.map\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst uri = require(\"uri-js\");\nuri.code = 'require(\"ajv/dist/runtime/uri\").default';\nexports.default = uri;\n//# sourceMappingURL=uri.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CodeGen = exports.Name = exports.nil = exports.stringify = exports.str = exports._ = exports.KeywordCxt = void 0;\nvar validate_1 = require(\"./compile/validate\");\nObject.defineProperty(exports, \"KeywordCxt\", { enumerable: true, get: function () { return validate_1.KeywordCxt; } });\nvar codegen_1 = require(\"./compile/codegen\");\nObject.defineProperty(exports, \"_\", { enumerable: true, get: function () { return codegen_1._; } });\nObject.defineProperty(exports, \"str\", { enumerable: true, get: function () { return codegen_1.str; } });\nObject.defineProperty(exports, \"stringify\", { enumerable: true, get: function () { return codegen_1.stringify; } });\nObject.defineProperty(exports, \"nil\", { enumerable: true, get: function () { return codegen_1.nil; } });\nObject.defineProperty(exports, \"Name\", { enumerable: true, get: function () { return codegen_1.Name; } });\nObject.defineProperty(exports, \"CodeGen\", { enumerable: true, get: function () { return codegen_1.CodeGen; } });\nconst validation_error_1 = require(\"./runtime/validation_error\");\nconst ref_error_1 = require(\"./compile/ref_error\");\nconst rules_1 = require(\"./compile/rules\");\nconst compile_1 = require(\"./compile\");\nconst codegen_2 = require(\"./compile/codegen\");\nconst resolve_1 = require(\"./compile/resolve\");\nconst dataType_1 = require(\"./compile/validate/dataType\");\nconst util_1 = require(\"./compile/util\");\nconst $dataRefSchema = require(\"./refs/data.json\");\nconst uri_1 = require(\"./runtime/uri\");\nconst defaultRegExp = (str, flags) => new RegExp(str, flags);\ndefaultRegExp.code = \"new RegExp\";\nconst META_IGNORE_OPTIONS = [\"removeAdditional\", \"useDefaults\", \"coerceTypes\"];\nconst EXT_SCOPE_NAMES = new Set([\n    \"validate\",\n    \"serialize\",\n    \"parse\",\n    \"wrapper\",\n    \"root\",\n    \"schema\",\n    \"keyword\",\n    \"pattern\",\n    \"formats\",\n    \"validate$data\",\n    \"func\",\n    \"obj\",\n    \"Error\",\n]);\nconst removedOptions = {\n    errorDataPath: \"\",\n    format: \"`validateFormats: false` can be used instead.\",\n    nullable: '\"nullable\" keyword is supported by default.',\n    jsonPointers: \"Deprecated jsPropertySyntax can be used instead.\",\n    extendRefs: \"Deprecated ignoreKeywordsWithRef can be used instead.\",\n    missingRefs: \"Pass empty schema with $id that should be ignored to ajv.addSchema.\",\n    processCode: \"Use option `code: {process: (code, schemaEnv: object) => string}`\",\n    sourceCode: \"Use option `code: {source: true}`\",\n    strictDefaults: \"It is default now, see option `strict`.\",\n    strictKeywords: \"It is default now, see option `strict`.\",\n    uniqueItems: '\"uniqueItems\" keyword is always validated.',\n    unknownFormats: \"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).\",\n    cache: \"Map is used as cache, schema object as key.\",\n    serialize: \"Map is used as cache, schema object as key.\",\n    ajvErrors: \"It is default now.\",\n};\nconst deprecatedOptions = {\n    ignoreKeywordsWithRef: \"\",\n    jsPropertySyntax: \"\",\n    unicode: '\"minLength\"/\"maxLength\" account for unicode characters by default.',\n};\nconst MAX_EXPRESSION = 200;\n// eslint-disable-next-line complexity\nfunction requiredOptions(o) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0;\n    const s = o.strict;\n    const _optz = (_a = o.code) === null || _a === void 0 ? void 0 : _a.optimize;\n    const optimize = _optz === true || _optz === undefined ? 1 : _optz || 0;\n    const regExp = (_c = (_b = o.code) === null || _b === void 0 ? void 0 : _b.regExp) !== null && _c !== void 0 ? _c : defaultRegExp;\n    const uriResolver = (_d = o.uriResolver) !== null && _d !== void 0 ? _d : uri_1.default;\n    return {\n        strictSchema: (_f = (_e = o.strictSchema) !== null && _e !== void 0 ? _e : s) !== null && _f !== void 0 ? _f : true,\n        strictNumbers: (_h = (_g = o.strictNumbers) !== null && _g !== void 0 ? _g : s) !== null && _h !== void 0 ? _h : true,\n        strictTypes: (_k = (_j = o.strictTypes) !== null && _j !== void 0 ? _j : s) !== null && _k !== void 0 ? _k : \"log\",\n        strictTuples: (_m = (_l = o.strictTuples) !== null && _l !== void 0 ? _l : s) !== null && _m !== void 0 ? _m : \"log\",\n        strictRequired: (_p = (_o = o.strictRequired) !== null && _o !== void 0 ? _o : s) !== null && _p !== void 0 ? _p : false,\n        code: o.code ? { ...o.code, optimize, regExp } : { optimize, regExp },\n        loopRequired: (_q = o.loopRequired) !== null && _q !== void 0 ? _q : MAX_EXPRESSION,\n        loopEnum: (_r = o.loopEnum) !== null && _r !== void 0 ? _r : MAX_EXPRESSION,\n        meta: (_s = o.meta) !== null && _s !== void 0 ? _s : true,\n        messages: (_t = o.messages) !== null && _t !== void 0 ? _t : true,\n        inlineRefs: (_u = o.inlineRefs) !== null && _u !== void 0 ? _u : true,\n        schemaId: (_v = o.schemaId) !== null && _v !== void 0 ? _v : \"$id\",\n        addUsedSchema: (_w = o.addUsedSchema) !== null && _w !== void 0 ? _w : true,\n        validateSchema: (_x = o.validateSchema) !== null && _x !== void 0 ? _x : true,\n        validateFormats: (_y = o.validateFormats) !== null && _y !== void 0 ? _y : true,\n        unicodeRegExp: (_z = o.unicodeRegExp) !== null && _z !== void 0 ? _z : true,\n        int32range: (_0 = o.int32range) !== null && _0 !== void 0 ? _0 : true,\n        uriResolver: uriResolver,\n    };\n}\nclass Ajv {\n    constructor(opts = {}) {\n        this.schemas = {};\n        this.refs = {};\n        this.formats = {};\n        this._compilations = new Set();\n        this._loading = {};\n        this._cache = new Map();\n        opts = this.opts = { ...opts, ...requiredOptions(opts) };\n        const { es5, lines } = this.opts.code;\n        this.scope = new codegen_2.ValueScope({ scope: {}, prefixes: EXT_SCOPE_NAMES, es5, lines });\n        this.logger = getLogger(opts.logger);\n        const formatOpt = opts.validateFormats;\n        opts.validateFormats = false;\n        this.RULES = (0, rules_1.getRules)();\n        checkOptions.call(this, removedOptions, opts, \"NOT SUPPORTED\");\n        checkOptions.call(this, deprecatedOptions, opts, \"DEPRECATED\", \"warn\");\n        this._metaOpts = getMetaSchemaOptions.call(this);\n        if (opts.formats)\n            addInitialFormats.call(this);\n        this._addVocabularies();\n        this._addDefaultMetaSchema();\n        if (opts.keywords)\n            addInitialKeywords.call(this, opts.keywords);\n        if (typeof opts.meta == \"object\")\n            this.addMetaSchema(opts.meta);\n        addInitialSchemas.call(this);\n        opts.validateFormats = formatOpt;\n    }\n    _addVocabularies() {\n        this.addKeyword(\"$async\");\n    }\n    _addDefaultMetaSchema() {\n        const { $data, meta, schemaId } = this.opts;\n        let _dataRefSchema = $dataRefSchema;\n        if (schemaId === \"id\") {\n            _dataRefSchema = { ...$dataRefSchema };\n            _dataRefSchema.id = _dataRefSchema.$id;\n            delete _dataRefSchema.$id;\n        }\n        if (meta && $data)\n            this.addMetaSchema(_dataRefSchema, _dataRefSchema[schemaId], false);\n    }\n    defaultMeta() {\n        const { meta, schemaId } = this.opts;\n        return (this.opts.defaultMeta = typeof meta == \"object\" ? meta[schemaId] || meta : undefined);\n    }\n    validate(schemaKeyRef, // key, ref or schema object\n    data // to be validated\n    ) {\n        let v;\n        if (typeof schemaKeyRef == \"string\") {\n            v = this.getSchema(schemaKeyRef);\n            if (!v)\n                throw new Error(`no schema with key or ref \"${schemaKeyRef}\"`);\n        }\n        else {\n            v = this.compile(schemaKeyRef);\n        }\n        const valid = v(data);\n        if (!(\"$async\" in v))\n            this.errors = v.errors;\n        return valid;\n    }\n    compile(schema, _meta) {\n        const sch = this._addSchema(schema, _meta);\n        return (sch.validate || this._compileSchemaEnv(sch));\n    }\n    compileAsync(schema, meta) {\n        if (typeof this.opts.loadSchema != \"function\") {\n            throw new Error(\"options.loadSchema should be a function\");\n        }\n        const { loadSchema } = this.opts;\n        return runCompileAsync.call(this, schema, meta);\n        async function runCompileAsync(_schema, _meta) {\n            await loadMetaSchema.call(this, _schema.$schema);\n            const sch = this._addSchema(_schema, _meta);\n            return sch.validate || _compileAsync.call(this, sch);\n        }\n        async function loadMetaSchema($ref) {\n            if ($ref && !this.getSchema($ref)) {\n                await runCompileAsync.call(this, { $ref }, true);\n            }\n        }\n        async function _compileAsync(sch) {\n            try {\n                return this._compileSchemaEnv(sch);\n            }\n            catch (e) {\n                if (!(e instanceof ref_error_1.default))\n                    throw e;\n                checkLoaded.call(this, e);\n                await loadMissingSchema.call(this, e.missingSchema);\n                return _compileAsync.call(this, sch);\n            }\n        }\n        function checkLoaded({ missingSchema: ref, missingRef }) {\n            if (this.refs[ref]) {\n                throw new Error(`AnySchema ${ref} is loaded but ${missingRef} cannot be resolved`);\n            }\n        }\n        async function loadMissingSchema(ref) {\n            const _schema = await _loadSchema.call(this, ref);\n            if (!this.refs[ref])\n                await loadMetaSchema.call(this, _schema.$schema);\n            if (!this.refs[ref])\n                this.addSchema(_schema, ref, meta);\n        }\n        async function _loadSchema(ref) {\n            const p = this._loading[ref];\n            if (p)\n                return p;\n            try {\n                return await (this._loading[ref] = loadSchema(ref));\n            }\n            finally {\n                delete this._loading[ref];\n            }\n        }\n    }\n    // Adds schema to the instance\n    addSchema(schema, // If array is passed, `key` will be ignored\n    key, // Optional schema key. Can be passed to `validate` method instead of schema object or id/ref. One schema per instance can have empty `id` and `key`.\n    _meta, // true if schema is a meta-schema. Used internally, addMetaSchema should be used instead.\n    _validateSchema = this.opts.validateSchema // false to skip schema validation. Used internally, option validateSchema should be used instead.\n    ) {\n        if (Array.isArray(schema)) {\n            for (const sch of schema)\n                this.addSchema(sch, undefined, _meta, _validateSchema);\n            return this;\n        }\n        let id;\n        if (typeof schema === \"object\") {\n            const { schemaId } = this.opts;\n            id = schema[schemaId];\n            if (id !== undefined && typeof id != \"string\") {\n                throw new Error(`schema ${schemaId} must be string`);\n            }\n        }\n        key = (0, resolve_1.normalizeId)(key || id);\n        this._checkUnique(key);\n        this.schemas[key] = this._addSchema(schema, _meta, key, _validateSchema, true);\n        return this;\n    }\n    // Add schema that will be used to validate other schemas\n    // options in META_IGNORE_OPTIONS are alway set to false\n    addMetaSchema(schema, key, // schema key\n    _validateSchema = this.opts.validateSchema // false to skip schema validation, can be used to override validateSchema option for meta-schema\n    ) {\n        this.addSchema(schema, key, true, _validateSchema);\n        return this;\n    }\n    //  Validate schema against its meta-schema\n    validateSchema(schema, throwOrLogError) {\n        if (typeof schema == \"boolean\")\n            return true;\n        let $schema;\n        $schema = schema.$schema;\n        if ($schema !== undefined && typeof $schema != \"string\") {\n            throw new Error(\"$schema must be a string\");\n        }\n        $schema = $schema || this.opts.defaultMeta || this.defaultMeta();\n        if (!$schema) {\n            this.logger.warn(\"meta-schema not available\");\n            this.errors = null;\n            return true;\n        }\n        const valid = this.validate($schema, schema);\n        if (!valid && throwOrLogError) {\n            const message = \"schema is invalid: \" + this.errorsText();\n            if (this.opts.validateSchema === \"log\")\n                this.logger.error(message);\n            else\n                throw new Error(message);\n        }\n        return valid;\n    }\n    // Get compiled schema by `key` or `ref`.\n    // (`key` that was passed to `addSchema` or full schema reference - `schema.$id` or resolved id)\n    getSchema(keyRef) {\n        let sch;\n        while (typeof (sch = getSchEnv.call(this, keyRef)) == \"string\")\n            keyRef = sch;\n        if (sch === undefined) {\n            const { schemaId } = this.opts;\n            const root = new compile_1.SchemaEnv({ schema: {}, schemaId });\n            sch = compile_1.resolveSchema.call(this, root, keyRef);\n            if (!sch)\n                return;\n            this.refs[keyRef] = sch;\n        }\n        return (sch.validate || this._compileSchemaEnv(sch));\n    }\n    // Remove cached schema(s).\n    // If no parameter is passed all schemas but meta-schemas are removed.\n    // If RegExp is passed all schemas with key/id matching pattern but meta-schemas are removed.\n    // Even if schema is referenced by other schemas it still can be removed as other schemas have local references.\n    removeSchema(schemaKeyRef) {\n        if (schemaKeyRef instanceof RegExp) {\n            this._removeAllSchemas(this.schemas, schemaKeyRef);\n            this._removeAllSchemas(this.refs, schemaKeyRef);\n            return this;\n        }\n        switch (typeof schemaKeyRef) {\n            case \"undefined\":\n                this._removeAllSchemas(this.schemas);\n                this._removeAllSchemas(this.refs);\n                this._cache.clear();\n                return this;\n            case \"string\": {\n                const sch = getSchEnv.call(this, schemaKeyRef);\n                if (typeof sch == \"object\")\n                    this._cache.delete(sch.schema);\n                delete this.schemas[schemaKeyRef];\n                delete this.refs[schemaKeyRef];\n                return this;\n            }\n            case \"object\": {\n                const cacheKey = schemaKeyRef;\n                this._cache.delete(cacheKey);\n                let id = schemaKeyRef[this.opts.schemaId];\n                if (id) {\n                    id = (0, resolve_1.normalizeId)(id);\n                    delete this.schemas[id];\n                    delete this.refs[id];\n                }\n                return this;\n            }\n            default:\n                throw new Error(\"ajv.removeSchema: invalid parameter\");\n        }\n    }\n    // add \"vocabulary\" - a collection of keywords\n    addVocabulary(definitions) {\n        for (const def of definitions)\n            this.addKeyword(def);\n        return this;\n    }\n    addKeyword(kwdOrDef, def // deprecated\n    ) {\n        let keyword;\n        if (typeof kwdOrDef == \"string\") {\n            keyword = kwdOrDef;\n            if (typeof def == \"object\") {\n                this.logger.warn(\"these parameters are deprecated, see docs for addKeyword\");\n                def.keyword = keyword;\n            }\n        }\n        else if (typeof kwdOrDef == \"object\" && def === undefined) {\n            def = kwdOrDef;\n            keyword = def.keyword;\n            if (Array.isArray(keyword) && !keyword.length) {\n                throw new Error(\"addKeywords: keyword must be string or non-empty array\");\n            }\n        }\n        else {\n            throw new Error(\"invalid addKeywords parameters\");\n        }\n        checkKeyword.call(this, keyword, def);\n        if (!def) {\n            (0, util_1.eachItem)(keyword, (kwd) => addRule.call(this, kwd));\n            return this;\n        }\n        keywordMetaschema.call(this, def);\n        const definition = {\n            ...def,\n            type: (0, dataType_1.getJSONTypes)(def.type),\n            schemaType: (0, dataType_1.getJSONTypes)(def.schemaType),\n        };\n        (0, util_1.eachItem)(keyword, definition.type.length === 0\n            ? (k) => addRule.call(this, k, definition)\n            : (k) => definition.type.forEach((t) => addRule.call(this, k, definition, t)));\n        return this;\n    }\n    getKeyword(keyword) {\n        const rule = this.RULES.all[keyword];\n        return typeof rule == \"object\" ? rule.definition : !!rule;\n    }\n    // Remove keyword\n    removeKeyword(keyword) {\n        // TODO return type should be Ajv\n        const { RULES } = this;\n        delete RULES.keywords[keyword];\n        delete RULES.all[keyword];\n        for (const group of RULES.rules) {\n            const i = group.rules.findIndex((rule) => rule.keyword === keyword);\n            if (i >= 0)\n                group.rules.splice(i, 1);\n        }\n        return this;\n    }\n    // Add format\n    addFormat(name, format) {\n        if (typeof format == \"string\")\n            format = new RegExp(format);\n        this.formats[name] = format;\n        return this;\n    }\n    errorsText(errors = this.errors, // optional array of validation errors\n    { separator = \", \", dataVar = \"data\" } = {} // optional options with properties `separator` and `dataVar`\n    ) {\n        if (!errors || errors.length === 0)\n            return \"No errors\";\n        return errors\n            .map((e) => `${dataVar}${e.instancePath} ${e.message}`)\n            .reduce((text, msg) => text + separator + msg);\n    }\n    $dataMetaSchema(metaSchema, keywordsJsonPointers) {\n        const rules = this.RULES.all;\n        metaSchema = JSON.parse(JSON.stringify(metaSchema));\n        for (const jsonPointer of keywordsJsonPointers) {\n            const segments = jsonPointer.split(\"/\").slice(1); // first segment is an empty string\n            let keywords = metaSchema;\n            for (const seg of segments)\n                keywords = keywords[seg];\n            for (const key in rules) {\n                const rule = rules[key];\n                if (typeof rule != \"object\")\n                    continue;\n                const { $data } = rule.definition;\n                const schema = keywords[key];\n                if ($data && schema)\n                    keywords[key] = schemaOrData(schema);\n            }\n        }\n        return metaSchema;\n    }\n    _removeAllSchemas(schemas, regex) {\n        for (const keyRef in schemas) {\n            const sch = schemas[keyRef];\n            if (!regex || regex.test(keyRef)) {\n                if (typeof sch == \"string\") {\n                    delete schemas[keyRef];\n                }\n                else if (sch && !sch.meta) {\n                    this._cache.delete(sch.schema);\n                    delete schemas[keyRef];\n                }\n            }\n        }\n    }\n    _addSchema(schema, meta, baseId, validateSchema = this.opts.validateSchema, addSchema = this.opts.addUsedSchema) {\n        let id;\n        const { schemaId } = this.opts;\n        if (typeof schema == \"object\") {\n            id = schema[schemaId];\n        }\n        else {\n            if (this.opts.jtd)\n                throw new Error(\"schema must be object\");\n            else if (typeof schema != \"boolean\")\n                throw new Error(\"schema must be object or boolean\");\n        }\n        let sch = this._cache.get(schema);\n        if (sch !== undefined)\n            return sch;\n        baseId = (0, resolve_1.normalizeId)(id || baseId);\n        const localRefs = resolve_1.getSchemaRefs.call(this, schema, baseId);\n        sch = new compile_1.SchemaEnv({ schema, schemaId, meta, baseId, localRefs });\n        this._cache.set(sch.schema, sch);\n        if (addSchema && !baseId.startsWith(\"#\")) {\n            // TODO atm it is allowed to overwrite schemas without id (instead of not adding them)\n            if (baseId)\n                this._checkUnique(baseId);\n            this.refs[baseId] = sch;\n        }\n        if (validateSchema)\n            this.validateSchema(schema, true);\n        return sch;\n    }\n    _checkUnique(id) {\n        if (this.schemas[id] || this.refs[id]) {\n            throw new Error(`schema with key or id \"${id}\" already exists`);\n        }\n    }\n    _compileSchemaEnv(sch) {\n        if (sch.meta)\n            this._compileMetaSchema(sch);\n        else\n            compile_1.compileSchema.call(this, sch);\n        /* istanbul ignore if */\n        if (!sch.validate)\n            throw new Error(\"ajv implementation error\");\n        return sch.validate;\n    }\n    _compileMetaSchema(sch) {\n        const currentOpts = this.opts;\n        this.opts = this._metaOpts;\n        try {\n            compile_1.compileSchema.call(this, sch);\n        }\n        finally {\n            this.opts = currentOpts;\n        }\n    }\n}\nexports.default = Ajv;\nAjv.ValidationError = validation_error_1.default;\nAjv.MissingRefError = ref_error_1.default;\nfunction checkOptions(checkOpts, options, msg, log = \"error\") {\n    for (const key in checkOpts) {\n        const opt = key;\n        if (opt in options)\n            this.logger[log](`${msg}: option ${key}. ${checkOpts[opt]}`);\n    }\n}\nfunction getSchEnv(keyRef) {\n    keyRef = (0, resolve_1.normalizeId)(keyRef); // TODO tests fail without this line\n    return this.schemas[keyRef] || this.refs[keyRef];\n}\nfunction addInitialSchemas() {\n    const optsSchemas = this.opts.schemas;\n    if (!optsSchemas)\n        return;\n    if (Array.isArray(optsSchemas))\n        this.addSchema(optsSchemas);\n    else\n        for (const key in optsSchemas)\n            this.addSchema(optsSchemas[key], key);\n}\nfunction addInitialFormats() {\n    for (const name in this.opts.formats) {\n        const format = this.opts.formats[name];\n        if (format)\n            this.addFormat(name, format);\n    }\n}\nfunction addInitialKeywords(defs) {\n    if (Array.isArray(defs)) {\n        this.addVocabulary(defs);\n        return;\n    }\n    this.logger.warn(\"keywords option as map is deprecated, pass array\");\n    for (const keyword in defs) {\n        const def = defs[keyword];\n        if (!def.keyword)\n            def.keyword = keyword;\n        this.addKeyword(def);\n    }\n}\nfunction getMetaSchemaOptions() {\n    const metaOpts = { ...this.opts };\n    for (const opt of META_IGNORE_OPTIONS)\n        delete metaOpts[opt];\n    return metaOpts;\n}\nconst noLogs = { log() { }, warn() { }, error() { } };\nfunction getLogger(logger) {\n    if (logger === false)\n        return noLogs;\n    if (logger === undefined)\n        return console;\n    if (logger.log && logger.warn && logger.error)\n        return logger;\n    throw new Error(\"logger must implement log, warn and error methods\");\n}\nconst KEYWORD_NAME = /^[a-z_$][a-z0-9_$:-]*$/i;\nfunction checkKeyword(keyword, def) {\n    const { RULES } = this;\n    (0, util_1.eachItem)(keyword, (kwd) => {\n        if (RULES.keywords[kwd])\n            throw new Error(`Keyword ${kwd} is already defined`);\n        if (!KEYWORD_NAME.test(kwd))\n            throw new Error(`Keyword ${kwd} has invalid name`);\n    });\n    if (!def)\n        return;\n    if (def.$data && !(\"code\" in def || \"validate\" in def)) {\n        throw new Error('$data keyword must have \"code\" or \"validate\" function');\n    }\n}\nfunction addRule(keyword, definition, dataType) {\n    var _a;\n    const post = definition === null || definition === void 0 ? void 0 : definition.post;\n    if (dataType && post)\n        throw new Error('keyword with \"post\" flag cannot have \"type\"');\n    const { RULES } = this;\n    let ruleGroup = post ? RULES.post : RULES.rules.find(({ type: t }) => t === dataType);\n    if (!ruleGroup) {\n        ruleGroup = { type: dataType, rules: [] };\n        RULES.rules.push(ruleGroup);\n    }\n    RULES.keywords[keyword] = true;\n    if (!definition)\n        return;\n    const rule = {\n        keyword,\n        definition: {\n            ...definition,\n            type: (0, dataType_1.getJSONTypes)(definition.type),\n            schemaType: (0, dataType_1.getJSONTypes)(definition.schemaType),\n        },\n    };\n    if (definition.before)\n        addBeforeRule.call(this, ruleGroup, rule, definition.before);\n    else\n        ruleGroup.rules.push(rule);\n    RULES.all[keyword] = rule;\n    (_a = definition.implements) === null || _a === void 0 ? void 0 : _a.forEach((kwd) => this.addKeyword(kwd));\n}\nfunction addBeforeRule(ruleGroup, rule, before) {\n    const i = ruleGroup.rules.findIndex((_rule) => _rule.keyword === before);\n    if (i >= 0) {\n        ruleGroup.rules.splice(i, 0, rule);\n    }\n    else {\n        ruleGroup.rules.push(rule);\n        this.logger.warn(`rule ${before} is not defined`);\n    }\n}\nfunction keywordMetaschema(def) {\n    let { metaSchema } = def;\n    if (metaSchema === undefined)\n        return;\n    if (def.$data && this.opts.$data)\n        metaSchema = schemaOrData(metaSchema);\n    def.validateSchema = this.compile(metaSchema, true);\n}\nconst $dataRef = {\n    $ref: \"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#\",\n};\nfunction schemaOrData(schema) {\n    return { anyOf: [schema, $dataRef] };\n}\n//# sourceMappingURL=core.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst def = {\n    keyword: \"id\",\n    code() {\n        throw new Error('NOT SUPPORTED: keyword \"id\", use \"$id\" for schema ID');\n    },\n};\nexports.default = def;\n//# sourceMappingURL=id.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.callRef = exports.getValidate = void 0;\nconst ref_error_1 = require(\"../../compile/ref_error\");\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst names_1 = require(\"../../compile/names\");\nconst compile_1 = require(\"../../compile\");\nconst util_1 = require(\"../../compile/util\");\nconst def = {\n    keyword: \"$ref\",\n    schemaType: \"string\",\n    code(cxt) {\n        const { gen, schema: $ref, it } = cxt;\n        const { baseId, schemaEnv: env, validateName, opts, self } = it;\n        const { root } = env;\n        if (($ref === \"#\" || $ref === \"#/\") && baseId === root.baseId)\n            return callRootRef();\n        const schOrEnv = compile_1.resolveRef.call(self, root, baseId, $ref);\n        if (schOrEnv === undefined)\n            throw new ref_error_1.default(it.opts.uriResolver, baseId, $ref);\n        if (schOrEnv instanceof compile_1.SchemaEnv)\n            return callValidate(schOrEnv);\n        return inlineRefSchema(schOrEnv);\n        function callRootRef() {\n            if (env === root)\n                return callRef(cxt, validateName, env, env.$async);\n            const rootName = gen.scopeValue(\"root\", { ref: root });\n            return callRef(cxt, (0, codegen_1._) `${rootName}.validate`, root, root.$async);\n        }\n        function callValidate(sch) {\n            const v = getValidate(cxt, sch);\n            callRef(cxt, v, sch, sch.$async);\n        }\n        function inlineRefSchema(sch) {\n            const schName = gen.scopeValue(\"schema\", opts.code.source === true ? { ref: sch, code: (0, codegen_1.stringify)(sch) } : { ref: sch });\n            const valid = gen.name(\"valid\");\n            const schCxt = cxt.subschema({\n                schema: sch,\n                dataTypes: [],\n                schemaPath: codegen_1.nil,\n                topSchemaRef: schName,\n                errSchemaPath: $ref,\n            }, valid);\n            cxt.mergeEvaluated(schCxt);\n            cxt.ok(valid);\n        }\n    },\n};\nfunction getValidate(cxt, sch) {\n    const { gen } = cxt;\n    return sch.validate\n        ? gen.scopeValue(\"validate\", { ref: sch.validate })\n        : (0, codegen_1._) `${gen.scopeValue(\"wrapper\", { ref: sch })}.validate`;\n}\nexports.getValidate = getValidate;\nfunction callRef(cxt, v, sch, $async) {\n    const { gen, it } = cxt;\n    const { allErrors, schemaEnv: env, opts } = it;\n    const passCxt = opts.passContext ? names_1.default.this : codegen_1.nil;\n    if ($async)\n        callAsyncRef();\n    else\n        callSyncRef();\n    function callAsyncRef() {\n        if (!env.$async)\n            throw new Error(\"async schema referenced by sync schema\");\n        const valid = gen.let(\"valid\");\n        gen.try(() => {\n            gen.code((0, codegen_1._) `await ${(0, code_1.callValidateCode)(cxt, v, passCxt)}`);\n            addEvaluatedFrom(v); // TODO will not work with async, it has to be returned with the result\n            if (!allErrors)\n                gen.assign(valid, true);\n        }, (e) => {\n            gen.if((0, codegen_1._) `!(${e} instanceof ${it.ValidationError})`, () => gen.throw(e));\n            addErrorsFrom(e);\n            if (!allErrors)\n                gen.assign(valid, false);\n        });\n        cxt.ok(valid);\n    }\n    function callSyncRef() {\n        cxt.result((0, code_1.callValidateCode)(cxt, v, passCxt), () => addEvaluatedFrom(v), () => addErrorsFrom(v));\n    }\n    function addErrorsFrom(source) {\n        const errs = (0, codegen_1._) `${source}.errors`;\n        gen.assign(names_1.default.vErrors, (0, codegen_1._) `${names_1.default.vErrors} === null ? ${errs} : ${names_1.default.vErrors}.concat(${errs})`); // TODO tagged\n        gen.assign(names_1.default.errors, (0, codegen_1._) `${names_1.default.vErrors}.length`);\n    }\n    function addEvaluatedFrom(source) {\n        var _a;\n        if (!it.opts.unevaluated)\n            return;\n        const schEvaluated = (_a = sch === null || sch === void 0 ? void 0 : sch.validate) === null || _a === void 0 ? void 0 : _a.evaluated;\n        // TODO refactor\n        if (it.props !== true) {\n            if (schEvaluated && !schEvaluated.dynamicProps) {\n                if (schEvaluated.props !== undefined) {\n                    it.props = util_1.mergeEvaluated.props(gen, schEvaluated.props, it.props);\n                }\n            }\n            else {\n                const props = gen.var(\"props\", (0, codegen_1._) `${source}.evaluated.props`);\n                it.props = util_1.mergeEvaluated.props(gen, props, it.props, codegen_1.Name);\n            }\n        }\n        if (it.items !== true) {\n            if (schEvaluated && !schEvaluated.dynamicItems) {\n                if (schEvaluated.items !== undefined) {\n                    it.items = util_1.mergeEvaluated.items(gen, schEvaluated.items, it.items);\n                }\n            }\n            else {\n                const items = gen.var(\"items\", (0, codegen_1._) `${source}.evaluated.items`);\n                it.items = util_1.mergeEvaluated.items(gen, items, it.items, codegen_1.Name);\n            }\n        }\n    }\n}\nexports.callRef = callRef;\nexports.default = def;\n//# sourceMappingURL=ref.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst id_1 = require(\"./id\");\nconst ref_1 = require(\"./ref\");\nconst core = [\n    \"$schema\",\n    \"$id\",\n    \"$defs\",\n    \"$vocabulary\",\n    { keyword: \"$comment\" },\n    \"definitions\",\n    id_1.default,\n    ref_1.default,\n];\nexports.default = core;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst ops = codegen_1.operators;\nconst KWDs = {\n    maximum: { okStr: \"<=\", ok: ops.LTE, fail: ops.GT },\n    minimum: { okStr: \">=\", ok: ops.GTE, fail: ops.LT },\n    exclusiveMaximum: { okStr: \"<\", ok: ops.LT, fail: ops.GTE },\n    exclusiveMinimum: { okStr: \">\", ok: ops.GT, fail: ops.LTE },\n};\nconst error = {\n    message: ({ keyword, schemaCode }) => (0, codegen_1.str) `must be ${KWDs[keyword].okStr} ${schemaCode}`,\n    params: ({ keyword, schemaCode }) => (0, codegen_1._) `{comparison: ${KWDs[keyword].okStr}, limit: ${schemaCode}}`,\n};\nconst def = {\n    keyword: Object.keys(KWDs),\n    type: \"number\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { keyword, data, schemaCode } = cxt;\n        cxt.fail$data((0, codegen_1._) `${data} ${KWDs[keyword].fail} ${schemaCode} || isNaN(${data})`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=limitNumber.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message: ({ schemaCode }) => (0, codegen_1.str) `must be multiple of ${schemaCode}`,\n    params: ({ schemaCode }) => (0, codegen_1._) `{multipleOf: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"multipleOf\",\n    type: \"number\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, schemaCode, it } = cxt;\n        // const bdt = bad$DataType(schemaCode, <string>def.schemaType, $data)\n        const prec = it.opts.multipleOfPrecision;\n        const res = gen.let(\"res\");\n        const invalid = prec\n            ? (0, codegen_1._) `Math.abs(Math.round(${res}) - ${res}) > 1e-${prec}`\n            : (0, codegen_1._) `${res} !== parseInt(${res})`;\n        cxt.fail$data((0, codegen_1._) `(${schemaCode} === 0 || (${res} = ${data}/${schemaCode}, ${invalid}))`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=multipleOf.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n// https://mathiasbynens.be/notes/javascript-encoding\n// https://github.com/bestiejs/punycode.js - punycode.ucs2.decode\nfunction ucs2length(str) {\n    const len = str.length;\n    let length = 0;\n    let pos = 0;\n    let value;\n    while (pos < len) {\n        length++;\n        value = str.charCodeAt(pos++);\n        if (value >= 0xd800 && value <= 0xdbff && pos < len) {\n            // high surrogate, and there is a next character\n            value = str.charCodeAt(pos);\n            if ((value & 0xfc00) === 0xdc00)\n                pos++; // low surrogate\n        }\n    }\n    return length;\n}\nexports.default = ucs2length;\nucs2length.code = 'require(\"ajv/dist/runtime/ucs2length\").default';\n//# sourceMappingURL=ucs2length.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst ucs2length_1 = require(\"../../runtime/ucs2length\");\nconst error = {\n    message({ keyword, schemaCode }) {\n        const comp = keyword === \"maxLength\" ? \"more\" : \"fewer\";\n        return (0, codegen_1.str) `must NOT have ${comp} than ${schemaCode} characters`;\n    },\n    params: ({ schemaCode }) => (0, codegen_1._) `{limit: ${schemaCode}}`,\n};\nconst def = {\n    keyword: [\"maxLength\", \"minLength\"],\n    type: \"string\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { keyword, data, schemaCode, it } = cxt;\n        const op = keyword === \"maxLength\" ? codegen_1.operators.GT : codegen_1.operators.LT;\n        const len = it.opts.unicode === false ? (0, codegen_1._) `${data}.length` : (0, codegen_1._) `${(0, util_1.useFunc)(cxt.gen, ucs2length_1.default)}(${data})`;\n        cxt.fail$data((0, codegen_1._) `${len} ${op} ${schemaCode}`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=limitLength.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message: ({ schemaCode }) => (0, codegen_1.str) `must match pattern \"${schemaCode}\"`,\n    params: ({ schemaCode }) => (0, codegen_1._) `{pattern: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"pattern\",\n    type: \"string\",\n    schemaType: \"string\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { data, $data, schema, schemaCode, it } = cxt;\n        // TODO regexp should be wrapped in try/catchs\n        const u = it.opts.unicodeRegExp ? \"u\" : \"\";\n        const regExp = $data ? (0, codegen_1._) `(new RegExp(${schemaCode}, ${u}))` : (0, code_1.usePattern)(cxt, schema);\n        cxt.fail$data((0, codegen_1._) `!${regExp}.test(${data})`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=pattern.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message({ keyword, schemaCode }) {\n        const comp = keyword === \"maxProperties\" ? \"more\" : \"fewer\";\n        return (0, codegen_1.str) `must NOT have ${comp} than ${schemaCode} properties`;\n    },\n    params: ({ schemaCode }) => (0, codegen_1._) `{limit: ${schemaCode}}`,\n};\nconst def = {\n    keyword: [\"maxProperties\", \"minProperties\"],\n    type: \"object\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { keyword, data, schemaCode } = cxt;\n        const op = keyword === \"maxProperties\" ? codegen_1.operators.GT : codegen_1.operators.LT;\n        cxt.fail$data((0, codegen_1._) `Object.keys(${data}).length ${op} ${schemaCode}`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=limitProperties.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params: { missingProperty } }) => (0, codegen_1.str) `must have required property '${missingProperty}'`,\n    params: ({ params: { missingProperty } }) => (0, codegen_1._) `{missingProperty: ${missingProperty}}`,\n};\nconst def = {\n    keyword: \"required\",\n    type: \"object\",\n    schemaType: \"array\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, schema, schemaCode, data, $data, it } = cxt;\n        const { opts } = it;\n        if (!$data && schema.length === 0)\n            return;\n        const useLoop = schema.length >= opts.loopRequired;\n        if (it.allErrors)\n            allErrorsMode();\n        else\n            exitOnErrorMode();\n        if (opts.strictRequired) {\n            const props = cxt.parentSchema.properties;\n            const { definedProperties } = cxt.it;\n            for (const requiredKey of schema) {\n                if ((props === null || props === void 0 ? void 0 : props[requiredKey]) === undefined && !definedProperties.has(requiredKey)) {\n                    const schemaPath = it.schemaEnv.baseId + it.errSchemaPath;\n                    const msg = `required property \"${requiredKey}\" is not defined at \"${schemaPath}\" (strictRequired)`;\n                    (0, util_1.checkStrictMode)(it, msg, it.opts.strictRequired);\n                }\n            }\n        }\n        function allErrorsMode() {\n            if (useLoop || $data) {\n                cxt.block$data(codegen_1.nil, loopAllRequired);\n            }\n            else {\n                for (const prop of schema) {\n                    (0, code_1.checkReportMissingProp)(cxt, prop);\n                }\n            }\n        }\n        function exitOnErrorMode() {\n            const missing = gen.let(\"missing\");\n            if (useLoop || $data) {\n                const valid = gen.let(\"valid\", true);\n                cxt.block$data(valid, () => loopUntilMissing(missing, valid));\n                cxt.ok(valid);\n            }\n            else {\n                gen.if((0, code_1.checkMissingProp)(cxt, schema, missing));\n                (0, code_1.reportMissingProp)(cxt, missing);\n                gen.else();\n            }\n        }\n        function loopAllRequired() {\n            gen.forOf(\"prop\", schemaCode, (prop) => {\n                cxt.setParams({ missingProperty: prop });\n                gen.if((0, code_1.noPropertyInData)(gen, data, prop, opts.ownProperties), () => cxt.error());\n            });\n        }\n        function loopUntilMissing(missing, valid) {\n            cxt.setParams({ missingProperty: missing });\n            gen.forOf(missing, schemaCode, () => {\n                gen.assign(valid, (0, code_1.propertyInData)(gen, data, missing, opts.ownProperties));\n                gen.if((0, codegen_1.not)(valid), () => {\n                    cxt.error();\n                    gen.break();\n                });\n            }, codegen_1.nil);\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=required.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message({ keyword, schemaCode }) {\n        const comp = keyword === \"maxItems\" ? \"more\" : \"fewer\";\n        return (0, codegen_1.str) `must NOT have ${comp} than ${schemaCode} items`;\n    },\n    params: ({ schemaCode }) => (0, codegen_1._) `{limit: ${schemaCode}}`,\n};\nconst def = {\n    keyword: [\"maxItems\", \"minItems\"],\n    type: \"array\",\n    schemaType: \"number\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { keyword, data, schemaCode } = cxt;\n        const op = keyword === \"maxItems\" ? codegen_1.operators.GT : codegen_1.operators.LT;\n        cxt.fail$data((0, codegen_1._) `${data}.length ${op} ${schemaCode}`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=limitItems.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n// https://github.com/ajv-validator/ajv/issues/889\nconst equal = require(\"fast-deep-equal\");\nequal.code = 'require(\"ajv/dist/runtime/equal\").default';\nexports.default = equal;\n//# sourceMappingURL=equal.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst limitNumber_1 = require(\"./limitNumber\");\nconst multipleOf_1 = require(\"./multipleOf\");\nconst limitLength_1 = require(\"./limitLength\");\nconst pattern_1 = require(\"./pattern\");\nconst limitProperties_1 = require(\"./limitProperties\");\nconst required_1 = require(\"./required\");\nconst limitItems_1 = require(\"./limitItems\");\nconst uniqueItems_1 = require(\"./uniqueItems\");\nconst const_1 = require(\"./const\");\nconst enum_1 = require(\"./enum\");\nconst validation = [\n    // number\n    limitNumber_1.default,\n    multipleOf_1.default,\n    // string\n    limitLength_1.default,\n    pattern_1.default,\n    // object\n    limitProperties_1.default,\n    required_1.default,\n    // array\n    limitItems_1.default,\n    uniqueItems_1.default,\n    // any\n    { keyword: \"type\", schemaType: [\"string\", \"array\"] },\n    { keyword: \"nullable\", schemaType: \"boolean\" },\n    const_1.default,\n    enum_1.default,\n];\nexports.default = validation;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst dataType_1 = require(\"../../compile/validate/dataType\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst equal_1 = require(\"../../runtime/equal\");\nconst error = {\n    message: ({ params: { i, j } }) => (0, codegen_1.str) `must NOT have duplicate items (items ## ${j} and ${i} are identical)`,\n    params: ({ params: { i, j } }) => (0, codegen_1._) `{i: ${i}, j: ${j}}`,\n};\nconst def = {\n    keyword: \"uniqueItems\",\n    type: \"array\",\n    schemaType: \"boolean\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, $data, schema, parentSchema, schemaCode, it } = cxt;\n        if (!$data && !schema)\n            return;\n        const valid = gen.let(\"valid\");\n        const itemTypes = parentSchema.items ? (0, dataType_1.getSchemaTypes)(parentSchema.items) : [];\n        cxt.block$data(valid, validateUniqueItems, (0, codegen_1._) `${schemaCode} === false`);\n        cxt.ok(valid);\n        function validateUniqueItems() {\n            const i = gen.let(\"i\", (0, codegen_1._) `${data}.length`);\n            const j = gen.let(\"j\");\n            cxt.setParams({ i, j });\n            gen.assign(valid, true);\n            gen.if((0, codegen_1._) `${i} > 1`, () => (canOptimize() ? loopN : loopN2)(i, j));\n        }\n        function canOptimize() {\n            return itemTypes.length > 0 && !itemTypes.some((t) => t === \"object\" || t === \"array\");\n        }\n        function loopN(i, j) {\n            const item = gen.name(\"item\");\n            const wrongType = (0, dataType_1.checkDataTypes)(itemTypes, item, it.opts.strictNumbers, dataType_1.DataType.Wrong);\n            const indices = gen.const(\"indices\", (0, codegen_1._) `{}`);\n            gen.for((0, codegen_1._) `;${i}--;`, () => {\n                gen.let(item, (0, codegen_1._) `${data}[${i}]`);\n                gen.if(wrongType, (0, codegen_1._) `continue`);\n                if (itemTypes.length > 1)\n                    gen.if((0, codegen_1._) `typeof ${item} == \"string\"`, (0, codegen_1._) `${item} += \"_\"`);\n                gen\n                    .if((0, codegen_1._) `typeof ${indices}[${item}] == \"number\"`, () => {\n                    gen.assign(j, (0, codegen_1._) `${indices}[${item}]`);\n                    cxt.error();\n                    gen.assign(valid, false).break();\n                })\n                    .code((0, codegen_1._) `${indices}[${item}] = ${i}`);\n            });\n        }\n        function loopN2(i, j) {\n            const eql = (0, util_1.useFunc)(gen, equal_1.default);\n            const outer = gen.name(\"outer\");\n            gen.label(outer).for((0, codegen_1._) `;${i}--;`, () => gen.for((0, codegen_1._) `${j} = ${i}; ${j}--;`, () => gen.if((0, codegen_1._) `${eql}(${data}[${i}], ${data}[${j}])`, () => {\n                cxt.error();\n                gen.assign(valid, false).break(outer);\n            })));\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=uniqueItems.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst equal_1 = require(\"../../runtime/equal\");\nconst error = {\n    message: \"must be equal to constant\",\n    params: ({ schemaCode }) => (0, codegen_1._) `{allowedValue: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"const\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, $data, schemaCode, schema } = cxt;\n        if ($data || (schema && typeof schema == \"object\")) {\n            cxt.fail$data((0, codegen_1._) `!${(0, util_1.useFunc)(gen, equal_1.default)}(${data}, ${schemaCode})`);\n        }\n        else {\n            cxt.fail((0, codegen_1._) `${schema} !== ${data}`);\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=const.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst equal_1 = require(\"../../runtime/equal\");\nconst error = {\n    message: \"must be equal to one of the allowed values\",\n    params: ({ schemaCode }) => (0, codegen_1._) `{allowedValues: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"enum\",\n    schemaType: \"array\",\n    $data: true,\n    error,\n    code(cxt) {\n        const { gen, data, $data, schema, schemaCode, it } = cxt;\n        if (!$data && schema.length === 0)\n            throw new Error(\"enum must have non-empty array\");\n        const useLoop = schema.length >= it.opts.loopEnum;\n        let eql;\n        const getEql = () => (eql !== null && eql !== void 0 ? eql : (eql = (0, util_1.useFunc)(gen, equal_1.default)));\n        let valid;\n        if (useLoop || $data) {\n            valid = gen.let(\"valid\");\n            cxt.block$data(valid, loopEnum);\n        }\n        else {\n            /* istanbul ignore if */\n            if (!Array.isArray(schema))\n                throw new Error(\"ajv implementation error\");\n            const vSchema = gen.const(\"vSchema\", schemaCode);\n            valid = (0, codegen_1.or)(...schema.map((_x, i) => equalCode(vSchema, i)));\n        }\n        cxt.pass(valid);\n        function loopEnum() {\n            gen.assign(valid, false);\n            gen.forOf(\"v\", schemaCode, (v) => gen.if((0, codegen_1._) `${getEql()}(${data}, ${v})`, () => gen.assign(valid, true).break()));\n        }\n        function equalCode(vSchema, i) {\n            const sch = schema[i];\n            return typeof sch === \"object\" && sch !== null\n                ? (0, codegen_1._) `${getEql()}(${data}, ${vSchema}[${i}])`\n                : (0, codegen_1._) `${data} === ${sch}`;\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=enum.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateAdditionalItems = void 0;\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params: { len } }) => (0, codegen_1.str) `must NOT have more than ${len} items`,\n    params: ({ params: { len } }) => (0, codegen_1._) `{limit: ${len}}`,\n};\nconst def = {\n    keyword: \"additionalItems\",\n    type: \"array\",\n    schemaType: [\"boolean\", \"object\"],\n    before: \"uniqueItems\",\n    error,\n    code(cxt) {\n        const { parentSchema, it } = cxt;\n        const { items } = parentSchema;\n        if (!Array.isArray(items)) {\n            (0, util_1.checkStrictMode)(it, '\"additionalItems\" is ignored when \"items\" is not an array of schemas');\n            return;\n        }\n        validateAdditionalItems(cxt, items);\n    },\n};\nfunction validateAdditionalItems(cxt, items) {\n    const { gen, schema, data, keyword, it } = cxt;\n    it.items = true;\n    const len = gen.const(\"len\", (0, codegen_1._) `${data}.length`);\n    if (schema === false) {\n        cxt.setParams({ len: items.length });\n        cxt.pass((0, codegen_1._) `${len} <= ${items.length}`);\n    }\n    else if (typeof schema == \"object\" && !(0, util_1.alwaysValidSchema)(it, schema)) {\n        const valid = gen.var(\"valid\", (0, codegen_1._) `${len} <= ${items.length}`); // TODO var\n        gen.if((0, codegen_1.not)(valid), () => validateItems(valid));\n        cxt.ok(valid);\n    }\n    function validateItems(valid) {\n        gen.forRange(\"i\", items.length, len, (i) => {\n            cxt.subschema({ keyword, dataProp: i, dataPropType: util_1.Type.Num }, valid);\n            if (!it.allErrors)\n                gen.if((0, codegen_1.not)(valid), () => gen.break());\n        });\n    }\n}\nexports.validateAdditionalItems = validateAdditionalItems;\nexports.default = def;\n//# sourceMappingURL=additionalItems.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateTuple = void 0;\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst code_1 = require(\"../code\");\nconst def = {\n    keyword: \"items\",\n    type: \"array\",\n    schemaType: [\"object\", \"array\", \"boolean\"],\n    before: \"uniqueItems\",\n    code(cxt) {\n        const { schema, it } = cxt;\n        if (Array.isArray(schema))\n            return validateTuple(cxt, \"additionalItems\", schema);\n        it.items = true;\n        if ((0, util_1.alwaysValidSchema)(it, schema))\n            return;\n        cxt.ok((0, code_1.validateArray)(cxt));\n    },\n};\nfunction validateTuple(cxt, extraItems, schArr = cxt.schema) {\n    const { gen, parentSchema, data, keyword, it } = cxt;\n    checkStrictTuple(parentSchema);\n    if (it.opts.unevaluated && schArr.length && it.items !== true) {\n        it.items = util_1.mergeEvaluated.items(gen, schArr.length, it.items);\n    }\n    const valid = gen.name(\"valid\");\n    const len = gen.const(\"len\", (0, codegen_1._) `${data}.length`);\n    schArr.forEach((sch, i) => {\n        if ((0, util_1.alwaysValidSchema)(it, sch))\n            return;\n        gen.if((0, codegen_1._) `${len} > ${i}`, () => cxt.subschema({\n            keyword,\n            schemaProp: i,\n            dataProp: i,\n        }, valid));\n        cxt.ok(valid);\n    });\n    function checkStrictTuple(sch) {\n        const { opts, errSchemaPath } = it;\n        const l = schArr.length;\n        const fullTuple = l === sch.minItems && (l === sch.maxItems || sch[extraItems] === false);\n        if (opts.strictTuples && !fullTuple) {\n            const msg = `\"${keyword}\" is ${l}-tuple, but minItems or maxItems/${extraItems} are not specified or different at path \"${errSchemaPath}\"`;\n            (0, util_1.checkStrictMode)(it, msg, opts.strictTuples);\n        }\n    }\n}\nexports.validateTuple = validateTuple;\nexports.default = def;\n//# sourceMappingURL=items.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst items_1 = require(\"./items\");\nconst def = {\n    keyword: \"prefixItems\",\n    type: \"array\",\n    schemaType: [\"array\"],\n    before: \"uniqueItems\",\n    code: (cxt) => (0, items_1.validateTuple)(cxt, \"items\"),\n};\nexports.default = def;\n//# sourceMappingURL=prefixItems.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst code_1 = require(\"../code\");\nconst additionalItems_1 = require(\"./additionalItems\");\nconst error = {\n    message: ({ params: { len } }) => (0, codegen_1.str) `must NOT have more than ${len} items`,\n    params: ({ params: { len } }) => (0, codegen_1._) `{limit: ${len}}`,\n};\nconst def = {\n    keyword: \"items\",\n    type: \"array\",\n    schemaType: [\"object\", \"boolean\"],\n    before: \"uniqueItems\",\n    error,\n    code(cxt) {\n        const { schema, parentSchema, it } = cxt;\n        const { prefixItems } = parentSchema;\n        it.items = true;\n        if ((0, util_1.alwaysValidSchema)(it, schema))\n            return;\n        if (prefixItems)\n            (0, additionalItems_1.validateAdditionalItems)(cxt, prefixItems);\n        else\n            cxt.ok((0, code_1.validateArray)(cxt));\n    },\n};\nexports.default = def;\n//# sourceMappingURL=items2020.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params: { min, max } }) => max === undefined\n        ? (0, codegen_1.str) `must contain at least ${min} valid item(s)`\n        : (0, codegen_1.str) `must contain at least ${min} and no more than ${max} valid item(s)`,\n    params: ({ params: { min, max } }) => max === undefined ? (0, codegen_1._) `{minContains: ${min}}` : (0, codegen_1._) `{minContains: ${min}, maxContains: ${max}}`,\n};\nconst def = {\n    keyword: \"contains\",\n    type: \"array\",\n    schemaType: [\"object\", \"boolean\"],\n    before: \"uniqueItems\",\n    trackErrors: true,\n    error,\n    code(cxt) {\n        const { gen, schema, parentSchema, data, it } = cxt;\n        let min;\n        let max;\n        const { minContains, maxContains } = parentSchema;\n        if (it.opts.next) {\n            min = minContains === undefined ? 1 : minContains;\n            max = maxContains;\n        }\n        else {\n            min = 1;\n        }\n        const len = gen.const(\"len\", (0, codegen_1._) `${data}.length`);\n        cxt.setParams({ min, max });\n        if (max === undefined && min === 0) {\n            (0, util_1.checkStrictMode)(it, `\"minContains\" == 0 without \"maxContains\": \"contains\" keyword ignored`);\n            return;\n        }\n        if (max !== undefined && min > max) {\n            (0, util_1.checkStrictMode)(it, `\"minContains\" > \"maxContains\" is always invalid`);\n            cxt.fail();\n            return;\n        }\n        if ((0, util_1.alwaysValidSchema)(it, schema)) {\n            let cond = (0, codegen_1._) `${len} >= ${min}`;\n            if (max !== undefined)\n                cond = (0, codegen_1._) `${cond} && ${len} <= ${max}`;\n            cxt.pass(cond);\n            return;\n        }\n        it.items = true;\n        const valid = gen.name(\"valid\");\n        if (max === undefined && min === 1) {\n            validateItems(valid, () => gen.if(valid, () => gen.break()));\n        }\n        else if (min === 0) {\n            gen.let(valid, true);\n            if (max !== undefined)\n                gen.if((0, codegen_1._) `${data}.length > 0`, validateItemsWithCount);\n        }\n        else {\n            gen.let(valid, false);\n            validateItemsWithCount();\n        }\n        cxt.result(valid, () => cxt.reset());\n        function validateItemsWithCount() {\n            const schValid = gen.name(\"_valid\");\n            const count = gen.let(\"count\", 0);\n            validateItems(schValid, () => gen.if(schValid, () => checkLimits(count)));\n        }\n        function validateItems(_valid, block) {\n            gen.forRange(\"i\", 0, len, (i) => {\n                cxt.subschema({\n                    keyword: \"contains\",\n                    dataProp: i,\n                    dataPropType: util_1.Type.Num,\n                    compositeRule: true,\n                }, _valid);\n                block();\n            });\n        }\n        function checkLimits(count) {\n            gen.code((0, codegen_1._) `${count}++`);\n            if (max === undefined) {\n                gen.if((0, codegen_1._) `${count} >= ${min}`, () => gen.assign(valid, true).break());\n            }\n            else {\n                gen.if((0, codegen_1._) `${count} > ${max}`, () => gen.assign(valid, false).break());\n                if (min === 1)\n                    gen.assign(valid, true);\n                else\n                    gen.if((0, codegen_1._) `${count} >= ${min}`, () => gen.assign(valid, true));\n            }\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=contains.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateSchemaDeps = exports.validatePropertyDeps = exports.error = void 0;\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst code_1 = require(\"../code\");\nexports.error = {\n    message: ({ params: { property, depsCount, deps } }) => {\n        const property_ies = depsCount === 1 ? \"property\" : \"properties\";\n        return (0, codegen_1.str) `must have ${property_ies} ${deps} when property ${property} is present`;\n    },\n    params: ({ params: { property, depsCount, deps, missingProperty } }) => (0, codegen_1._) `{property: ${property},\n    missingProperty: ${missingProperty},\n    depsCount: ${depsCount},\n    deps: ${deps}}`, // TODO change to reference\n};\nconst def = {\n    keyword: \"dependencies\",\n    type: \"object\",\n    schemaType: \"object\",\n    error: exports.error,\n    code(cxt) {\n        const [propDeps, schDeps] = splitDependencies(cxt);\n        validatePropertyDeps(cxt, propDeps);\n        validateSchemaDeps(cxt, schDeps);\n    },\n};\nfunction splitDependencies({ schema }) {\n    const propertyDeps = {};\n    const schemaDeps = {};\n    for (const key in schema) {\n        if (key === \"__proto__\")\n            continue;\n        const deps = Array.isArray(schema[key]) ? propertyDeps : schemaDeps;\n        deps[key] = schema[key];\n    }\n    return [propertyDeps, schemaDeps];\n}\nfunction validatePropertyDeps(cxt, propertyDeps = cxt.schema) {\n    const { gen, data, it } = cxt;\n    if (Object.keys(propertyDeps).length === 0)\n        return;\n    const missing = gen.let(\"missing\");\n    for (const prop in propertyDeps) {\n        const deps = propertyDeps[prop];\n        if (deps.length === 0)\n            continue;\n        const hasProperty = (0, code_1.propertyInData)(gen, data, prop, it.opts.ownProperties);\n        cxt.setParams({\n            property: prop,\n            depsCount: deps.length,\n            deps: deps.join(\", \"),\n        });\n        if (it.allErrors) {\n            gen.if(hasProperty, () => {\n                for (const depProp of deps) {\n                    (0, code_1.checkReportMissingProp)(cxt, depProp);\n                }\n            });\n        }\n        else {\n            gen.if((0, codegen_1._) `${hasProperty} && (${(0, code_1.checkMissingProp)(cxt, deps, missing)})`);\n            (0, code_1.reportMissingProp)(cxt, missing);\n            gen.else();\n        }\n    }\n}\nexports.validatePropertyDeps = validatePropertyDeps;\nfunction validateSchemaDeps(cxt, schemaDeps = cxt.schema) {\n    const { gen, data, keyword, it } = cxt;\n    const valid = gen.name(\"valid\");\n    for (const prop in schemaDeps) {\n        if ((0, util_1.alwaysValidSchema)(it, schemaDeps[prop]))\n            continue;\n        gen.if((0, code_1.propertyInData)(gen, data, prop, it.opts.ownProperties), () => {\n            const schCxt = cxt.subschema({ keyword, schemaProp: prop }, valid);\n            cxt.mergeValidEvaluated(schCxt, valid);\n        }, () => gen.var(valid, true) // TODO var\n        );\n        cxt.ok(valid);\n    }\n}\nexports.validateSchemaDeps = validateSchemaDeps;\nexports.default = def;\n//# sourceMappingURL=dependencies.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: \"property name must be valid\",\n    params: ({ params }) => (0, codegen_1._) `{propertyName: ${params.propertyName}}`,\n};\nconst def = {\n    keyword: \"propertyNames\",\n    type: \"object\",\n    schemaType: [\"object\", \"boolean\"],\n    error,\n    code(cxt) {\n        const { gen, schema, data, it } = cxt;\n        if ((0, util_1.alwaysValidSchema)(it, schema))\n            return;\n        const valid = gen.name(\"valid\");\n        gen.forIn(\"key\", data, (key) => {\n            cxt.setParams({ propertyName: key });\n            cxt.subschema({\n                keyword: \"propertyNames\",\n                data: key,\n                dataTypes: [\"string\"],\n                propertyName: key,\n                compositeRule: true,\n            }, valid);\n            gen.if((0, codegen_1.not)(valid), () => {\n                cxt.error(true);\n                if (!it.allErrors)\n                    gen.break();\n            });\n        });\n        cxt.ok(valid);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=propertyNames.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst names_1 = require(\"../../compile/names\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: \"must NOT have additional properties\",\n    params: ({ params }) => (0, codegen_1._) `{additionalProperty: ${params.additionalProperty}}`,\n};\nconst def = {\n    keyword: \"additionalProperties\",\n    type: [\"object\"],\n    schemaType: [\"boolean\", \"object\"],\n    allowUndefined: true,\n    trackErrors: true,\n    error,\n    code(cxt) {\n        const { gen, schema, parentSchema, data, errsCount, it } = cxt;\n        /* istanbul ignore if */\n        if (!errsCount)\n            throw new Error(\"ajv implementation error\");\n        const { allErrors, opts } = it;\n        it.props = true;\n        if (opts.removeAdditional !== \"all\" && (0, util_1.alwaysValidSchema)(it, schema))\n            return;\n        const props = (0, code_1.allSchemaProperties)(parentSchema.properties);\n        const patProps = (0, code_1.allSchemaProperties)(parentSchema.patternProperties);\n        checkAdditionalProperties();\n        cxt.ok((0, codegen_1._) `${errsCount} === ${names_1.default.errors}`);\n        function checkAdditionalProperties() {\n            gen.forIn(\"key\", data, (key) => {\n                if (!props.length && !patProps.length)\n                    additionalPropertyCode(key);\n                else\n                    gen.if(isAdditional(key), () => additionalPropertyCode(key));\n            });\n        }\n        function isAdditional(key) {\n            let definedProp;\n            if (props.length > 8) {\n                // TODO maybe an option instead of hard-coded 8?\n                const propsSchema = (0, util_1.schemaRefOrVal)(it, parentSchema.properties, \"properties\");\n                definedProp = (0, code_1.isOwnProperty)(gen, propsSchema, key);\n            }\n            else if (props.length) {\n                definedProp = (0, codegen_1.or)(...props.map((p) => (0, codegen_1._) `${key} === ${p}`));\n            }\n            else {\n                definedProp = codegen_1.nil;\n            }\n            if (patProps.length) {\n                definedProp = (0, codegen_1.or)(definedProp, ...patProps.map((p) => (0, codegen_1._) `${(0, code_1.usePattern)(cxt, p)}.test(${key})`));\n            }\n            return (0, codegen_1.not)(definedProp);\n        }\n        function deleteAdditional(key) {\n            gen.code((0, codegen_1._) `delete ${data}[${key}]`);\n        }\n        function additionalPropertyCode(key) {\n            if (opts.removeAdditional === \"all\" || (opts.removeAdditional && schema === false)) {\n                deleteAdditional(key);\n                return;\n            }\n            if (schema === false) {\n                cxt.setParams({ additionalProperty: key });\n                cxt.error();\n                if (!allErrors)\n                    gen.break();\n                return;\n            }\n            if (typeof schema == \"object\" && !(0, util_1.alwaysValidSchema)(it, schema)) {\n                const valid = gen.name(\"valid\");\n                if (opts.removeAdditional === \"failing\") {\n                    applyAdditionalSchema(key, valid, false);\n                    gen.if((0, codegen_1.not)(valid), () => {\n                        cxt.reset();\n                        deleteAdditional(key);\n                    });\n                }\n                else {\n                    applyAdditionalSchema(key, valid);\n                    if (!allErrors)\n                        gen.if((0, codegen_1.not)(valid), () => gen.break());\n                }\n            }\n        }\n        function applyAdditionalSchema(key, valid, errors) {\n            const subschema = {\n                keyword: \"additionalProperties\",\n                dataProp: key,\n                dataPropType: util_1.Type.Str,\n            };\n            if (errors === false) {\n                Object.assign(subschema, {\n                    compositeRule: true,\n                    createErrors: false,\n                    allErrors: false,\n                });\n            }\n            cxt.subschema(subschema, valid);\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=additionalProperties.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst validate_1 = require(\"../../compile/validate\");\nconst code_1 = require(\"../code\");\nconst util_1 = require(\"../../compile/util\");\nconst additionalProperties_1 = require(\"./additionalProperties\");\nconst def = {\n    keyword: \"properties\",\n    type: \"object\",\n    schemaType: \"object\",\n    code(cxt) {\n        const { gen, schema, parentSchema, data, it } = cxt;\n        if (it.opts.removeAdditional === \"all\" && parentSchema.additionalProperties === undefined) {\n            additionalProperties_1.default.code(new validate_1.KeywordCxt(it, additionalProperties_1.default, \"additionalProperties\"));\n        }\n        const allProps = (0, code_1.allSchemaProperties)(schema);\n        for (const prop of allProps) {\n            it.definedProperties.add(prop);\n        }\n        if (it.opts.unevaluated && allProps.length && it.props !== true) {\n            it.props = util_1.mergeEvaluated.props(gen, (0, util_1.toHash)(allProps), it.props);\n        }\n        const properties = allProps.filter((p) => !(0, util_1.alwaysValidSchema)(it, schema[p]));\n        if (properties.length === 0)\n            return;\n        const valid = gen.name(\"valid\");\n        for (const prop of properties) {\n            if (hasDefault(prop)) {\n                applyPropertySchema(prop);\n            }\n            else {\n                gen.if((0, code_1.propertyInData)(gen, data, prop, it.opts.ownProperties));\n                applyPropertySchema(prop);\n                if (!it.allErrors)\n                    gen.else().var(valid, true);\n                gen.endIf();\n            }\n            cxt.it.definedProperties.add(prop);\n            cxt.ok(valid);\n        }\n        function hasDefault(prop) {\n            return it.opts.useDefaults && !it.compositeRule && schema[prop].default !== undefined;\n        }\n        function applyPropertySchema(prop) {\n            cxt.subschema({\n                keyword: \"properties\",\n                schemaProp: prop,\n                dataProp: prop,\n            }, valid);\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=properties.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst util_2 = require(\"../../compile/util\");\nconst def = {\n    keyword: \"patternProperties\",\n    type: \"object\",\n    schemaType: \"object\",\n    code(cxt) {\n        const { gen, schema, data, parentSchema, it } = cxt;\n        const { opts } = it;\n        const patterns = (0, code_1.allSchemaProperties)(schema);\n        const alwaysValidPatterns = patterns.filter((p) => (0, util_1.alwaysValidSchema)(it, schema[p]));\n        if (patterns.length === 0 ||\n            (alwaysValidPatterns.length === patterns.length &&\n                (!it.opts.unevaluated || it.props === true))) {\n            return;\n        }\n        const checkProperties = opts.strictSchema && !opts.allowMatchingProperties && parentSchema.properties;\n        const valid = gen.name(\"valid\");\n        if (it.props !== true && !(it.props instanceof codegen_1.Name)) {\n            it.props = (0, util_2.evaluatedPropsToName)(gen, it.props);\n        }\n        const { props } = it;\n        validatePatternProperties();\n        function validatePatternProperties() {\n            for (const pat of patterns) {\n                if (checkProperties)\n                    checkMatchingProperties(pat);\n                if (it.allErrors) {\n                    validateProperties(pat);\n                }\n                else {\n                    gen.var(valid, true); // TODO var\n                    validateProperties(pat);\n                    gen.if(valid);\n                }\n            }\n        }\n        function checkMatchingProperties(pat) {\n            for (const prop in checkProperties) {\n                if (new RegExp(pat).test(prop)) {\n                    (0, util_1.checkStrictMode)(it, `property ${prop} matches pattern ${pat} (use allowMatchingProperties)`);\n                }\n            }\n        }\n        function validateProperties(pat) {\n            gen.forIn(\"key\", data, (key) => {\n                gen.if((0, codegen_1._) `${(0, code_1.usePattern)(cxt, pat)}.test(${key})`, () => {\n                    const alwaysValid = alwaysValidPatterns.includes(pat);\n                    if (!alwaysValid) {\n                        cxt.subschema({\n                            keyword: \"patternProperties\",\n                            schemaProp: pat,\n                            dataProp: key,\n                            dataPropType: util_2.Type.Str,\n                        }, valid);\n                    }\n                    if (it.opts.unevaluated && props !== true) {\n                        gen.assign((0, codegen_1._) `${props}[${key}]`, true);\n                    }\n                    else if (!alwaysValid && !it.allErrors) {\n                        // can short-circuit if `unevaluatedProperties` is not supported (opts.next === false)\n                        // or if all properties were evaluated (props === true)\n                        gen.if((0, codegen_1.not)(valid), () => gen.break());\n                    }\n                });\n            });\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=patternProperties.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"../../compile/util\");\nconst def = {\n    keyword: \"not\",\n    schemaType: [\"object\", \"boolean\"],\n    trackErrors: true,\n    code(cxt) {\n        const { gen, schema, it } = cxt;\n        if ((0, util_1.alwaysValidSchema)(it, schema)) {\n            cxt.fail();\n            return;\n        }\n        const valid = gen.name(\"valid\");\n        cxt.subschema({\n            keyword: \"not\",\n            compositeRule: true,\n            createErrors: false,\n            allErrors: false,\n        }, valid);\n        cxt.failResult(valid, () => cxt.reset(), () => cxt.error());\n    },\n    error: { message: \"must NOT be valid\" },\n};\nexports.default = def;\n//# sourceMappingURL=not.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst code_1 = require(\"../code\");\nconst def = {\n    keyword: \"anyOf\",\n    schemaType: \"array\",\n    trackErrors: true,\n    code: code_1.validateUnion,\n    error: { message: \"must match a schema in anyOf\" },\n};\nexports.default = def;\n//# sourceMappingURL=anyOf.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: \"must match exactly one schema in oneOf\",\n    params: ({ params }) => (0, codegen_1._) `{passingSchemas: ${params.passing}}`,\n};\nconst def = {\n    keyword: \"oneOf\",\n    schemaType: \"array\",\n    trackErrors: true,\n    error,\n    code(cxt) {\n        const { gen, schema, parentSchema, it } = cxt;\n        /* istanbul ignore if */\n        if (!Array.isArray(schema))\n            throw new Error(\"ajv implementation error\");\n        if (it.opts.discriminator && parentSchema.discriminator)\n            return;\n        const schArr = schema;\n        const valid = gen.let(\"valid\", false);\n        const passing = gen.let(\"passing\", null);\n        const schValid = gen.name(\"_valid\");\n        cxt.setParams({ passing });\n        // TODO possibly fail straight away (with warning or exception) if there are two empty always valid schemas\n        gen.block(validateOneOf);\n        cxt.result(valid, () => cxt.reset(), () => cxt.error(true));\n        function validateOneOf() {\n            schArr.forEach((sch, i) => {\n                let schCxt;\n                if ((0, util_1.alwaysValidSchema)(it, sch)) {\n                    gen.var(schValid, true);\n                }\n                else {\n                    schCxt = cxt.subschema({\n                        keyword: \"oneOf\",\n                        schemaProp: i,\n                        compositeRule: true,\n                    }, schValid);\n                }\n                if (i > 0) {\n                    gen\n                        .if((0, codegen_1._) `${schValid} && ${valid}`)\n                        .assign(valid, false)\n                        .assign(passing, (0, codegen_1._) `[${passing}, ${i}]`)\n                        .else();\n                }\n                gen.if(schValid, () => {\n                    gen.assign(valid, true);\n                    gen.assign(passing, i);\n                    if (schCxt)\n                        cxt.mergeEvaluated(schCxt, codegen_1.Name);\n                });\n            });\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=oneOf.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"../../compile/util\");\nconst def = {\n    keyword: \"allOf\",\n    schemaType: \"array\",\n    code(cxt) {\n        const { gen, schema, it } = cxt;\n        /* istanbul ignore if */\n        if (!Array.isArray(schema))\n            throw new Error(\"ajv implementation error\");\n        const valid = gen.name(\"valid\");\n        schema.forEach((sch, i) => {\n            if ((0, util_1.alwaysValidSchema)(it, sch))\n                return;\n            const schCxt = cxt.subschema({ keyword: \"allOf\", schemaProp: i }, valid);\n            cxt.ok(valid);\n            cxt.mergeEvaluated(schCxt);\n        });\n    },\n};\nexports.default = def;\n//# sourceMappingURL=allOf.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params }) => (0, codegen_1.str) `must match \"${params.ifClause}\" schema`,\n    params: ({ params }) => (0, codegen_1._) `{failingKeyword: ${params.ifClause}}`,\n};\nconst def = {\n    keyword: \"if\",\n    schemaType: [\"object\", \"boolean\"],\n    trackErrors: true,\n    error,\n    code(cxt) {\n        const { gen, parentSchema, it } = cxt;\n        if (parentSchema.then === undefined && parentSchema.else === undefined) {\n            (0, util_1.checkStrictMode)(it, '\"if\" without \"then\" and \"else\" is ignored');\n        }\n        const hasThen = hasSchema(it, \"then\");\n        const hasElse = hasSchema(it, \"else\");\n        if (!hasThen && !hasElse)\n            return;\n        const valid = gen.let(\"valid\", true);\n        const schValid = gen.name(\"_valid\");\n        validateIf();\n        cxt.reset();\n        if (hasThen && hasElse) {\n            const ifClause = gen.let(\"ifClause\");\n            cxt.setParams({ ifClause });\n            gen.if(schValid, validateClause(\"then\", ifClause), validateClause(\"else\", ifClause));\n        }\n        else if (hasThen) {\n            gen.if(schValid, validateClause(\"then\"));\n        }\n        else {\n            gen.if((0, codegen_1.not)(schValid), validateClause(\"else\"));\n        }\n        cxt.pass(valid, () => cxt.error(true));\n        function validateIf() {\n            const schCxt = cxt.subschema({\n                keyword: \"if\",\n                compositeRule: true,\n                createErrors: false,\n                allErrors: false,\n            }, schValid);\n            cxt.mergeEvaluated(schCxt);\n        }\n        function validateClause(keyword, ifClause) {\n            return () => {\n                const schCxt = cxt.subschema({ keyword }, schValid);\n                gen.assign(valid, schValid);\n                cxt.mergeValidEvaluated(schCxt, valid);\n                if (ifClause)\n                    gen.assign(ifClause, (0, codegen_1._) `${keyword}`);\n                else\n                    cxt.setParams({ ifClause: keyword });\n            };\n        }\n    },\n};\nfunction hasSchema(it, keyword) {\n    const schema = it.schema[keyword];\n    return schema !== undefined && !(0, util_1.alwaysValidSchema)(it, schema);\n}\nexports.default = def;\n//# sourceMappingURL=if.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"../../compile/util\");\nconst def = {\n    keyword: [\"then\", \"else\"],\n    schemaType: [\"object\", \"boolean\"],\n    code({ keyword, parentSchema, it }) {\n        if (parentSchema.if === undefined)\n            (0, util_1.checkStrictMode)(it, `\"${keyword}\" without \"if\" is ignored`);\n    },\n};\nexports.default = def;\n//# sourceMappingURL=thenElse.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst additionalItems_1 = require(\"./additionalItems\");\nconst prefixItems_1 = require(\"./prefixItems\");\nconst items_1 = require(\"./items\");\nconst items2020_1 = require(\"./items2020\");\nconst contains_1 = require(\"./contains\");\nconst dependencies_1 = require(\"./dependencies\");\nconst propertyNames_1 = require(\"./propertyNames\");\nconst additionalProperties_1 = require(\"./additionalProperties\");\nconst properties_1 = require(\"./properties\");\nconst patternProperties_1 = require(\"./patternProperties\");\nconst not_1 = require(\"./not\");\nconst anyOf_1 = require(\"./anyOf\");\nconst oneOf_1 = require(\"./oneOf\");\nconst allOf_1 = require(\"./allOf\");\nconst if_1 = require(\"./if\");\nconst thenElse_1 = require(\"./thenElse\");\nfunction getApplicator(draft2020 = false) {\n    const applicator = [\n        // any\n        not_1.default,\n        anyOf_1.default,\n        oneOf_1.default,\n        allOf_1.default,\n        if_1.default,\n        thenElse_1.default,\n        // object\n        propertyNames_1.default,\n        additionalProperties_1.default,\n        dependencies_1.default,\n        properties_1.default,\n        patternProperties_1.default,\n    ];\n    // array\n    if (draft2020)\n        applicator.push(prefixItems_1.default, items2020_1.default);\n    else\n        applicator.push(additionalItems_1.default, items_1.default);\n    applicator.push(contains_1.default);\n    return applicator;\n}\nexports.default = getApplicator;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst format_1 = require(\"./format\");\nconst format = [format_1.default];\nexports.default = format;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst error = {\n    message: ({ schemaCode }) => (0, codegen_1.str) `must match format \"${schemaCode}\"`,\n    params: ({ schemaCode }) => (0, codegen_1._) `{format: ${schemaCode}}`,\n};\nconst def = {\n    keyword: \"format\",\n    type: [\"number\", \"string\"],\n    schemaType: \"string\",\n    $data: true,\n    error,\n    code(cxt, ruleType) {\n        const { gen, data, $data, schema, schemaCode, it } = cxt;\n        const { opts, errSchemaPath, schemaEnv, self } = it;\n        if (!opts.validateFormats)\n            return;\n        if ($data)\n            validate$DataFormat();\n        else\n            validateFormat();\n        function validate$DataFormat() {\n            const fmts = gen.scopeValue(\"formats\", {\n                ref: self.formats,\n                code: opts.code.formats,\n            });\n            const fDef = gen.const(\"fDef\", (0, codegen_1._) `${fmts}[${schemaCode}]`);\n            const fType = gen.let(\"fType\");\n            const format = gen.let(\"format\");\n            // TODO simplify\n            gen.if((0, codegen_1._) `typeof ${fDef} == \"object\" && !(${fDef} instanceof RegExp)`, () => gen.assign(fType, (0, codegen_1._) `${fDef}.type || \"string\"`).assign(format, (0, codegen_1._) `${fDef}.validate`), () => gen.assign(fType, (0, codegen_1._) `\"string\"`).assign(format, fDef));\n            cxt.fail$data((0, codegen_1.or)(unknownFmt(), invalidFmt()));\n            function unknownFmt() {\n                if (opts.strictSchema === false)\n                    return codegen_1.nil;\n                return (0, codegen_1._) `${schemaCode} && !${format}`;\n            }\n            function invalidFmt() {\n                const callFormat = schemaEnv.$async\n                    ? (0, codegen_1._) `(${fDef}.async ? await ${format}(${data}) : ${format}(${data}))`\n                    : (0, codegen_1._) `${format}(${data})`;\n                const validData = (0, codegen_1._) `(typeof ${format} == \"function\" ? ${callFormat} : ${format}.test(${data}))`;\n                return (0, codegen_1._) `${format} && ${format} !== true && ${fType} === ${ruleType} && !${validData}`;\n            }\n        }\n        function validateFormat() {\n            const formatDef = self.formats[schema];\n            if (!formatDef) {\n                unknownFormat();\n                return;\n            }\n            if (formatDef === true)\n                return;\n            const [fmtType, format, fmtRef] = getFormat(formatDef);\n            if (fmtType === ruleType)\n                cxt.pass(validCondition());\n            function unknownFormat() {\n                if (opts.strictSchema === false) {\n                    self.logger.warn(unknownMsg());\n                    return;\n                }\n                throw new Error(unknownMsg());\n                function unknownMsg() {\n                    return `unknown format \"${schema}\" ignored in schema at path \"${errSchemaPath}\"`;\n                }\n            }\n            function getFormat(fmtDef) {\n                const code = fmtDef instanceof RegExp\n                    ? (0, codegen_1.regexpCode)(fmtDef)\n                    : opts.code.formats\n                        ? (0, codegen_1._) `${opts.code.formats}${(0, codegen_1.getProperty)(schema)}`\n                        : undefined;\n                const fmt = gen.scopeValue(\"formats\", { key: schema, ref: fmtDef, code });\n                if (typeof fmtDef == \"object\" && !(fmtDef instanceof RegExp)) {\n                    return [fmtDef.type || \"string\", fmtDef.validate, (0, codegen_1._) `${fmt}.validate`];\n                }\n                return [\"string\", fmtDef, fmt];\n            }\n            function validCondition() {\n                if (typeof formatDef == \"object\" && !(formatDef instanceof RegExp) && formatDef.async) {\n                    if (!schemaEnv.$async)\n                        throw new Error(\"async format in sync schema\");\n                    return (0, codegen_1._) `await ${fmtRef}(${data})`;\n                }\n                return typeof format == \"function\" ? (0, codegen_1._) `${fmtRef}(${data})` : (0, codegen_1._) `${fmtRef}.test(${data})`;\n            }\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=format.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.contentVocabulary = exports.metadataVocabulary = void 0;\nexports.metadataVocabulary = [\n    \"title\",\n    \"description\",\n    \"default\",\n    \"deprecated\",\n    \"readOnly\",\n    \"writeOnly\",\n    \"examples\",\n];\nexports.contentVocabulary = [\n    \"contentMediaType\",\n    \"contentEncoding\",\n    \"contentSchema\",\n];\n//# sourceMappingURL=metadata.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst core_1 = require(\"./core\");\nconst validation_1 = require(\"./validation\");\nconst applicator_1 = require(\"./applicator\");\nconst format_1 = require(\"./format\");\nconst metadata_1 = require(\"./metadata\");\nconst draft7Vocabularies = [\n    core_1.default,\n    validation_1.default,\n    (0, applicator_1.default)(),\n    format_1.default,\n    metadata_1.metadataVocabulary,\n    metadata_1.contentVocabulary,\n];\nexports.default = draft7Vocabularies;\n//# sourceMappingURL=draft7.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DiscrError = void 0;\nvar DiscrError;\n(function (DiscrError) {\n    DiscrError[\"Tag\"] = \"tag\";\n    DiscrError[\"Mapping\"] = \"mapping\";\n})(DiscrError = exports.DiscrError || (exports.DiscrError = {}));\n//# sourceMappingURL=types.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst codegen_1 = require(\"../../compile/codegen\");\nconst types_1 = require(\"../discriminator/types\");\nconst compile_1 = require(\"../../compile\");\nconst util_1 = require(\"../../compile/util\");\nconst error = {\n    message: ({ params: { discrError, tagName } }) => discrError === types_1.DiscrError.Tag\n        ? `tag \"${tagName}\" must be string`\n        : `value of tag \"${tagName}\" must be in oneOf`,\n    params: ({ params: { discrError, tag, tagName } }) => (0, codegen_1._) `{error: ${discrError}, tag: ${tagName}, tagValue: ${tag}}`,\n};\nconst def = {\n    keyword: \"discriminator\",\n    type: \"object\",\n    schemaType: \"object\",\n    error,\n    code(cxt) {\n        const { gen, data, schema, parentSchema, it } = cxt;\n        const { oneOf } = parentSchema;\n        if (!it.opts.discriminator) {\n            throw new Error(\"discriminator: requires discriminator option\");\n        }\n        const tagName = schema.propertyName;\n        if (typeof tagName != \"string\")\n            throw new Error(\"discriminator: requires propertyName\");\n        if (schema.mapping)\n            throw new Error(\"discriminator: mapping is not supported\");\n        if (!oneOf)\n            throw new Error(\"discriminator: requires oneOf keyword\");\n        const valid = gen.let(\"valid\", false);\n        const tag = gen.const(\"tag\", (0, codegen_1._) `${data}${(0, codegen_1.getProperty)(tagName)}`);\n        gen.if((0, codegen_1._) `typeof ${tag} == \"string\"`, () => validateMapping(), () => cxt.error(false, { discrError: types_1.DiscrError.Tag, tag, tagName }));\n        cxt.ok(valid);\n        function validateMapping() {\n            const mapping = getMapping();\n            gen.if(false);\n            for (const tagValue in mapping) {\n                gen.elseIf((0, codegen_1._) `${tag} === ${tagValue}`);\n                gen.assign(valid, applyTagSchema(mapping[tagValue]));\n            }\n            gen.else();\n            cxt.error(false, { discrError: types_1.DiscrError.Mapping, tag, tagName });\n            gen.endIf();\n        }\n        function applyTagSchema(schemaProp) {\n            const _valid = gen.name(\"valid\");\n            const schCxt = cxt.subschema({ keyword: \"oneOf\", schemaProp }, _valid);\n            cxt.mergeEvaluated(schCxt, codegen_1.Name);\n            return _valid;\n        }\n        function getMapping() {\n            var _a;\n            const oneOfMapping = {};\n            const topRequired = hasRequired(parentSchema);\n            let tagRequired = true;\n            for (let i = 0; i < oneOf.length; i++) {\n                let sch = oneOf[i];\n                if ((sch === null || sch === void 0 ? void 0 : sch.$ref) && !(0, util_1.schemaHasRulesButRef)(sch, it.self.RULES)) {\n                    sch = compile_1.resolveRef.call(it.self, it.schemaEnv.root, it.baseId, sch === null || sch === void 0 ? void 0 : sch.$ref);\n                    if (sch instanceof compile_1.SchemaEnv)\n                        sch = sch.schema;\n                }\n                const propSch = (_a = sch === null || sch === void 0 ? void 0 : sch.properties) === null || _a === void 0 ? void 0 : _a[tagName];\n                if (typeof propSch != \"object\") {\n                    throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have \"properties/${tagName}\"`);\n                }\n                tagRequired = tagRequired && (topRequired || hasRequired(sch));\n                addMappings(propSch, i);\n            }\n            if (!tagRequired)\n                throw new Error(`discriminator: \"${tagName}\" must be required`);\n            return oneOfMapping;\n            function hasRequired({ required }) {\n                return Array.isArray(required) && required.includes(tagName);\n            }\n            function addMappings(sch, i) {\n                if (sch.const) {\n                    addMapping(sch.const, i);\n                }\n                else if (sch.enum) {\n                    for (const tagValue of sch.enum) {\n                        addMapping(tagValue, i);\n                    }\n                }\n                else {\n                    throw new Error(`discriminator: \"properties/${tagName}\" must have \"const\" or \"enum\"`);\n                }\n            }\n            function addMapping(tagValue, i) {\n                if (typeof tagValue != \"string\" || tagValue in oneOfMapping) {\n                    throw new Error(`discriminator: \"${tagName}\" values must be unique strings`);\n                }\n                oneOfMapping[tagValue] = i;\n            }\n        }\n    },\n};\nexports.default = def;\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CodeGen = exports.Name = exports.nil = exports.stringify = exports.str = exports._ = exports.KeywordCxt = void 0;\nconst core_1 = require(\"./core\");\nconst draft7_1 = require(\"./vocabularies/draft7\");\nconst discriminator_1 = require(\"./vocabularies/discriminator\");\nconst draft7MetaSchema = require(\"./refs/json-schema-draft-07.json\");\nconst META_SUPPORT_DATA = [\"/properties\"];\nconst META_SCHEMA_ID = \"http://json-schema.org/draft-07/schema\";\nclass Ajv extends core_1.default {\n    _addVocabularies() {\n        super._addVocabularies();\n        draft7_1.default.forEach((v) => this.addVocabulary(v));\n        if (this.opts.discriminator)\n            this.addKeyword(discriminator_1.default);\n    }\n    _addDefaultMetaSchema() {\n        super._addDefaultMetaSchema();\n        if (!this.opts.meta)\n            return;\n        const metaSchema = this.opts.$data\n            ? this.$dataMetaSchema(draft7MetaSchema, META_SUPPORT_DATA)\n            : draft7MetaSchema;\n        this.addMetaSchema(metaSchema, META_SCHEMA_ID, false);\n        this.refs[\"http://json-schema.org/schema\"] = META_SCHEMA_ID;\n    }\n    defaultMeta() {\n        return (this.opts.defaultMeta =\n            super.defaultMeta() || (this.getSchema(META_SCHEMA_ID) ? META_SCHEMA_ID : undefined));\n    }\n}\nmodule.exports = exports = Ajv;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.default = Ajv;\nvar validate_1 = require(\"./compile/validate\");\nObject.defineProperty(exports, \"KeywordCxt\", { enumerable: true, get: function () { return validate_1.KeywordCxt; } });\nvar codegen_1 = require(\"./compile/codegen\");\nObject.defineProperty(exports, \"_\", { enumerable: true, get: function () { return codegen_1._; } });\nObject.defineProperty(exports, \"str\", { enumerable: true, get: function () { return codegen_1.str; } });\nObject.defineProperty(exports, \"stringify\", { enumerable: true, get: function () { return codegen_1.stringify; } });\nObject.defineProperty(exports, \"nil\", { enumerable: true, get: function () { return codegen_1.nil; } });\nObject.defineProperty(exports, \"Name\", { enumerable: true, get: function () { return codegen_1.Name; } });\nObject.defineProperty(exports, \"CodeGen\", { enumerable: true, get: function () { return codegen_1.CodeGen; } });\n//# sourceMappingURL=ajv.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst ajv_1 = require(\"ajv\");\nconst codegen_1 = require(\"ajv/dist/compile/codegen\");\nconst code_1 = require(\"ajv/dist/compile/codegen/code\");\nconst validate_1 = require(\"ajv/dist/compile/validate\");\nconst errors_1 = require(\"ajv/dist/compile/errors\");\nconst names_1 = require(\"ajv/dist/compile/names\");\nconst keyword = \"errorMessage\";\nconst used = new ajv_1.Name(\"emUsed\");\nconst KEYWORD_PROPERTY_PARAMS = {\n    required: \"missingProperty\",\n    dependencies: \"property\",\n    dependentRequired: \"property\",\n};\nconst INTERPOLATION = /\\$\\{[^}]+\\}/;\nconst INTERPOLATION_REPLACE = /\\$\\{([^}]+)\\}/g;\nconst EMPTY_STR = /^\"\"\\s*\\+\\s*|\\s*\\+\\s*\"\"$/g;\nfunction errorMessage(options) {\n    return {\n        keyword,\n        schemaType: [\"string\", \"object\"],\n        post: true,\n        code(cxt) {\n            const { gen, data, schema, schemaValue, it } = cxt;\n            if (it.createErrors === false)\n                return;\n            const sch = schema;\n            const instancePath = codegen_1.strConcat(names_1.default.instancePath, it.errorPath);\n            gen.if(ajv_1._ `${names_1.default.errors} > 0`, () => {\n                if (typeof sch == \"object\") {\n                    const [kwdPropErrors, kwdErrors] = keywordErrorsConfig(sch);\n                    if (kwdErrors)\n                        processKeywordErrors(kwdErrors);\n                    if (kwdPropErrors)\n                        processKeywordPropErrors(kwdPropErrors);\n                    processChildErrors(childErrorsConfig(sch));\n                }\n                const schMessage = typeof sch == \"string\" ? sch : sch._;\n                if (schMessage)\n                    processAllErrors(schMessage);\n                if (!options.keepErrors)\n                    removeUsedErrors();\n            });\n            function childErrorsConfig({ properties, items }) {\n                const errors = {};\n                if (properties) {\n                    errors.props = {};\n                    for (const p in properties)\n                        errors.props[p] = [];\n                }\n                if (items) {\n                    errors.items = {};\n                    for (let i = 0; i < items.length; i++)\n                        errors.items[i] = [];\n                }\n                return errors;\n            }\n            function keywordErrorsConfig(emSchema) {\n                let propErrors;\n                let errors;\n                for (const k in emSchema) {\n                    if (k === \"properties\" || k === \"items\")\n                        continue;\n                    const kwdSch = emSchema[k];\n                    if (typeof kwdSch == \"object\") {\n                        propErrors || (propErrors = {});\n                        const errMap = (propErrors[k] = {});\n                        for (const p in kwdSch)\n                            errMap[p] = [];\n                    }\n                    else {\n                        errors || (errors = {});\n                        errors[k] = [];\n                    }\n                }\n                return [propErrors, errors];\n            }\n            function processKeywordErrors(kwdErrors) {\n                const kwdErrs = gen.const(\"emErrors\", ajv_1.stringify(kwdErrors));\n                const templates = gen.const(\"templates\", getTemplatesCode(kwdErrors, schema));\n                gen.forOf(\"err\", names_1.default.vErrors, (err) => gen.if(matchKeywordError(err, kwdErrs), () => gen.code(ajv_1._ `${kwdErrs}[${err}.keyword].push(${err})`).assign(ajv_1._ `${err}.${used}`, true)));\n                const { singleError } = options;\n                if (singleError) {\n                    const message = gen.let(\"message\", ajv_1._ `\"\"`);\n                    const paramsErrors = gen.let(\"paramsErrors\", ajv_1._ `[]`);\n                    loopErrors((key) => {\n                        gen.if(message, () => gen.code(ajv_1._ `${message} += ${typeof singleError == \"string\" ? singleError : \";\"}`));\n                        gen.code(ajv_1._ `${message} += ${errMessage(key)}`);\n                        gen.assign(paramsErrors, ajv_1._ `${paramsErrors}.concat(${kwdErrs}[${key}])`);\n                    });\n                    errors_1.reportError(cxt, { message, params: ajv_1._ `{errors: ${paramsErrors}}` });\n                }\n                else {\n                    loopErrors((key) => errors_1.reportError(cxt, {\n                        message: errMessage(key),\n                        params: ajv_1._ `{errors: ${kwdErrs}[${key}]}`,\n                    }));\n                }\n                function loopErrors(body) {\n                    gen.forIn(\"key\", kwdErrs, (key) => gen.if(ajv_1._ `${kwdErrs}[${key}].length`, () => body(key)));\n                }\n                function errMessage(key) {\n                    return ajv_1._ `${key} in ${templates} ? ${templates}[${key}]() : ${schemaValue}[${key}]`;\n                }\n            }\n            function processKeywordPropErrors(kwdPropErrors) {\n                const kwdErrs = gen.const(\"emErrors\", ajv_1.stringify(kwdPropErrors));\n                const templatesCode = [];\n                for (const k in kwdPropErrors) {\n                    templatesCode.push([\n                        k,\n                        getTemplatesCode(kwdPropErrors[k], schema[k]),\n                    ]);\n                }\n                const templates = gen.const(\"templates\", gen.object(...templatesCode));\n                const kwdPropParams = gen.scopeValue(\"obj\", {\n                    ref: KEYWORD_PROPERTY_PARAMS,\n                    code: ajv_1.stringify(KEYWORD_PROPERTY_PARAMS),\n                });\n                const propParam = gen.let(\"emPropParams\");\n                const paramsErrors = gen.let(\"emParamsErrors\");\n                gen.forOf(\"err\", names_1.default.vErrors, (err) => gen.if(matchKeywordError(err, kwdErrs), () => {\n                    gen.assign(propParam, ajv_1._ `${kwdPropParams}[${err}.keyword]`);\n                    gen.assign(paramsErrors, ajv_1._ `${kwdErrs}[${err}.keyword][${err}.params[${propParam}]]`);\n                    gen.if(paramsErrors, () => gen.code(ajv_1._ `${paramsErrors}.push(${err})`).assign(ajv_1._ `${err}.${used}`, true));\n                }));\n                gen.forIn(\"key\", kwdErrs, (key) => gen.forIn(\"keyProp\", ajv_1._ `${kwdErrs}[${key}]`, (keyProp) => {\n                    gen.assign(paramsErrors, ajv_1._ `${kwdErrs}[${key}][${keyProp}]`);\n                    gen.if(ajv_1._ `${paramsErrors}.length`, () => {\n                        const tmpl = gen.const(\"tmpl\", ajv_1._ `${templates}[${key}] && ${templates}[${key}][${keyProp}]`);\n                        errors_1.reportError(cxt, {\n                            message: ajv_1._ `${tmpl} ? ${tmpl}() : ${schemaValue}[${key}][${keyProp}]`,\n                            params: ajv_1._ `{errors: ${paramsErrors}}`,\n                        });\n                    });\n                }));\n            }\n            function processChildErrors(childErrors) {\n                const { props, items } = childErrors;\n                if (!props && !items)\n                    return;\n                const isObj = ajv_1._ `typeof ${data} == \"object\"`;\n                const isArr = ajv_1._ `Array.isArray(${data})`;\n                const childErrs = gen.let(\"emErrors\");\n                let childKwd;\n                let childProp;\n                const templates = gen.let(\"templates\");\n                if (props && items) {\n                    childKwd = gen.let(\"emChildKwd\");\n                    gen.if(isObj);\n                    gen.if(isArr, () => {\n                        init(items, schema.items);\n                        gen.assign(childKwd, ajv_1.str `items`);\n                    }, () => {\n                        init(props, schema.properties);\n                        gen.assign(childKwd, ajv_1.str `properties`);\n                    });\n                    childProp = ajv_1._ `[${childKwd}]`;\n                }\n                else if (items) {\n                    gen.if(isArr);\n                    init(items, schema.items);\n                    childProp = ajv_1._ `.items`;\n                }\n                else if (props) {\n                    gen.if(codegen_1.and(isObj, codegen_1.not(isArr)));\n                    init(props, schema.properties);\n                    childProp = ajv_1._ `.properties`;\n                }\n                gen.forOf(\"err\", names_1.default.vErrors, (err) => ifMatchesChildError(err, childErrs, (child) => gen.code(ajv_1._ `${childErrs}[${child}].push(${err})`).assign(ajv_1._ `${err}.${used}`, true)));\n                gen.forIn(\"key\", childErrs, (key) => gen.if(ajv_1._ `${childErrs}[${key}].length`, () => {\n                    errors_1.reportError(cxt, {\n                        message: ajv_1._ `${key} in ${templates} ? ${templates}[${key}]() : ${schemaValue}${childProp}[${key}]`,\n                        params: ajv_1._ `{errors: ${childErrs}[${key}]}`,\n                    });\n                    gen.assign(ajv_1._ `${names_1.default.vErrors}[${names_1.default.errors}-1].instancePath`, ajv_1._ `${instancePath} + \"/\" + ${key}.replace(/~/g, \"~0\").replace(/\\\\//g, \"~1\")`);\n                }));\n                gen.endIf();\n                function init(children, msgs) {\n                    gen.assign(childErrs, ajv_1.stringify(children));\n                    gen.assign(templates, getTemplatesCode(children, msgs));\n                }\n            }\n            function processAllErrors(schMessage) {\n                const errs = gen.const(\"emErrs\", ajv_1._ `[]`);\n                gen.forOf(\"err\", names_1.default.vErrors, (err) => gen.if(matchAnyError(err), () => gen.code(ajv_1._ `${errs}.push(${err})`).assign(ajv_1._ `${err}.${used}`, true)));\n                gen.if(ajv_1._ `${errs}.length`, () => errors_1.reportError(cxt, {\n                    message: templateExpr(schMessage),\n                    params: ajv_1._ `{errors: ${errs}}`,\n                }));\n            }\n            function removeUsedErrors() {\n                const errs = gen.const(\"emErrs\", ajv_1._ `[]`);\n                gen.forOf(\"err\", names_1.default.vErrors, (err) => gen.if(ajv_1._ `!${err}.${used}`, () => gen.code(ajv_1._ `${errs}.push(${err})`)));\n                gen.assign(names_1.default.vErrors, errs).assign(names_1.default.errors, ajv_1._ `${errs}.length`);\n            }\n            function matchKeywordError(err, kwdErrs) {\n                return codegen_1.and(ajv_1._ `${err}.keyword !== ${keyword}`, ajv_1._ `!${err}.${used}`, ajv_1._ `${err}.instancePath === ${instancePath}`, ajv_1._ `${err}.keyword in ${kwdErrs}`, \n                // TODO match the end of the string?\n                ajv_1._ `${err}.schemaPath.indexOf(${it.errSchemaPath}) === 0`, ajv_1._ `/^\\\\/[^\\\\/]*$/.test(${err}.schemaPath.slice(${it.errSchemaPath.length}))`);\n            }\n            function ifMatchesChildError(err, childErrs, thenBody) {\n                gen.if(codegen_1.and(ajv_1._ `${err}.keyword !== ${keyword}`, ajv_1._ `!${err}.${used}`, ajv_1._ `${err}.instancePath.indexOf(${instancePath}) === 0`), () => {\n                    const childRegex = gen.scopeValue(\"pattern\", {\n                        ref: /^\\/([^/]*)(?:\\/|$)/,\n                        code: ajv_1._ `new RegExp(\"^\\\\\\/([^/]*)(?:\\\\\\/|$)\")`,\n                    });\n                    const matches = gen.const(\"emMatches\", ajv_1._ `${childRegex}.exec(${err}.instancePath.slice(${instancePath}.length))`);\n                    const child = gen.const(\"emChild\", ajv_1._ `${matches} && ${matches}[1].replace(/~1/g, \"/\").replace(/~0/g, \"~\")`);\n                    gen.if(ajv_1._ `${child} !== undefined && ${child} in ${childErrs}`, () => thenBody(child));\n                });\n            }\n            function matchAnyError(err) {\n                return codegen_1.and(ajv_1._ `${err}.keyword !== ${keyword}`, ajv_1._ `!${err}.${used}`, codegen_1.or(ajv_1._ `${err}.instancePath === ${instancePath}`, codegen_1.and(ajv_1._ `${err}.instancePath.indexOf(${instancePath}) === 0`, ajv_1._ `${err}.instancePath[${instancePath}.length] === \"/\"`)), ajv_1._ `${err}.schemaPath.indexOf(${it.errSchemaPath}) === 0`, ajv_1._ `${err}.schemaPath[${it.errSchemaPath}.length] === \"/\"`);\n            }\n            function getTemplatesCode(keys, msgs) {\n                const templatesCode = [];\n                for (const k in keys) {\n                    const msg = msgs[k];\n                    if (INTERPOLATION.test(msg))\n                        templatesCode.push([k, templateFunc(msg)]);\n                }\n                return gen.object(...templatesCode);\n            }\n            function templateExpr(msg) {\n                if (!INTERPOLATION.test(msg))\n                    return ajv_1.stringify(msg);\n                return new code_1._Code(code_1.safeStringify(msg)\n                    .replace(INTERPOLATION_REPLACE, (_s, ptr) => `\" + JSON.stringify(${validate_1.getData(ptr, it)}) + \"`)\n                    .replace(EMPTY_STR, \"\"));\n            }\n            function templateFunc(msg) {\n                return ajv_1._ `function(){return ${templateExpr(msg)}}`;\n            }\n        },\n        metaSchema: {\n            anyOf: [\n                { type: \"string\" },\n                {\n                    type: \"object\",\n                    properties: {\n                        properties: { $ref: \"#/$defs/stringMap\" },\n                        items: { $ref: \"#/$defs/stringList\" },\n                        required: { $ref: \"#/$defs/stringOrMap\" },\n                        dependencies: { $ref: \"#/$defs/stringOrMap\" },\n                    },\n                    additionalProperties: { type: \"string\" },\n                },\n            ],\n            $defs: {\n                stringMap: {\n                    type: \"object\",\n                    additionalProperties: { type: \"string\" },\n                },\n                stringOrMap: {\n                    anyOf: [{ type: \"string\" }, { $ref: \"#/$defs/stringMap\" }],\n                },\n                stringList: { type: \"array\", items: { type: \"string\" } },\n            },\n        },\n    };\n}\nconst ajvErrors = (ajv, options = {}) => {\n    if (!ajv.opts.allErrors)\n        throw new Error(\"ajv-errors: Ajv option allErrors must be true\");\n    if (ajv.opts.jsPropertySyntax) {\n        throw new Error(\"ajv-errors: ajv option jsPropertySyntax is not supported\");\n    }\n    return ajv.addKeyword(errorMessage(options));\n};\nexports.default = ajvErrors;\nmodule.exports = ajvErrors;\nmodule.exports.default = ajvErrors;\n//# sourceMappingURL=index.js.map", "import { toNestError, validateFieldsNatively } from '@hookform/resolvers';\nimport Ajv, { DefinedError } from 'ajv';\nimport ajvErrors from 'ajv-errors';\nimport { appendErrors, FieldError } from 'react-hook-form';\nimport { Resolver } from './types';\n\nconst parseErrorSchema = (\n  ajvErrors: DefinedError[],\n  validateAllFieldCriteria: boolean,\n) => {\n  // Ajv will return empty instancePath when require error\n  ajvErrors.forEach((error) => {\n    if (error.keyword === 'required') {\n      error.instancePath += '/' + error.params.missingProperty;\n    }\n  });\n\n  return ajvErrors.reduce<Record<string, FieldError>>((previous, error) => {\n    // `/deepObject/data` -> `deepObject.data`\n    const path = error.instancePath.substring(1).replace(/\\//g, '.');\n\n    if (!previous[path]) {\n      previous[path] = {\n        message: error.message,\n        type: error.keyword,\n      };\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = previous[path].types;\n      const messages = types && types[error.keyword];\n\n      previous[path] = appendErrors(\n        path,\n        validateAllFieldCriteria,\n        previous,\n        error.keyword,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message || '')\n          : error.message,\n      ) as FieldError;\n    }\n\n    return previous;\n  }, {});\n};\n\nexport const ajvResolver: Resolver =\n  (schema, schemaOptions, resolverOptions = {}) =>\n  async (values, _, options) => {\n    const ajv = new Ajv({\n      allErrors: true,\n      validateSchema: true,\n      ...schemaOptions,\n    });\n\n    ajvErrors(ajv);\n\n    const validate = ajv.compile(\n      Object.assign({ $async: resolverOptions?.mode === 'async' }, schema),\n    );\n    const valid = validate(values);\n\n    if (!valid) {\n      return {\n        values: {},\n        errors: toNestError(\n          parseErrorSchema(\n            validate.errors as DefinedError[],\n            !options.shouldUseNativeValidation &&\n              options.criteriaMode === 'all',\n          ),\n          options,\n        ),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values,\n      errors: {},\n    };\n  };\n"], "names": ["Object", "defineProperty", "value", "exports", "_CodeOrName", "Name", "constructor", "s", "super", "IDENTIFIER", "test", "Error", "this", "str", "toString", "emptyStr", "names", "_Code", "code", "_items", "length", "item", "_a", "_str", "reduce", "c", "_names", "_", "strs", "args", "i", "addCodeArg", "push", "plus", "expr", "safeStringify", "res", "mergeExprItems", "undefined", "splice", "optimize", "arg", "x", "Array", "isArray", "join", "a", "b", "slice", "JSON", "stringify", "replace", "c1", "c2", "key", "rx", "ValueError", "name", "UsedValueState", "const", "code_1", "let", "var", "<PERSON><PERSON>", "prefixes", "parent", "_prefixes", "_parent", "to<PERSON>ame", "nameOrPrefix", "prefix", "_newName", "_nameGroup", "index", "_b", "has", "ValueScopeName", "nameStr", "setValue", "property", "itemIndex", "scopePath", "line", "opts", "_values", "_scope", "scope", "_n", "lines", "nil", "get", "ref", "valueKey", "vs", "_name", "Map", "set", "getValue", "keyOrRef", "scopeRefs", "scopeName", "values", "_reduceValues", "scopeCode", "usedValues", "getCode", "valueCode", "nameSet", "for<PERSON>ach", "Started", "es5", "var<PERSON><PERSON><PERSON>", "Completed", "code_2", "enumerable", "strConcat", "getProperty", "regexpCode", "scope_2", "scope_1", "ValueScope", "GT", "GTE", "LT", "LTE", "EQ", "NEQ", "NOT", "OR", "AND", "ADD", "Node", "optimizeNodes", "optimizeNames", "_constants", "Def", "<PERSON><PERSON><PERSON>", "rhs", "render", "constants", "optimizeExpr", "Assign", "lhs", "sideEffects", "addExprNames", "AssignOp", "op", "Label", "label", "Break", "<PERSON>hrow", "error", "AnyCode", "ParentNode", "nodes", "n", "subtractNames", "addNames", "BlockNode", "Root", "Else", "kind", "If", "condition", "else", "cond", "e", "ns", "not", "For", "ForLoop", "iteration", "ForRange", "from", "to", "ForIter", "loop", "iterable", "Func", "async", "Return", "Try", "catch", "finally", "Catch", "Finally", "replaceName", "some", "items", "par", "extScope", "_blockStarts", "_extScope", "_nodes", "_root", "scopeValue", "prefixOrName", "Set", "add", "getScopeValue", "_def", "constant", "_leafNode", "_constant", "assign", "operators", "object", "keyV<PERSON><PERSON>", "if", "then<PERSON>ody", "elseBody", "_blockNode", "endIf", "elseIf", "_elseNode", "_endBlockNode", "_for", "node", "forBody", "endFor", "for", "forRange", "forOf", "arr", "forIn", "obj", "ownProperties", "break", "return", "try", "tryBody", "catchCode", "finallyCode", "_currNode", "throw", "block", "body", "nodeCount", "endBlock", "len", "pop", "toClose", "func", "funcBody", "endFunc", "N1", "N2", "andCode", "mappend", "orCode", "y", "checkUnknownRules", "it", "schema", "self", "strictSchema", "rules", "RULES", "keywords", "checkStrictMode", "schemaHasRules", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unescape<PERSON>son<PERSON>ointer", "makeMergeEvaluated", "mergeNames", "mergeToName", "mergeValues", "resultToName", "gen", "codegen_1", "evaluatedPropsToName", "ps", "props", "setEvaluated", "keys", "p", "hash", "all", "topSchemaRef", "schemaPath", "keyword", "$data", "decodeURIComponent", "encodeURIComponent", "xs", "f", "Math", "max", "snippets", "Type", "msg", "mode", "logger", "warn", "dataProp", "dataPropType", "jsPropertySyntax", "isNumber", "<PERSON><PERSON>", "data", "valCxt", "instancePath", "parentData", "parentDataProperty", "rootData", "dynamicAnchors", "vErrors", "errors", "json", "jsonPos", "jsonLen", "jsonPart", "addError", "err<PERSON><PERSON><PERSON>", "err", "names_1", "default", "returnErrors", "errs", "validateName", "schemaEnv", "$async", "ValidationError", "message", "schemaType", "cxt", "keywordError", "errorPaths", "overrideAllErrors", "compositeRule", "allErrors", "errorObjectCode", "errsCount", "schemaValue", "errorPath", "errSchemaPath", "verbose", "E", "params", "propertyName", "parentSchema", "createErrors", "errorInstancePath", "errorSchemaPath", "messages", "extraErrorProps", "errorObject", "instPath", "util_1", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "Str", "sch<PERSON><PERSON>", "boolError", "falseSchemaError", "errors_1", "reportError", "schemaCode", "valid", "jsonTypes", "groups", "number", "type", "string", "array", "types", "integer", "boolean", "null", "post", "shouldUseGroup", "group", "rule", "shouldUseRule", "definition", "implements", "kwd", "DataType", "getJSONTypes", "ts", "every", "rules_1", "isJSONType", "includes", "nullable", "coerceTo", "coerceTypes", "filter", "t", "COERCIBLE", "coerceToTypes", "checkTypes", "applicability_1", "schemaHasRulesForType", "wrongType", "checkDataTypes", "strictNumbers", "Wrong", "dataType", "coerced", "coerceSpecificType", "reportTypeError", "assignParentData", "coerceData", "checkDataType", "strictNums", "correct", "Correct", "numCond", "_cond", "and", "dataTypes", "toHash", "notObj", "typeError", "schemaRefOrVal", "getTypeErrorContext", "assignDefault", "prop", "defaultValue", "childData", "useDefaults", "ty", "properties", "sch", "util_2", "hasPropFunc", "prototype", "hasOwnProperty", "isOwnProperty", "noPropertyInData", "or", "allSchemaProperties", "schemaMap", "setParams", "missingProperty", "missing", "map", "alwaysValidSchema", "context", "passSchema", "dataAndSchema", "dynamicRef", "newRegExp", "pattern", "u", "unicodeRegExp", "regExp", "useFunc", "validArr", "validateItems", "not<PERSON><PERSON><PERSON>", "subschema", "unevaluated", "sch<PERSON><PERSON><PERSON>", "_sch", "schCxt", "schemaProp", "mergeValidEvaluated", "result", "reset", "modifyData", "useKeyword", "def", "macroSchema", "macro", "call", "schemaRef", "validateSchema", "pass", "checkAsyncKeyword", "validate", "compile", "validateRef", "assignValid", "_await", "callValidateCode", "passContext", "modifying", "reportErrs", "block$data", "ruleErrs", "validateAsync", "validateErrs", "validateSync", "extendErrors", "addErrs", "ok", "allowUndefined", "st", "deps", "dependencies", "errorsText", "escapeFragment", "dpType", "dataPathArr", "dataContextProps", "_nextData", "dataLevel", "definedProperties", "dataNames", "jtdDiscriminator", "jtdMetadata", "equal", "RegExp", "source", "flags", "valueOf", "traverse", "module", "cb", "_traverse", "pre", "jsonPtr", "rootSchema", "parentJsonPtr", "parentKeyword", "keyIndex", "arrayKeywords", "props<PERSON><PERSON><PERSON>", "allKeys", "skipKeywords", "additionalItems", "contains", "additionalProperties", "propertyNames", "then", "allOf", "anyOf", "oneOf", "$defs", "definitions", "patternProperties", "enum", "required", "maximum", "minimum", "exclusiveMaximum", "exclusiveMinimum", "multipleOf", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "format", "maxItems", "minItems", "uniqueItems", "maxProperties", "minProperties", "SIMPLE_INLINED", "limit", "hasRef", "count<PERSON>eys", "REF_KEYWORDS", "count", "Infinity", "eachItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resolver", "id", "normalize", "normalizeId", "parse", "_get<PERSON><PERSON><PERSON><PERSON>", "serialize", "split", "TRAILING_SLASH_HASH", "baseId", "resolve", "ANCHOR", "schemaId", "uriResolver", "schId", "baseIds", "pathPrefix", "localRefs", "schemaRefs", "fullPath", "addRef", "_resolve", "ambiguos", "schOrRef", "refs", "checkAmbiguosRef", "addAnchor", "anchor", "$anchor", "$dynamicAnchor", "sch1", "sch2", "dataType_2", "dataType_1", "validateFunction", "funcSourceUrl", "destructureValCxtES5", "destructureValCxt", "process", "schemaCxtHasRules", "isSchemaObj", "checkKeywords", "$ref", "ignoreKeywordsWithRef", "schemaHasRulesButRef", "checkRefsAndKeywords", "typeAndKeywords", "jtd", "schemaKeywords", "getSchemaTypes", "coerceAndCheckDataType", "commentKeyword", "$comment", "rootName", "root", "typeErrors", "groupKeywords", "iterateKeywords", "meta", "strictTypes", "includesType", "strictTypesError", "checkContextTypes", "allowUnionTypes", "checkMultipleTypes", "hasApplicableType", "schTs", "kwdT", "checkKeywordTypes", "checkStrictTypes", "keywordCode", "defaults_1", "assignDefaults", "checkNoDefault", "evaluated", "resetEvaluated", "assignEvaluated", "returnResults", "topSchemaObjCode", "boolSchema_1", "topBoolOrEmptySchema", "KeywordCxt", "keyword_1", "validateKeywordUsage", "getData", "validSchemaType", "trackErrors", "successAction", "failAction", "failResult", "fail", "fail$data", "invalid$data", "append", "errorParams", "_error", "reportExtraError", "$dataError", "keyword$DataError", "resetErrorsCount", "codeBlock", "$dataValid", "check$data", "wrong$DataType", "validateSchemaRef", "invalid$DataSchema", "appl", "subschema_1", "getSubschema", "extendSubschemaData", "extendSubschemaMode", "nextContext", "resolve_1", "resolveUrl", "updateContext", "checkAsyncSchema", "subSchemaObjCode", "boolOrEmptySchema", "subschemaCode", "mergeEvaluated", "schemaCxt", "ruleType", "funcKeywordCode", "macroKeywordCode", "JSON_POINTER", "RELATIVE_JSON_POINTER", "<PERSON>son<PERSON>oint<PERSON>", "matches", "exec", "up", "errorMsg", "segments", "segment", "pointerType", "ajv", "validation", "Missing<PERSON>ef<PERSON><PERSON><PERSON>", "missingRef", "missingSchema", "SchemaEnv", "env", "compileSchema", "getCompilingSchema", "rootId", "CodeGen", "_ValidationError", "validation_error_1", "sourceCode", "_compilations", "validate_1", "validateFunctionCode", "validateCode", "Function", "makeValidate", "scopeValues", "dynamicProps", "dynamicItems", "delete", "inlineOrCompile", "inlineRef", "inlineRefs", "schEnv", "s1", "s2", "schemas", "resolveSchema", "refPath", "get<PERSON>sonPointer", "schOrFunc", "PREVENT_SCOPE_CHANGE", "parsedRef", "fragment", "part", "partSchema", "unescapeFragment", "merge", "_len", "arguments", "sets", "_key", "xl", "subexp", "typeOf", "o", "shift", "toLowerCase", "toUpperCase", "buildExps", "isIRI", "ALPHA$$", "DIGIT$$", "HEXDIG$$", "PCT_ENCODED$", "SUB_DELIMS$$", "RESERVED$$", "IPRIVATE$$", "UNRESERVED$$", "DEC_OCTET_RELAXED$", "IPV4ADDRESS$", "H16$", "LS32$", "IPV6ADDRESS1$", "IPV6ADDRESS2$", "IPV6ADDRESS3$", "IPV6ADDRESS4$", "IPV6ADDRESS5$", "IPV6ADDRESS6$", "IPV6ADDRESS7$", "IPV6ADDRESS8$", "IPV6ADDRESS9$", "IPV6ADDRESS$", "ZONEID$", "PCHAR$", "NOT_SCHEME", "NOT_USERINFO", "NOT_HOST", "NOT_PATH", "NOT_PATH_NOSCHEME", "NOT_QUERY", "NOT_FRAGMENT", "ESCAPE", "UNRESERVED", "OTHER_CHARS", "PCT_ENCODED", "IPV4ADDRESS", "IPV6ADDRESS", "URI_PROTOCOL", "IRI_PROTOCOL", "slicedToArray", "Symbol", "iterator", "_arr", "_d", "_e", "_s", "_i", "next", "done", "sliceIterator", "TypeError", "maxInt", "base", "regexPunycode", "regexNonASCII", "regexSeparators", "overflow", "floor", "stringFromCharCode", "String", "fromCharCode", "error$1", "RangeError", "mapDomain", "fn", "parts", "digitToBasic", "digit", "flag", "adapt", "delta", "numPoints", "firstTime", "k", "baseMinusTMin", "punycode", "input", "output", "inputLength", "counter", "charCodeAt", "extra", "ucs2decode", "bias", "_iteratorNormalCompletion", "_didIteratorError", "_iteratorError", "_step", "_iterator", "_currentValue2", "basicLength", "handledCPCount", "m", "_iteratorNormalCompletion2", "_didIteratorError2", "_iteratorError2", "_step2", "_iterator2", "currentValue", "handledCPCountPlusOne", "_iteratorNormalCompletion3", "_didIteratorError3", "_iteratorError3", "_step3", "_iterator3", "_currentValue", "q", "qMinusT", "baseMinusT", "encode", "codePoint", "basic", "lastIndexOf", "j", "oldi", "w", "out", "fromCodePoint", "apply", "decode", "SCHEMES", "pctEncChar", "chr", "pctDecChars", "newStr", "il", "parseInt", "substr", "_c", "c3", "_normalizeComponentEncoding", "components", "protocol", "decodeUnreserved", "decStr", "match", "scheme", "userinfo", "host", "path", "query", "_stripLeadingZeros", "_normalizeIPv4", "address", "_normalizeIPv6", "_matches2", "zone", "_address$toLowerCase$", "reverse", "_address$toLowerCase$2", "last", "first", "firstFields", "lastFields", "isLastFieldIPv4Address", "fieldCount", "lastFieldsStart", "fields", "longestZeroFields", "acc", "field", "lastLongest", "sort", "newHost", "newFirst", "newLast", "URI_PARSE", "NO_MATCH_IS_UNDEFINED", "uriString", "options", "iri", "reference", "port", "isNaN", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "unicodeSupport", "domainHost", "_recomposeAuthority", "uri<PERSON><PERSON>s", "$1", "$2", "RDS1", "RDS2", "RDS3", "RDS5", "removeDotSegments", "im", "authority", "char<PERSON>t", "absolutePath", "resolveComponents", "relative", "target", "tolerant", "unescapeComponent", "handler", "secure", "handler$1", "isSecure", "wsComponents", "handler$2", "resourceName", "_wsComponents$resourc", "_wsComponents$resourc2", "handler$3", "O", "VCHAR$$", "NOT_LOCAL_PART", "NOT_HFNAME", "NOT_HFVALUE", "handler$4", "mailtoComponents", "unknownHeaders", "headers", "hfields", "hfield", "toAddrs", "_x", "_xl", "subject", "_x2", "_xl2", "addr", "setInterval", "toAddr", "atIdx", "localPart", "domain", "URN_PARSE", "handler$5", "urnComponents", "nid", "nss", "uriComponents", "UUID", "handler$6", "uuidComponents", "uuid", "baseURI", "relativeURI", "schemelessOptions", "uri", "uriA", "uriB", "escapeComponent", "factory", "codegen_2", "defaultRegExp", "META_IGNORE_OPTIONS", "EXT_SCOPE_NAMES", "removedOptions", "errorDataPath", "jsonPointers", "extendRefs", "missingRefs", "processCode", "strictDefaults", "strictKeywords", "unknownFormats", "cache", "ajvErrors", "deprecatedOptions", "unicode", "requiredOptions", "_f", "_g", "_h", "_j", "_k", "_l", "_m", "_o", "_p", "_q", "_r", "_t", "_u", "_v", "_w", "_y", "_z", "_0", "strict", "_optz", "uri_1", "strictTuples", "strictRequired", "loopRequired", "loopEnum", "addUsedSchema", "validateFormats", "int32range", "Ajv", "formats", "_loading", "_cache", "noLogs", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "formatOpt", "getRules", "checkOptions", "_metaOpts", "getMetaSchemaOptions", "addInitialFormats", "_addVocabularies", "_addDefaultMetaSchema", "addInitialKeywords", "addMetaSchema", "addInitialSchemas", "addKeyword", "_dataRefSchema", "$dataRefSchema", "$id", "defaultMeta", "schemaKeyRef", "v", "getSchema", "_meta", "_addSchema", "_compileSchemaEnv", "compileAsync", "loadSchema", "runCompileAsync", "_schema", "loadMetaSchema", "$schema", "_compileAsync", "ref_error_1", "checkLoaded", "loadMissingSchema", "_loadSchema", "addSchema", "_validateSchema", "_checkUnique", "throwOrLogError", "keyRef", "getSchEnv", "compile_1", "removeSchema", "_removeAllSchemas", "clear", "addVocabulary", "kwdOrDef", "checkKeyword", "addRule", "keywordMetaschema", "getKeyword", "removeKeyword", "findIndex", "addFormat", "separator", "dataVar", "text", "$dataMetaSchema", "metaSchema", "keywordsJsonPointers", "seg", "schemaOrData", "regex", "getSchemaRefs", "startsWith", "_compileMetaSchema", "currentOpts", "checkOpts", "opt", "optsSchemas", "defs", "metaOpts", "KEYWORD_NAME", "ruleGroup", "find", "before", "addBeforeRule", "_rule", "$dataRef", "callRef", "callRootRef", "schOrEnv", "resolveRef", "getValidate", "callValidate", "schName", "inlineRefSchema", "passCxt", "addErrorsFrom", "addEvaluatedFrom", "schEvaluated", "callAsyncRef", "id_1", "ref_1", "ops", "KWDs", "okStr", "prec", "multipleOfPrecision", "invalid", "ucs2length", "pos", "ucs2length_1", "usePattern", "useLoop", "loopAllRequired", "checkReportMissingProp", "allErrorsMode", "propertyInData", "loopUntilMissing", "checkMissingProp", "reportMissingProp", "exitOnErrorMode", "<PERSON><PERSON><PERSON>", "limitNumber_1", "multipleOf_1", "limitLength_1", "pattern_1", "limitProperties_1", "required_1", "limitItems_1", "itemTypes", "loopN", "indices", "loopN2", "eql", "equal_1", "outer", "getEql", "vSchema", "equalCode", "validateAdditionalItems", "validateTuple", "validate<PERSON><PERSON>y", "extraItems", "schArr", "l", "checkStrictTuple", "items_1", "prefixItems", "additionalItems_1", "min", "minContains", "maxContains", "validateItemsWithCount", "checkLimits", "_valid", "depsCount", "propDeps", "schDeps", "propertyDeps", "schemaDeps", "splitDependencies", "validatePropertyDeps", "validateSchemaDeps", "hasProperty", "depProp", "additionalProperty", "removeAdditional", "patProps", "deleteAdditional", "additionalPropertyCode", "applyAdditionalSchema", "definedProp", "propsSchema", "isAdditional", "additionalProperties_1", "allProps", "<PERSON><PERSON><PERSON><PERSON>", "applyPropertySchema", "patterns", "alwaysValidPatterns", "checkProperties", "allowMatchingProperties", "checkMatchingProperties", "pat", "validateProperties", "<PERSON><PERSON><PERSON><PERSON>", "validatePatternProperties", "validateUnion", "passing", "discriminator", "hasSchema", "if<PERSON>lause", "has<PERSON>hen", "<PERSON><PERSON><PERSON><PERSON>", "validateIf", "validate<PERSON><PERSON><PERSON>", "draft2020", "applicator", "not_1", "anyOf_1", "oneOf_1", "allOf_1", "if_1", "thenElse_1", "propertyNames_1", "dependencies_1", "properties_1", "patternProperties_1", "prefixItems_1", "items2020_1", "contains_1", "fmts", "fDef", "fType", "callFormat", "validData", "invalidFmt", "validate$DataFormat", "formatDef", "unknownMsg", "unknownFormat", "fmtType", "fmtRef", "fmtDef", "fmt", "getFormat", "validCondition", "validateFormat", "draft7Vocabularies", "core_1", "validation_1", "applicator_1", "format_1", "metadata_1", "metadataVocabulary", "contentVocabulary", "DiscrError", "discrError", "tagName", "types_1", "Tag", "tag", "mapping", "applyTagSchema", "oneOfMapping", "topRequired", "hasRequired", "tagRequired", "propSch", "addMappings", "addMapping", "tagValue", "getMapping", "Mapping", "validateMapping", "META_SUPPORT_DATA", "META_SCHEMA_ID", "draft7_1", "discriminator_1", "draft7MetaSchema", "used", "ajv_1", "KEYWORD_PROPERTY_PARAMS", "dependentRequired", "INTERPOLATION", "INTERPOLATION_REPLACE", "EMPTY_STR", "errorMessage", "matchKeywordError", "kwdErrs", "getTemplatesCode", "msgs", "templatesCode", "templateFunc", "templateExpr", "ptr", "kwdPropErrors", "kwdErrors", "emSchema", "propErrors", "kwdSch", "errMap", "keywordErrorsConfig", "templates", "singleError", "paramsErrors", "loopErrors", "errMessage", "processKeywordErrors", "kwdPropParams", "prop<PERSON><PERSON><PERSON>", "keyProp", "tmpl", "processKeywordPropErrors", "childErrors", "isObj", "isArr", "childErrs", "childKwd", "childProp", "init", "children", "childRegex", "child", "ifMatchesChildError", "processChildErrors", "childErrorsConfig", "schMessage", "matchAnyError", "processAllErrors", "keepErrors", "removeUsedErrors", "stringMap", "stringOrMap", "stringList", "parseErrorSchema", "validateAllFieldCriteria", "previous", "substring", "appendErrors", "concat", "ajvResolver", "schemaOptions", "resolverOptions", "shouldUseNativeValidation", "validateFieldsNatively", "toNestError", "criteriaMode"], "mappings": "opBACAA,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,aAAqBA,mBAA2BA,cAAsBA,gBAAwBA,YAAoBA,YAAoBA,aAAqBA,MAAcA,IAAYA,MAAcA,QAAgBA,OAAeA,aAAqBA,mBAAsB,EAC7Q,MAAMC,GAEND,cAAsBC,EACtBD,aAAqB,wBACrB,MAAME,UAAaD,EACfE,YAAYC,GAER,GADAC,SACKL,EAAQM,WAAWC,KAAKH,GACzB,MAAM,IAAII,MAAM,4CACpBC,KAAKC,IAAMN,EAEfO,WACI,OAAOF,KAAKC,IAEhBE,WACI,OAAO,EAEXC,YACI,MAAO,CAAE,CAACJ,KAAKC,KAAM,IAG7BV,OAAeE,EACf,MAAMY,UAAcb,EAChBE,YAAYY,GACRV,QACAI,KAAKO,OAAyB,iBAATD,EAAoB,CAACA,GAAQA,EAEtDJ,WACI,OAAOF,KAAKC,IAEhBE,WACI,GAAIH,KAAKO,OAAOC,OAAS,EACrB,OAAO,EACX,MAAMC,EAAOT,KAAKO,OAAO,GACzB,MAAgB,KAATE,GAAwB,OAATA,EAE1BR,UACI,IAAIS,EACJ,OAA6B,QAApBA,EAAKV,KAAKW,YAAyB,IAAPD,EAAgBA,EAAMV,KAAKW,KAAOX,KAAKO,OAAOK,OAAO,CAACjB,EAAGkB,IAAM,GAAGlB,IAAIkB,IAAK,IAEpHT,YACI,IAAIM,EACJ,OAA+B,QAAtBA,EAAKV,KAAKc,cAA2B,IAAPJ,EAAgBA,EAAMV,KAAKc,OAASd,KAAKO,OAAOK,OAAO,CAACR,EAAOS,KAC9FA,aAAapB,IACbW,EAAMS,EAAEZ,MAAQG,EAAMS,EAAEZ,MAAQ,GAAK,GAClCG,GACR,KAKX,SAASW,EAAEC,KAASC,GAChB,MAAMX,EAAO,CAACU,EAAK,IACnB,IAAIE,EAAI,EACR,KAAOA,EAAID,EAAKT,QACZW,EAAWb,EAAMW,EAAKC,IACtBZ,EAAKc,KAAKJ,IAAOE,IAErB,OAAO,IAAIb,EAAMC,GATrBf,QAAgBc,EAChBd,MAAc,IAAIc,EAAM,IAUxBd,IAAYwB,EACZ,MAAMM,EAAO,IAAIhB,EAAM,KACvB,SAASJ,EAAIe,KAASC,GAClB,MAAMK,EAAO,CAACC,EAAcP,EAAK,KACjC,IAAIE,EAAI,EACR,KAAOA,EAAID,EAAKT,QACZc,EAAKF,KAAKC,GACVF,EAAWG,EAAML,EAAKC,IACtBI,EAAKF,KAAKC,EAAME,EAAcP,IAAOE,KAGzC,OAYJ,SAAkBI,GACd,IAAIJ,EAAI,EACR,KAAOA,EAAII,EAAKd,OAAS,GAAG,CACxB,GAAIc,EAAKJ,KAAOG,EAAM,CAClB,MAAMG,EAAMC,EAAeH,EAAKJ,EAAI,GAAII,EAAKJ,EAAI,IACjD,QAAYQ,IAARF,EAAmB,CACnBF,EAAKK,OAAOT,EAAI,EAAG,EAAGM,GACtB,SAEJF,EAAKJ,KAAO,IAEhBA,KAxBJU,CAASN,GACF,IAAIjB,EAAMiB,GAGrB,SAASH,EAAWb,EAAMuB,GA8C1B,IAAqBC,EA7CbD,aAAexB,EACfC,EAAKc,QAAQS,EAAItB,QAEjBD,EAAKc,KADAS,aAAepC,EACVoC,EA2CK,iBADFC,EAxCSD,IAyCiB,kBAALC,GAAwB,OAANA,EAClDA,EACAP,EAAcQ,MAAMC,QAAQF,GAAKA,EAAEG,KAAK,KAAOH,IA1BzD,SAASL,EAAeS,EAAGC,GACvB,GAAU,OAANA,EACA,OAAOD,EACX,GAAU,OAANA,EACA,OAAOC,EACX,GAAgB,iBAALD,EAAe,CACtB,GAAIC,aAAa1C,GAA4B,MAApByC,EAAEA,EAAE1B,OAAS,GAClC,OACJ,MAAgB,iBAAL2B,EACA,GAAGD,EAAEE,MAAM,GAAI,KAAKD,KAClB,MAATA,EAAE,GACKD,EAAEE,MAAM,GAAI,GAAKD,EAAEC,MAAM,QACpC,EAEJ,MAAgB,iBAALD,GAA0B,MAATA,EAAE,IAAgBD,aAAazC,OAA3D,EACW,IAAIyC,IAAIC,EAAEC,MAAM,KAiB/B,SAASb,EAAcO,GACnB,OAAOO,KAAKC,UAAUR,GACjBS,QAAQ,UAAW,WACnBA,QAAQ,UAAW,WA3D5BhD,MAAcU,EASdV,aAAqB4B,EAoCrB5B,YAHA,SAAmBiD,EAAIC,GACnB,OAAOA,EAAGtC,WAAaqC,EAAKA,EAAGrC,WAAasC,EAAKxC,CAAI,GAAGuC,IAAKC,KAYjElD,YAHA,SAAmBuC,GACf,OAAO,IAAIzB,EAAMkB,EAAcO,KAQnCvC,gBAAwBgC,EAIxBhC,cAHA,SAAqBmD,GACjB,MAAqB,iBAAPA,GAAmBnD,EAAQM,WAAWC,KAAK4C,GAAO,IAAIrC,EAAM,IAAIqC,KAAS3B,CAAE,IAAI2B,MAUjGnD,mBANA,SAA0BmD,GACtB,GAAkB,iBAAPA,GAAmBnD,EAAQM,WAAWC,KAAK4C,GAClD,OAAO,IAAIrC,EAAM,GAAGqC,KAExB,MAAM,IAAI3C,MAAM,iCAAiC2C,qCAMrDnD,aAHA,SAAoBoD,GAChB,OAAO,IAAItC,EAAMsC,EAAGzC,iCCtJxBd,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,aAAqBA,iBAAyBA,QAAgBA,WAAmBA,sBAAyB,EAE1G,MAAMqD,UAAmB7C,MACrBL,YAAYmD,GACRjD,MAAM,uBAAuBiD,iBAC7B7C,KAAKV,MAAQuD,EAAKvD,OAG1B,IAAIwD,GACJ,SAAWA,GACPA,EAAeA,EAAwB,QAAI,GAAK,UAChDA,EAAeA,EAA0B,UAAI,GAAK,YAFtD,CAGGA,EAAiBvD,EAAQuD,iBAAmBvD,iBAAyB,KACxEA,WAAmB,CACfwD,MAAO,IAAIC,EAAOvD,KAAK,SACvBwD,IAAK,IAAID,EAAOvD,KAAK,OACrByD,IAAK,IAAIF,EAAOvD,KAAK,QAEzB,MAAM0D,EACFzD,aAAY0D,SAAEA,EAAQC,OAAEA,GAAW,IAC/BrD,KAAKc,OAAS,GACdd,KAAKsD,UAAYF,EACjBpD,KAAKuD,QAAUF,EAEnBG,OAAOC,GACH,OAAOA,aAAwBT,EAAOvD,KAAOgE,EAAezD,KAAK6C,KAAKY,GAE1EZ,KAAKa,GACD,OAAO,IAAIV,EAAOvD,KAAKO,KAAK2D,SAASD,IAEzCC,SAASD,GAEL,MAAO,GAAGA,KADC1D,KAAKc,OAAO4C,IAAW1D,KAAK4D,WAAWF,IAC5BG,UAE1BD,WAAWF,GACP,IAAIhD,EAAIoD,EACR,IAAsF,QAAhFA,EAA6B,QAAvBpD,EAAKV,KAAKuD,eAA4B,IAAP7C,OAAgB,EAASA,EAAG4C,iBAA8B,IAAPQ,OAAgB,EAASA,EAAGC,IAAIL,KAAa1D,KAAKsD,YAActD,KAAKsD,UAAUS,IAAIL,GAC7K,MAAM,IAAI3D,MAAM,oBAAoB2D,mCAExC,OAAQ1D,KAAKc,OAAO4C,GAAU,CAAEA,OAAAA,EAAQG,MAAO,IAGvDtE,QAAgB4D,EAChB,MAAMa,UAAuBhB,EAAOvD,KAChCC,YAAYgE,EAAQO,GAChBrE,MAAMqE,GACNjE,KAAK0D,OAASA,EAElBQ,SAAS5E,GAAO6E,SAAEA,EAAQC,UAAEA,IACxBpE,KAAKV,MAAQA,EACbU,KAAKqE,UAAgBrB,EAAOjC,CAAG,IAAI,IAAIiC,EAAOvD,KAAK0E,MAAaC,MAGxE7E,iBAAyByE,EACzB,MAAMM,EAAWtB,EAAOjC,CAAG,KAqF3BxB,aApFA,cAAyB4D,EACrBzD,YAAY6E,GACR3E,MAAM2E,GACNvE,KAAKwE,QAAU,GACfxE,KAAKyE,OAASF,EAAKG,MACnB1E,KAAKuE,KAAO,IAAKA,EAAMI,GAAIJ,EAAKK,MAAQN,EAAOtB,EAAO6B,KAE1DC,MACI,OAAO9E,KAAKyE,OAEhB5B,KAAKa,GACD,OAAO,IAAIM,EAAeN,EAAQ1D,KAAK2D,SAASD,IAEpDpE,MAAMmE,EAAcnE,GAChB,IAAIoB,EACJ,QAAkBgB,IAAdpC,EAAMyF,IACN,MAAM,IAAIhF,MAAM,wCACpB,MAAM8C,EAAO7C,KAAKwD,OAAOC,IACnBC,OAAEA,GAAWb,EACbmC,EAAgC,QAApBtE,EAAKpB,EAAMoD,WAAwB,IAAPhC,EAAgBA,EAAKpB,EAAMyF,IACzE,IAAIE,EAAKjF,KAAKwE,QAAQd,GACtB,GAAIuB,EAAI,CACJ,MAAMC,EAAQD,EAAGH,IAAIE,GACrB,GAAIE,EACA,OAAOA,OAGXD,EAAKjF,KAAKwE,QAAQd,GAAU,IAAIyB,IAEpCF,EAAGG,IAAIJ,EAAUnC,GACjB,MAAMlD,EAAIK,KAAKyE,OAAOf,KAAY1D,KAAKyE,OAAOf,GAAU,IAClDU,EAAYzE,EAAEa,OAGpB,OAFAb,EAAEyE,GAAa9E,EAAMyF,IACrBlC,EAAKqB,SAAS5E,EAAO,CAAE6E,SAAUT,EAAQU,UAAAA,IAClCvB,EAEXwC,SAAS3B,EAAQ4B,GACb,MAAML,EAAKjF,KAAKwE,QAAQd,GACxB,GAAKuB,EAEL,OAAOA,EAAGH,IAAIQ,GAElBC,UAAUC,EAAWC,EAASzF,KAAKwE,SAC/B,OAAOxE,KAAK0F,cAAcD,EAAS5C,IAC/B,QAAuBnB,IAAnBmB,EAAKwB,UACL,MAAM,IAAItE,MAAM,kBAAkB8C,mBACtC,OAAWG,EAAOjC,CAAG,GAAGyE,IAAY3C,EAAKwB,cAGjDsB,UAAUF,EAASzF,KAAKwE,QAASoB,EAAYC,GACzC,OAAO7F,KAAK0F,cAAcD,EAAS5C,IAC/B,QAAmBnB,IAAfmB,EAAKvD,MACL,MAAM,IAAIS,MAAM,kBAAkB8C,mBACtC,OAAOA,EAAKvD,MAAMgB,MACnBsF,EAAYC,GAEnBH,cAAcD,EAAQK,EAAWF,EAAa,GAAIC,GAC9C,IAAIvF,EAAO0C,EAAO6B,IAClB,IAAK,MAAMnB,KAAU+B,EAAQ,CACzB,MAAMR,EAAKQ,EAAO/B,GAClB,IAAKuB,EACD,SACJ,MAAMc,EAAWH,EAAWlC,GAAUkC,EAAWlC,IAAW,IAAIyB,IAChEF,EAAGe,QAASnD,IACR,GAAIkD,EAAQhC,IAAIlB,GACZ,OACJkD,EAAQX,IAAIvC,EAAMC,EAAemD,SACjC,IAAIpF,EAAIiF,EAAUjD,GAClB,GAAIhC,EAEAP,EAAW0C,EAAOjC,CAAG,GAAGT,IADZN,KAAKuE,KAAK2B,IAAM3G,EAAQ4G,SAASjD,IAAM3D,EAAQ4G,SAASpD,SAC9BF,OAAUhC,KAAKb,KAAKuE,KAAKI,SAE9D,CAAA,KAAK9D,EAAIgF,MAAAA,OAAyC,EAASA,EAAQhD,IAIpE,MAAM,IAAID,EAAWC,GAHrBvC,EAAW0C,EAAOjC,CAAG,GAAGT,IAAOO,IAAIb,KAAKuE,KAAKI,KAKjDoB,EAAQX,IAAIvC,EAAMC,EAAesD,aAGzC,OAAO9F,wBCzIflB,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,KAAaA,MAAcA,MAAcA,UAAkBA,YAAoBA,WAAmBA,iBAAyBA,aAAqBA,QAAgBA,OAAeA,aAAqBA,YAAoBA,cAAsBA,MAAcA,YAAoBA,MAAcA,SAAY,EAG1S,IAAI8G,EAASrD,EACb5D,OAAOC,eAAeE,EAAS,IAAK,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOuB,EAAOtF,KACzF3B,OAAOC,eAAeE,EAAS,MAAO,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOuB,EAAOpG,OAC3Fb,OAAOC,eAAeE,EAAS,YAAa,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOuB,EAAOE,aACjGnH,OAAOC,eAAeE,EAAS,MAAO,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOuB,EAAOxB,OAC3FzF,OAAOC,eAAeE,EAAS,cAAe,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOuB,EAAOG,eACnGpH,OAAOC,eAAeE,EAAS,YAAa,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOuB,EAAO/D,aACjGlD,OAAOC,eAAeE,EAAS,aAAc,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOuB,EAAOI,cAClGrH,OAAOC,eAAeE,EAAS,OAAQ,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOuB,EAAO5G,QAC5F,IAAIiH,EAAUC,EACdvH,OAAOC,eAAeE,EAAS,QAAS,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAO4B,EAAQvD,SAC9F/D,OAAOC,eAAeE,EAAS,aAAc,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAO4B,EAAQE,cACnGxH,OAAOC,eAAeE,EAAS,iBAAkB,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAO4B,EAAQ1C,kBACvG5E,OAAOC,eAAeE,EAAS,WAAY,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAO4B,EAAQP,YACjG5G,YAAoB,CAChBsH,GAAI,IAAI7D,EAAO3C,MAAM,KACrByG,IAAK,IAAI9D,EAAO3C,MAAM,MACtB0G,GAAI,IAAI/D,EAAO3C,MAAM,KACrB2G,IAAK,IAAIhE,EAAO3C,MAAM,MACtB4G,GAAI,IAAIjE,EAAO3C,MAAM,OACrB6G,IAAK,IAAIlE,EAAO3C,MAAM,OACtB8G,IAAK,IAAInE,EAAO3C,MAAM,KACtB+G,GAAI,IAAIpE,EAAO3C,MAAM,MACrBgH,IAAK,IAAIrE,EAAO3C,MAAM,MACtBiH,IAAK,IAAItE,EAAO3C,MAAM,MAE1B,MAAMkH,EACFC,gBACI,OAAOxH,KAEXyH,cAAc3G,EAAQ4G,GAClB,OAAO1H,MAGf,MAAM2H,UAAYJ,EACd7H,YAAYkI,EAAS/E,EAAMgF,GACvBjI,QACAI,KAAK4H,QAAUA,EACf5H,KAAK6C,KAAOA,EACZ7C,KAAK6H,IAAMA,EAEfC,QAAO5B,IAAEA,EAAGvB,GAAEA,IAGV,MAAO,GAFSuB,EAAMS,EAAQR,SAASjD,IAAMlD,KAAK4H,WAE7B5H,KAAK6C,YADDnB,IAAb1B,KAAK6H,IAAoB,GAAK,MAAM7H,KAAK6H,SACXlD,EAE9C8C,cAAcrH,EAAO2H,GACjB,GAAK3H,EAAMJ,KAAK6C,KAAK5C,KAIrB,OAFID,KAAK6H,MACL7H,KAAK6H,IAAMG,EAAahI,KAAK6H,IAAKzH,EAAO2H,IACtC/H,KAEXI,YACI,OAAOJ,KAAK6H,eAAe7E,EAAOxD,YAAcQ,KAAK6H,IAAIzH,MAAQ,IAGzE,MAAM6H,UAAeV,EACjB7H,YAAYwI,EAAKL,EAAKM,GAClBvI,QACAI,KAAKkI,IAAMA,EACXlI,KAAK6H,IAAMA,EACX7H,KAAKmI,YAAcA,EAEvBL,QAAOnD,GAAEA,IACL,MAAO,GAAG3E,KAAKkI,SAASlI,KAAK6H,OAASlD,EAE1C8C,cAAcrH,EAAO2H,GACjB,KAAI/H,KAAKkI,eAAelF,EAAOvD,OAASW,EAAMJ,KAAKkI,IAAIjI,MAASD,KAAKmI,YAGrE,OADAnI,KAAK6H,IAAMG,EAAahI,KAAK6H,IAAKzH,EAAO2H,GAClC/H,KAEXI,YAEI,OAAOgI,EADOpI,KAAKkI,eAAelF,EAAOvD,KAAO,GAAK,IAAKO,KAAKkI,IAAI9H,OACxCJ,KAAK6H,MAGxC,MAAMQ,UAAiBJ,EACnBvI,YAAYwI,EAAKI,EAAIT,EAAKM,GACtBvI,MAAMsI,EAAKL,EAAKM,GAChBnI,KAAKsI,GAAKA,EAEdR,QAAOnD,GAAEA,IACL,MAAO,GAAG3E,KAAKkI,OAAOlI,KAAKsI,OAAOtI,KAAK6H,OAASlD,GAGxD,MAAM4D,UAAchB,EAChB7H,YAAY8I,GACR5I,QACAI,KAAKwI,MAAQA,EACbxI,KAAKI,MAAQ,GAEjB0H,QAAOnD,GAAEA,IACL,MAAO,GAAG3E,KAAKwI,SAAW7D,GAGlC,MAAM8D,UAAclB,EAChB7H,YAAY8I,GACR5I,QACAI,KAAKwI,MAAQA,EACbxI,KAAKI,MAAQ,GAEjB0H,QAAOnD,GAAEA,IAEL,MAAO,QADO3E,KAAKwI,MAAQ,IAAIxI,KAAKwI,QAAU,MACpB7D,GAGlC,MAAM+D,UAAcnB,EAChB7H,YAAYiJ,GACR/I,QACAI,KAAK2I,MAAQA,EAEjBb,QAAOnD,GAAEA,IACL,MAAO,SAAS3E,KAAK2I,SAAWhE,EAEpCvE,YACI,OAAOJ,KAAK2I,MAAMvI,OAG1B,MAAMwI,UAAgBrB,EAClB7H,YAAYY,GACRV,QACAI,KAAKM,KAAOA,EAEhBwH,QAAOnD,GAAEA,IACL,MAAO,GAAG3E,KAAKM,QAAUqE,EAE7B6C,gBACI,MAAO,GAAGxH,KAAKM,OAASN,UAAO0B,EAEnC+F,cAAcrH,EAAO2H,GAEjB,OADA/H,KAAKM,KAAO0H,EAAahI,KAAKM,KAAMF,EAAO2H,GACpC/H,KAEXI,YACI,OAAOJ,KAAKM,gBAAgB0C,EAAOxD,YAAcQ,KAAKM,KAAKF,MAAQ,IAG3E,MAAMyI,UAAmBtB,EACrB7H,YAAYoJ,EAAQ,IAChBlJ,QACAI,KAAK8I,MAAQA,EAEjBhB,OAAOvD,GACH,OAAOvE,KAAK8I,MAAMlI,OAAO,CAACN,EAAMyI,IAAMzI,EAAOyI,EAAEjB,OAAOvD,GAAO,IAEjEiD,gBACI,MAAMsB,MAAEA,GAAU9I,KAClB,IAAIkB,EAAI4H,EAAMtI,OACd,KAAOU,KAAK,CACR,MAAM6H,EAAID,EAAM5H,GAAGsG,gBACfzF,MAAMC,QAAQ+G,GACdD,EAAMnH,OAAOT,EAAG,KAAM6H,GACjBA,EACLD,EAAM5H,GAAK6H,EAEXD,EAAMnH,OAAOT,EAAG,GAExB,OAAO4H,EAAMtI,OAAS,EAAIR,UAAO0B,EAErC+F,cAAcrH,EAAO2H,GACjB,MAAMe,MAAEA,GAAU9I,KAClB,IAAIkB,EAAI4H,EAAMtI,OACd,KAAOU,KAAK,CAER,MAAM6H,EAAID,EAAM5H,GACZ6H,EAAEtB,cAAcrH,EAAO2H,KAE3BiB,EAAc5I,EAAO2I,EAAE3I,OACvB0I,EAAMnH,OAAOT,EAAG,IAEpB,OAAO4H,EAAMtI,OAAS,EAAIR,UAAO0B,EAErCtB,YACI,OAAOJ,KAAK8I,MAAMlI,OAAO,CAACR,EAAO2I,IAAME,EAAS7I,EAAO2I,EAAE3I,OAAQ,KAGzE,MAAM8I,UAAkBL,EACpBf,OAAOvD,GACH,MAAO,IAAMA,EAAKI,GAAK/E,MAAMkI,OAAOvD,GAAQ,IAAMA,EAAKI,IAG/D,MAAMwE,UAAaN,GAEnB,MAAMO,UAAaF,GAEnBE,EAAKC,KAAO,OACZ,MAAMC,UAAWJ,EACbxJ,YAAY6J,EAAWT,GACnBlJ,MAAMkJ,GACN9I,KAAKuJ,UAAYA,EAErBzB,OAAOvD,GACH,IAAIjE,EAAO,MAAMN,KAAKuJ,aAAe3J,MAAMkI,OAAOvD,GAGlD,OAFIvE,KAAKwJ,OACLlJ,GAAQ,QAAUN,KAAKwJ,KAAK1B,OAAOvD,IAChCjE,EAEXkH,gBACI5H,MAAM4H,gBACN,MAAMiC,EAAOzJ,KAAKuJ,UAClB,IAAa,IAATE,EACA,OAAOzJ,KAAK8I,MAChB,IAAIY,EAAI1J,KAAKwJ,KACb,GAAIE,EAAG,CACH,MAAMC,EAAKD,EAAElC,gBACbkC,EAAI1J,KAAKwJ,KAAOzH,MAAMC,QAAQ2H,GAAM,IAAIP,EAAKO,GAAMA,EAEvD,OAAID,GACa,IAATD,EACOC,aAAaJ,EAAKI,EAAIA,EAAEZ,MAC/B9I,KAAK8I,MAAMtI,OACJR,KACJ,IAAIsJ,EAAGM,EAAIH,GAAOC,aAAaJ,EAAK,CAACI,GAAKA,EAAEZ,QAE1C,IAATW,GAAmBzJ,KAAK8I,MAAMtI,OAE3BR,UAFP,EAIJyH,cAAcrH,EAAO2H,GACjB,IAAIrH,EAEJ,GADAV,KAAKwJ,KAA4B,QAApB9I,EAAKV,KAAKwJ,YAAyB,IAAP9I,OAAgB,EAASA,EAAG+G,cAAcrH,EAAO2H,GACpFnI,MAAM6H,cAAcrH,EAAO2H,IAAc/H,KAAKwJ,KAGpD,OADAxJ,KAAKuJ,UAAYvB,EAAahI,KAAKuJ,UAAWnJ,EAAO2H,GAC9C/H,KAEXI,YACI,MAAMA,EAAQR,MAAMQ,MAIpB,OAHAgI,EAAahI,EAAOJ,KAAKuJ,WACrBvJ,KAAKwJ,MACLP,EAAS7I,EAAOJ,KAAKwJ,KAAKpJ,OACvBA,GAGfkJ,EAAGD,KAAO,KACV,MAAMQ,UAAYX,GAElBW,EAAIR,KAAO,MACX,MAAMS,UAAgBD,EAClBnK,YAAYqK,GACRnK,QACAI,KAAK+J,UAAYA,EAErBjC,OAAOvD,GACH,MAAO,OAAOvE,KAAK+J,aAAenK,MAAMkI,OAAOvD,GAEnDkD,cAAcrH,EAAO2H,GACjB,GAAKnI,MAAM6H,cAAcrH,EAAO2H,GAGhC,OADA/H,KAAK+J,UAAY/B,EAAahI,KAAK+J,UAAW3J,EAAO2H,GAC9C/H,KAEXI,YACI,OAAO6I,EAASrJ,MAAMQ,MAAOJ,KAAK+J,UAAU3J,QAGpD,MAAM4J,UAAiBH,EACnBnK,YAAYkI,EAAS/E,EAAMoH,EAAMC,GAC7BtK,QACAI,KAAK4H,QAAUA,EACf5H,KAAK6C,KAAOA,EACZ7C,KAAKiK,KAAOA,EACZjK,KAAKkK,GAAKA,EAEdpC,OAAOvD,GACH,MAAMqD,EAAUrD,EAAK2B,IAAMS,EAAQR,SAASjD,IAAMlD,KAAK4H,SACjD/E,KAAEA,EAAIoH,KAAEA,EAAIC,GAAEA,GAAOlK,KAC3B,MAAO,OAAO4H,KAAW/E,KAAQoH,MAASpH,KAAQqH,MAAOrH,OAAYjD,MAAMkI,OAAOvD,GAEtFnE,YACI,MAAMA,EAAQgI,EAAaxI,MAAMQ,MAAOJ,KAAKiK,MAC7C,OAAO7B,EAAahI,EAAOJ,KAAKkK,KAGxC,MAAMC,UAAgBN,EAClBnK,YAAY0K,EAAMxC,EAAS/E,EAAMwH,GAC7BzK,QACAI,KAAKoK,KAAOA,EACZpK,KAAK4H,QAAUA,EACf5H,KAAK6C,KAAOA,EACZ7C,KAAKqK,SAAWA,EAEpBvC,OAAOvD,GACH,MAAO,OAAOvE,KAAK4H,WAAW5H,KAAK6C,QAAQ7C,KAAKoK,QAAQpK,KAAKqK,YAAczK,MAAMkI,OAAOvD,GAE5FkD,cAAcrH,EAAO2H,GACjB,GAAKnI,MAAM6H,cAAcrH,EAAO2H,GAGhC,OADA/H,KAAKqK,SAAWrC,EAAahI,KAAKqK,SAAUjK,EAAO2H,GAC5C/H,KAEXI,YACI,OAAO6I,EAASrJ,MAAMQ,MAAOJ,KAAKqK,SAASjK,QAGnD,MAAMkK,UAAapB,EACfxJ,YAAYmD,EAAM5B,EAAMsJ,GACpB3K,QACAI,KAAK6C,KAAOA,EACZ7C,KAAKiB,KAAOA,EACZjB,KAAKuK,MAAQA,EAEjBzC,OAAOvD,GAEH,MAAO,GADQvE,KAAKuK,MAAQ,SAAW,cACXvK,KAAK6C,QAAQ7C,KAAKiB,QAAUrB,MAAMkI,OAAOvD,IAG7E+F,EAAKjB,KAAO,OACZ,MAAMmB,UAAe3B,EACjBf,OAAOvD,GACH,MAAO,UAAY3E,MAAMkI,OAAOvD,IAGxCiG,EAAOnB,KAAO,SACd,MAAMoB,UAAYvB,EACdpB,OAAOvD,GACH,IAAIjE,EAAO,MAAQV,MAAMkI,OAAOvD,GAKhC,OAJIvE,KAAK0K,QACLpK,GAAQN,KAAK0K,MAAM5C,OAAOvD,IAC1BvE,KAAK2K,UACLrK,GAAQN,KAAK2K,QAAQ7C,OAAOvD,IACzBjE,EAEXkH,gBACI,IAAI9G,EAAIoD,EAIR,OAHAlE,MAAM4H,gBACgB,QAArB9G,EAAKV,KAAK0K,aAA0B,IAAPhK,GAAyBA,EAAG8G,gBAClC,QAAvB1D,EAAK9D,KAAK2K,eAA4B,IAAP7G,GAAyBA,EAAG0D,gBACrDxH,KAEXyH,cAAcrH,EAAO2H,GACjB,IAAIrH,EAAIoD,EAIR,OAHAlE,MAAM6H,cAAcrH,EAAO2H,GACL,QAArBrH,EAAKV,KAAK0K,aAA0B,IAAPhK,GAAyBA,EAAG+G,cAAcrH,EAAO2H,GACvD,QAAvBjE,EAAK9D,KAAK2K,eAA4B,IAAP7G,GAAyBA,EAAG2D,cAAcrH,EAAO2H,GAC1E/H,KAEXI,YACI,MAAMA,EAAQR,MAAMQ,MAKpB,OAJIJ,KAAK0K,OACLzB,EAAS7I,EAAOJ,KAAK0K,MAAMtK,OAC3BJ,KAAK2K,SACL1B,EAAS7I,EAAOJ,KAAK2K,QAAQvK,OAC1BA,GAGf,MAAMwK,UAAc1B,EAChBxJ,YAAYiJ,GACR/I,QACAI,KAAK2I,MAAQA,EAEjBb,OAAOvD,GACH,MAAO,SAASvE,KAAK2I,SAAW/I,MAAMkI,OAAOvD,IAGrDqG,EAAMvB,KAAO,QACb,MAAMwB,UAAgB3B,EAClBpB,OAAOvD,GACH,MAAO,UAAY3E,MAAMkI,OAAOvD,IA+QxC,SAAS0E,EAAS7I,EAAO6J,GACrB,IAAK,MAAMlB,KAAKkB,EACZ7J,EAAM2I,IAAM3I,EAAM2I,IAAM,IAAMkB,EAAKlB,IAAM,GAC7C,OAAO3I,EAEX,SAASgI,EAAahI,EAAO6J,GACzB,OAAOA,aAAgBjH,EAAOxD,YAAcyJ,EAAS7I,EAAO6J,EAAK7J,OAASA,EAE9E,SAAS4H,EAAa1G,EAAMlB,EAAO2H,GAC/B,OAAIzG,aAAgB0B,EAAOvD,KAChBqL,EAAYxJ,IAmBFoI,EAlBJpI,aAmBQ0B,EAAO3C,OACxBqJ,EAAEnJ,OAAOwK,KAAMlK,GAAMA,aAAamC,EAAOvD,MAAyB,IAAjBW,EAAMS,EAAEZ,WAAmCyB,IAArBqG,EAAUlH,EAAEZ,MAlBpF,IAAI+C,EAAO3C,MAAMiB,EAAKf,OAAOK,OAAO,CAACoK,EAAOnK,KAC3CA,aAAamC,EAAOvD,OACpBoB,EAAIiK,EAAYjK,IAChBA,aAAamC,EAAO3C,MACpB2K,EAAM5J,QAAQP,EAAEN,QAEhByK,EAAM5J,KAAKP,GACRmK,GACR,KATQ1J,EAiBX,IAAqBoI,EAPrB,SAASoB,EAAY/B,GACjB,MAAMlI,EAAIkH,EAAUgB,EAAE9I,KACtB,YAAUyB,IAANb,GAAoC,IAAjBT,EAAM2I,EAAE9I,KACpB8I,UACJ3I,EAAM2I,EAAE9I,KACRY,IAOf,SAASmI,EAAc5I,EAAO6J,GAC1B,IAAK,MAAMlB,KAAKkB,EACZ7J,EAAM2I,IAAM3I,EAAM2I,IAAM,IAAMkB,EAAKlB,IAAM,GAEjD,SAASa,EAAI9H,GACT,MAAmB,kBAALA,GAA8B,iBAALA,GAAuB,OAANA,GAAcA,EAAQkB,EAAOjC,CAAG,IAAIkK,EAAInJ,KAnTpG+I,EAAQxB,KAAO,UA2Qf9J,UA1QA,MACIG,YAAYwL,EAAU3G,EAAO,IACzBvE,KAAKwE,QAAU,GACfxE,KAAKmL,aAAe,GACpBnL,KAAK0H,WAAa,GAClB1H,KAAKuE,KAAO,IAAKA,EAAMI,GAAIJ,EAAKK,MAAQ,KAAO,IAC/C5E,KAAKoL,UAAYF,EACjBlL,KAAKyE,OAAS,IAAIkC,EAAQxD,MAAM,CAAEE,OAAQ6H,IAC1ClL,KAAKqL,OAAS,CAAC,IAAIlC,GAEvBjJ,WACI,OAAOF,KAAKsL,MAAMxD,OAAO9H,KAAKuE,MAGlC1B,KAAKa,GACD,OAAO1D,KAAKyE,OAAO5B,KAAKa,GAG5B8B,UAAU9B,GACN,OAAO1D,KAAKoL,UAAUvI,KAAKa,GAG/B6H,WAAWC,EAAclM,GACrB,MAAMuD,EAAO7C,KAAKoL,UAAU9L,MAAMkM,EAAclM,GAGhD,OAFWU,KAAKwE,QAAQ3B,EAAKa,UAAY1D,KAAKwE,QAAQ3B,EAAKa,QAAU,IAAI+H,MACtEC,IAAI7I,GACAA,EAEX8I,cAAcjI,EAAQ4B,GAClB,OAAOtF,KAAKoL,UAAU/F,SAAS3B,EAAQ4B,GAI3CC,UAAUC,GACN,OAAOxF,KAAKoL,UAAU7F,UAAUC,EAAWxF,KAAKwE,SAEpDmB,YACI,OAAO3F,KAAKoL,UAAUzF,UAAU3F,KAAKwE,SAEzCoH,KAAKhE,EAASnE,EAAcoE,EAAKgE,GAC7B,MAAMhJ,EAAO7C,KAAKyE,OAAOjB,OAAOC,GAIhC,YAHY/B,IAARmG,GAAqBgE,IACrB7L,KAAK0H,WAAW7E,EAAK5C,KAAO4H,GAChC7H,KAAK8L,UAAU,IAAInE,EAAIC,EAAS/E,EAAMgF,IAC/BhF,EAGXE,MAAMU,EAAcoE,EAAKkE,GACrB,OAAO/L,KAAK4L,KAAKjF,EAAQR,SAASpD,MAAOU,EAAcoE,EAAKkE,GAGhE9I,IAAIQ,EAAcoE,EAAKkE,GACnB,OAAO/L,KAAK4L,KAAKjF,EAAQR,SAASlD,IAAKQ,EAAcoE,EAAKkE,GAG9D7I,IAAIO,EAAcoE,EAAKkE,GACnB,OAAO/L,KAAK4L,KAAKjF,EAAQR,SAASjD,IAAKO,EAAcoE,EAAKkE,GAG9DC,OAAO9D,EAAKL,EAAKM,GACb,OAAOnI,KAAK8L,UAAU,IAAI7D,EAAOC,EAAKL,EAAKM,IAG/CuD,IAAIxD,EAAKL,GACL,OAAO7H,KAAK8L,UAAU,IAAIzD,EAASH,EAAK3I,EAAQ0M,UAAU3E,IAAKO,IAGnEvH,KAAKO,GAKD,MAJgB,mBAALA,EACPA,IACKA,IAAMmC,EAAO6B,KAClB7E,KAAK8L,UAAU,IAAIlD,EAAQ/H,IACxBb,KAGXkM,UAAUC,GACN,MAAM7L,EAAO,CAAC,KACd,IAAK,MAAOoC,EAAKpD,KAAU6M,EACnB7L,EAAKE,OAAS,GACdF,EAAKc,KAAK,KACdd,EAAKc,KAAKsB,IACNA,IAAQpD,GAASU,KAAKuE,KAAK2B,OAC3B5F,EAAKc,KAAK,MACV,EAAI4B,EAAO7B,YAAYb,EAAMhB,IAIrC,OADAgB,EAAKc,KAAK,KACH,IAAI4B,EAAO3C,MAAMC,GAG5B8L,GAAG7C,EAAW8C,EAAUC,GAEpB,GADAtM,KAAKuM,WAAW,IAAIjD,EAAGC,IACnB8C,GAAYC,EACZtM,KAAKM,KAAK+L,GAAU7C,OAAOlJ,KAAKgM,GAAUE,aAEzC,GAAIH,EACLrM,KAAKM,KAAK+L,GAAUG,aAEnB,GAAIF,EACL,MAAM,IAAIvM,MAAM,4CAEpB,OAAOC,KAGXyM,OAAOlD,GACH,OAAOvJ,KAAK0M,UAAU,IAAIpD,EAAGC,IAGjCC,OACI,OAAOxJ,KAAK0M,UAAU,IAAItD,GAG9BoD,QACI,OAAOxM,KAAK2M,cAAcrD,EAAIF,GAElCwD,KAAKC,EAAMC,GAIP,OAHA9M,KAAKuM,WAAWM,GACZC,GACA9M,KAAKM,KAAKwM,GAASC,SAChB/M,KAGXgN,IAAIjD,EAAW+C,GACX,OAAO9M,KAAK4M,KAAK,IAAI9C,EAAQC,GAAY+C,GAG7CG,SAASxJ,EAAcwG,EAAMC,EAAI4C,EAASlF,GAAU5H,KAAKuE,KAAK2B,IAAMS,EAAQR,SAASjD,IAAMyD,EAAQR,SAASlD,MACxG,MAAMJ,EAAO7C,KAAKyE,OAAOjB,OAAOC,GAChC,OAAOzD,KAAK4M,KAAK,IAAI5C,EAASpC,EAAS/E,EAAMoH,EAAMC,GAAK,IAAM4C,EAAQjK,IAG1EqK,MAAMzJ,EAAc4G,EAAUyC,EAASlF,EAAUjB,EAAQR,SAASpD,OAC9D,MAAMF,EAAO7C,KAAKyE,OAAOjB,OAAOC,GAChC,GAAIzD,KAAKuE,KAAK2B,IAAK,CACf,MAAMiH,EAAM9C,aAAoBrH,EAAOvD,KAAO4K,EAAWrK,KAAKkD,IAAI,OAAQmH,GAC1E,OAAOrK,KAAKiN,SAAS,KAAM,EAAOjK,EAAOjC,CAAG,GAAGoM,WAAejM,IAC1DlB,KAAKkD,IAAIL,EAAUG,EAAOjC,CAAG,GAAGoM,KAAOjM,MACvC4L,EAAQjK,KAGhB,OAAO7C,KAAK4M,KAAK,IAAIzC,EAAQ,KAAMvC,EAAS/E,EAAMwH,GAAW,IAAMyC,EAAQjK,IAI/EuK,MAAM3J,EAAc4J,EAAKP,EAASlF,GAAU5H,KAAKuE,KAAK2B,IAAMS,EAAQR,SAASjD,IAAMyD,EAAQR,SAASpD,QAChG,GAAI/C,KAAKuE,KAAK+I,cACV,OAAOtN,KAAKkN,MAAMzJ,EAAkBT,EAAOjC,CAAG,eAAesM,KAAQP,GAEzE,MAAMjK,EAAO7C,KAAKyE,OAAOjB,OAAOC,GAChC,OAAOzD,KAAK4M,KAAK,IAAIzC,EAAQ,KAAMvC,EAAS/E,EAAMwK,GAAM,IAAMP,EAAQjK,IAG1EkK,SACI,OAAO/M,KAAK2M,cAAc9C,GAG9BrB,MAAMA,GACF,OAAOxI,KAAK8L,UAAU,IAAIvD,EAAMC,IAGpC+E,MAAM/E,GACF,OAAOxI,KAAK8L,UAAU,IAAIrD,EAAMD,IAGpCgF,OAAOlO,GACH,MAAMuN,EAAO,IAAIrC,EAGjB,GAFAxK,KAAKuM,WAAWM,GAChB7M,KAAKM,KAAKhB,GACgB,IAAtBuN,EAAK/D,MAAMtI,OACX,MAAM,IAAIT,MAAM,0CACpB,OAAOC,KAAK2M,cAAcnC,GAG9BiD,IAAIC,EAASC,EAAWC,GACpB,IAAKD,IAAcC,EACf,MAAM,IAAI7N,MAAM,gDACpB,MAAM8M,EAAO,IAAIpC,EAGjB,GAFAzK,KAAKuM,WAAWM,GAChB7M,KAAKM,KAAKoN,GACNC,EAAW,CACX,MAAMhF,EAAQ3I,KAAK6C,KAAK,KACxB7C,KAAK6N,UAAYhB,EAAKnC,MAAQ,IAAIE,EAAMjC,GACxCgF,EAAUhF,GAMd,OAJIiF,IACA5N,KAAK6N,UAAYhB,EAAKlC,QAAU,IAAIE,EACpC7K,KAAKM,KAAKsN,IAEP5N,KAAK2M,cAAc/B,EAAOC,GAGrCiD,MAAMnF,GACF,OAAO3I,KAAK8L,UAAU,IAAIpD,EAAMC,IAGpCoF,MAAMC,EAAMC,GAIR,OAHAjO,KAAKmL,aAAa/J,KAAKpB,KAAKqL,OAAO7K,QAC/BwN,GACAhO,KAAKM,KAAK0N,GAAME,SAASD,GACtBjO,KAGXkO,SAASD,GACL,MAAME,EAAMnO,KAAKmL,aAAaiD,MAC9B,QAAY1M,IAARyM,EACA,MAAM,IAAIpO,MAAM,wCACpB,MAAMsO,EAAUrO,KAAKqL,OAAO7K,OAAS2N,EACrC,GAAIE,EAAU,QAAoB3M,IAAduM,GAA2BI,IAAYJ,EACvD,MAAM,IAAIlO,MAAM,mCAAmCsO,QAAcJ,cAGrE,OADAjO,KAAKqL,OAAO7K,OAAS2N,EACdnO,KAGXsO,KAAKzL,EAAM5B,EAAO+B,EAAO6B,IAAK0F,EAAOgE,GAIjC,OAHAvO,KAAKuM,WAAW,IAAIjC,EAAKzH,EAAM5B,EAAMsJ,IACjCgE,GACAvO,KAAKM,KAAKiO,GAAUC,UACjBxO,KAGXwO,UACI,OAAOxO,KAAK2M,cAAcrC,GAE9B1I,SAASmH,EAAI,GACT,KAAOA,KAAM,GACT/I,KAAKsL,MAAM9D,gBACXxH,KAAKsL,MAAM7D,cAAczH,KAAKsL,MAAMlL,MAAOJ,KAAK0H,YAGxDoE,UAAUe,GAEN,OADA7M,KAAK6N,UAAU/E,MAAM1H,KAAKyL,GACnB7M,KAEXuM,WAAWM,GACP7M,KAAK6N,UAAU/E,MAAM1H,KAAKyL,GAC1B7M,KAAKqL,OAAOjK,KAAKyL,GAErBF,cAAc8B,EAAIC,GACd,MAAM3F,EAAI/I,KAAK6N,UACf,GAAI9E,aAAa0F,GAAOC,GAAM3F,aAAa2F,EAEvC,OADA1O,KAAKqL,OAAO+C,MACLpO,KAEX,MAAM,IAAID,MAAM,0BAA0B2O,EAAK,GAAGD,EAAGpF,QAAQqF,EAAGrF,OAASoF,EAAGpF,SAEhFqD,UAAUG,GACN,MAAM9D,EAAI/I,KAAK6N,UACf,KAAM9E,aAAaO,GACf,MAAM,IAAIvJ,MAAM,gCAGpB,OADAC,KAAK6N,UAAY9E,EAAES,KAAOqD,EACnB7M,KAEXsL,YACI,OAAOtL,KAAKqL,OAAO,GAEvBwC,gBACI,MAAMlE,EAAK3J,KAAKqL,OAChB,OAAO1B,EAAGA,EAAGnJ,OAAS,GAE1BqN,cAAchB,GACV,MAAMlD,EAAK3J,KAAKqL,OAChB1B,EAAGA,EAAGnJ,OAAS,GAAKqM,IA6C5BtN,MAAcqK,EACd,MAAM+E,EAAUC,EAAQrP,EAAQ0M,UAAU5E,KAK1C9H,MAHA,YAAgB0B,GACZ,OAAOA,EAAKL,OAAO+N,IAGvB,MAAME,EAASD,EAAQrP,EAAQ0M,UAAU7E,IAMzC,SAASwH,EAAQtG,GACb,MAAO,CAACxG,EAAGgN,IAAOhN,IAAMkB,EAAO6B,IAAMiK,EAAIA,IAAM9L,EAAO6B,IAAM/C,EAAQkB,EAAOjC,CAAG,GAAGkK,EAAInJ,MAAMwG,KAAM2C,EAAI6D,KAEzG,SAAS7D,EAAInJ,GACT,OAAOA,aAAakB,EAAOvD,KAAOqC,EAAQkB,EAAOjC,CAAG,IAAIe,KAL5DvC,KAHA,YAAe0B,GACX,OAAOA,EAAKL,OAAOiO,wBCzpBvB,SAASE,EAAkBC,EAAIC,EAASD,EAAGC,QACvC,MAAM1K,KAAEA,EAAI2K,KAAEA,GAASF,EACvB,IAAKzK,EAAK4K,aACN,OACJ,GAAsB,kBAAXF,EACP,OACJ,MAAMG,EAAQF,EAAKG,MAAMC,SACzB,IAAK,MAAM5M,KAAOuM,EACTG,EAAM1M,IACP6M,EAAgBP,EAAI,qBAAqBtM,MAIrD,SAAS8M,EAAeP,EAAQG,GAC5B,GAAqB,kBAAVH,EACP,OAAQA,EACZ,IAAK,MAAMvM,KAAOuM,EACd,GAAIG,EAAM1M,GACN,OAAO,EACf,OAAO,EA8BX,SAAS+M,EAAkBxP,GACvB,MAAkB,iBAAPA,EACA,GAAGA,IACPA,EAAIsC,QAAQ,KAAM,MAAMA,QAAQ,MAAO,MAGlD,SAASmN,EAAoBzP,GACzB,OAAOA,EAAIsC,QAAQ,MAAO,KAAKA,QAAQ,MAAO,KAalD,SAASoN,GAAmBC,WAAEA,EAAUC,YAAEA,EAAWC,YAAEA,EAAWC,aAAEA,IAChE,MAAO,CAACC,EAAK/F,EAAMC,EAAI1G,KACnB,MAAMhC,OAAaE,IAAPwI,EACND,EACAC,aAAc+F,EAAUxQ,MACnBwK,aAAgBgG,EAAUxQ,KAAOmQ,EAAWI,EAAK/F,EAAMC,GAAM2F,EAAYG,EAAK/F,EAAMC,GAAKA,GAC1FD,aAAgBgG,EAAUxQ,MACrBoQ,EAAYG,EAAK9F,EAAID,GAAOA,GAC7B6F,EAAY7F,EAAMC,GAChC,OAAO1G,IAAWyM,EAAUxQ,MAAU+B,aAAeyO,EAAUxQ,KAAiC+B,EAAzBuO,EAAaC,EAAKxO,IA2BjG,SAAS0O,EAAqBF,EAAKG,GAC/B,IAAW,IAAPA,EACA,OAAOH,EAAI9M,IAAI,SAAS,GAC5B,MAAMkN,EAAQJ,EAAI9M,IAAI,QAAa+M,EAAUlP,CAAG,MAGhD,YAFWW,IAAPyO,GACAE,EAAaL,EAAKI,EAAOD,GACtBC,EAGX,SAASC,EAAaL,EAAKI,EAAOD,GAC9B/Q,OAAOkR,KAAKH,GAAInK,QAASuK,GAAMP,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAGqP,KAAQ,EAAIH,EAAUzJ,aAAa+J,MAAM,IAxI3GnR,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,kBAA0BA,eAAuBA,OAAeA,UAAkBA,eAAuBA,uBAA+BA,iBAAyBA,WAAmBA,sBAA8BA,oBAA4BA,iBAAyBA,mBAA2BA,iBAAyBA,uBAA+BA,iBAAyBA,oBAA4BA,oBAA4BA,cAAiB,EAU5bA,SANA,SAAgB4N,GACZ,MAAMqD,EAAO,GACb,IAAK,MAAM/P,KAAQ0M,EACfqD,EAAK/P,IAAQ,EACjB,OAAO+P,GAWXjR,oBARA,SAA2ByP,EAAIC,GAC3B,MAAqB,kBAAVA,EACAA,EACwB,IAA/B7P,OAAOkR,KAAKrB,GAAQzO,SAExBuO,EAAkBC,EAAIC,IACdO,EAAeP,EAAQD,EAAGE,KAAKG,MAAMoB,OAejDlR,oBAA4BwP,EAS5BxP,iBAAyBiQ,EASzBjQ,uBARA,SAA8B0P,EAAQI,GAClC,GAAqB,kBAAVJ,EACP,OAAQA,EACZ,IAAK,MAAMvM,KAAOuM,EACd,GAAY,SAARvM,GAAkB2M,EAAMoB,IAAI/N,GAC5B,OAAO,EACf,OAAO,GAYXnD,iBATA,UAAwBmR,aAAEA,EAAYC,WAAEA,GAAc1B,EAAQ2B,EAASC,GACnE,IAAKA,EAAO,CACR,GAAqB,iBAAV5B,GAAuC,kBAAVA,EACpC,OAAOA,EACX,GAAqB,iBAAVA,EACP,OAAWgB,EAAUlP,CAAG,GAAGkO,IAEnC,OAAWgB,EAAUlP,CAAG,GAAG2P,IAAeC,KAAa,EAAIV,EAAUzJ,aAAaoK,MAMtFrR,mBAHA,SAA0BU,GACtB,OAAOyP,EAAoBoB,mBAAmB7Q,KAMlDV,iBAHA,SAAwBU,GACpB,OAAO8Q,mBAAmBtB,EAAkBxP,KAQhDV,oBAA4BkQ,EAI5BlQ,sBAA8BmQ,EAU9BnQ,WATA,SAAkByR,EAAIC,GAClB,GAAIlP,MAAMC,QAAQgP,GACd,IAAK,MAAMlP,KAAKkP,EACZC,EAAEnP,QAGNmP,EAAED,IAgBVzR,iBAAyB,CACrB6Q,MAAOT,EAAmB,CACtBC,WAAY,CAACI,EAAK/F,EAAMC,IAAO8F,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGmJ,iBAAkBD,kBAAsB,KAC9F+F,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGkJ,aAAiB,IAAM+F,EAAIhE,OAAO9B,GAAI,GAAO,IAAM8F,EAAIhE,OAAO9B,EAAQ+F,EAAUlP,CAAG,GAAGmJ,WAAY5J,KAAS2P,EAAUlP,CAAG,iBAAiBmJ,MAAOD,SAE/K4F,YAAa,CAACG,EAAK/F,EAAMC,IAAO8F,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGmJ,aAAe,MACzD,IAATD,EACA+F,EAAIhE,OAAO9B,GAAI,IAGf8F,EAAIhE,OAAO9B,EAAQ+F,EAAUlP,CAAG,GAAGmJ,WACnCmG,EAAaL,EAAK9F,EAAID,MAG9B6F,YAAa,CAAC7F,EAAMC,KAAiB,IAATD,GAAuB,IAAKA,KAASC,GACjE6F,aAAcG,IAElBlF,MAAO2E,EAAmB,CACtBC,WAAY,CAACI,EAAK/F,EAAMC,IAAO8F,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGmJ,iBAAkBD,kBAAsB,IAAM+F,EAAIhE,OAAO9B,EAAQ+F,EAAUlP,CAAG,GAAGkJ,uBAA0BC,OAAQD,OAAUC,OAAQD,MAC/L4F,YAAa,CAACG,EAAK/F,EAAMC,IAAO8F,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGmJ,aAAe,IAAM8F,EAAIhE,OAAO9B,GAAa,IAATD,GAA2BgG,EAAUlP,CAAG,GAAGmJ,OAAQD,OAAUC,OAAQD,MACpK6F,YAAa,CAAC7F,EAAMC,KAAiB,IAATD,GAAuBiH,KAAKC,IAAIlH,EAAMC,GAClE6F,aAAc,CAACC,EAAKhF,IAAUgF,EAAI9M,IAAI,QAAS8H,MAWvDzL,uBAA+B2Q,EAI/B3Q,eAAuB8Q,EACvB,MAAMe,EAAW,GAQjB,IAAIC,EAoBJ,SAAS9B,EAAgBP,EAAIsC,EAAKC,EAAOvC,EAAGzK,KAAK4K,cAC7C,GAAKoC,EAAL,CAGA,GADAD,EAAM,gBAAgBA,KACT,IAATC,EACA,MAAM,IAAIxR,MAAMuR,GACpBtC,EAAGE,KAAKsC,OAAOC,KAAKH,IA3BxB/R,UANA,SAAiByQ,EAAKiB,GAClB,OAAOjB,EAAIzE,WAAW,OAAQ,CAC1BxG,IAAKkM,EACL3Q,KAAM8Q,EAASH,EAAE3Q,QAAU8Q,EAASH,EAAE3Q,MAAQ,IAAI0C,EAAO3C,MAAM4Q,EAAE3Q,UAKzE,SAAW+Q,GACPA,EAAKA,EAAU,IAAI,GAAK,MACxBA,EAAKA,EAAU,IAAI,GAAK,MAF5B,CAGGA,EAAO9R,EAAQ8R,OAAS9R,OAAe,KAe1CA,eAdA,SAAsBmS,EAAUC,EAAcC,GAE1C,GAAIF,aAAoBzB,EAAUxQ,KAAM,CACpC,MAAMoS,EAAWF,IAAiBN,EAAKS,IACvC,OAAOF,EACDC,EACQ5B,EAAUlP,CAAG,SAAS2Q,UACtBzB,EAAUlP,CAAG,UAAU2Q,WAC/BG,EACQ5B,EAAUlP,CAAG,SAAS2Q,IACtBzB,EAAUlP,CAAG,SAAS2Q,8CAExC,OAAOE,GAAmB,EAAI3B,EAAUzJ,aAAakL,GAAUxR,WAAa,IAAMuP,EAAkBiC,IAWxGnS,kBAA0BgQ,IC7K1B,MAAMnP,EAAQ,CAEV2R,KAAM,IAAI9B,EAAUxQ,KAAK,QAEzBuS,OAAQ,IAAI/B,EAAUxQ,KAAK,UAC3BwS,aAAc,IAAIhC,EAAUxQ,KAAK,gBACjCyS,WAAY,IAAIjC,EAAUxQ,KAAK,cAC/B0S,mBAAoB,IAAIlC,EAAUxQ,KAAK,sBACvC2S,SAAU,IAAInC,EAAUxQ,KAAK,YAC7B4S,eAAgB,IAAIpC,EAAUxQ,KAAK,kBAEnC6S,QAAS,IAAIrC,EAAUxQ,KAAK,WAC5B8S,OAAQ,IAAItC,EAAUxQ,KAAK,UAC3BO,KAAM,IAAIiQ,EAAUxQ,KAAK,QAEzByP,KAAM,IAAIe,EAAUxQ,KAAK,QACzBiF,MAAO,IAAIuL,EAAUxQ,KAAK,SAE1B+S,KAAM,IAAIvC,EAAUxQ,KAAK,QACzBgT,QAAS,IAAIxC,EAAUxQ,KAAK,WAC5BiT,QAAS,IAAIzC,EAAUxQ,KAAK,WAC5BkT,SAAU,IAAI1C,EAAUxQ,KAAK,aAEjC,kDAAkBW,8CC+BlB,SAASwS,EAAS5C,EAAK6C,GACnB,MAAMC,EAAM9C,EAAIjN,MAAM,MAAO8P,GAC7B7C,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQV,mBAAoB,IAAMtC,EAAIhE,OAAO+G,EAAQC,QAAQV,QAAarC,EAAUlP,CAAG,IAAI+R,MAAa7C,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQV,gBAAgBQ,MAC3L9C,EAAI1P,KAAS2P,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQT,YAEjD,SAASU,EAAajE,EAAIkE,GACtB,MAAMlD,IAAEA,EAAGmD,aAAEA,EAAYC,UAAEA,GAAcpE,EACrCoE,EAAUC,OACVrD,EAAIlC,MAAUmC,EAAUlP,CAAG,OAAOiO,EAAGsE,mBAAmBJ,OAGxDlD,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAGoS,WAAuBD,GACtDlD,EAAIxC,QAAO,IApEnBpO,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,eAAuBA,mBAA2BA,mBAA2BA,cAAsBA,oBAA4BA,oBAAuB,EAItJA,eAAuB,CACnBgU,QAAS,EAAG3C,QAAAA,KAAkBX,EAAUhQ,GAAK,cAAc2Q,yBAE/DrR,oBAA4B,CACxBgU,QAAS,EAAG3C,QAAAA,EAAS4C,WAAAA,KAAiBA,EAC5BvD,EAAUhQ,GAAK,IAAI2Q,sBAA4B4C,YAC/CvD,EAAUhQ,GAAK,IAAI2Q,iCAajCrR,cAXA,SAAqBkU,EAAK9K,EAAQpJ,EAAQmU,aAAcC,EAAYC,GAChE,MAAM5E,GAAEA,GAAOyE,GACTzD,IAAEA,EAAG6D,cAAEA,EAAaC,UAAEA,GAAc9E,EACpC6D,EAASkB,EAAgBN,EAAK9K,EAAOgL,IACvCC,MAAAA,EAA6DA,EAAqBC,GAAiBC,GACnGlB,EAAS5C,EAAK6C,GAGdI,EAAajE,EAAQiB,EAAUlP,CAAG,IAAI8R,OAa9CtT,mBATA,SAA0BkU,EAAK9K,EAAQpJ,EAAQmU,aAAcC,GACzD,MAAM3E,GAAEA,GAAOyE,GACTzD,IAAEA,EAAG6D,cAAEA,EAAaC,UAAEA,GAAc9E,EAE1C4D,EAAS5C,EADM+D,EAAgBN,EAAK9K,EAAOgL,IAErCE,GAAiBC,GACnBb,EAAajE,EAAI+D,EAAQC,QAAQV,UAQzC/S,mBAJA,SAA0ByQ,EAAKgE,GAC3BhE,EAAIhE,OAAO+G,EAAQC,QAAQT,OAAQyB,GACnChE,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQV,mBAAoB,IAAMtC,EAAI5D,GAAG4H,EAAW,IAAMhE,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQV,iBAAkB0B,GAAY,IAAMhE,EAAIhE,OAAO+G,EAAQC,QAAQV,QAAS,SAkBxN/S,eAfA,UAAsByQ,IAAEA,EAAGY,QAAEA,EAAOqD,YAAEA,EAAWlC,KAAEA,EAAIiC,UAAEA,EAAShF,GAAEA,IAEhE,QAAkBtN,IAAdsS,EACA,MAAM,IAAIjU,MAAM,4BACpB,MAAM+S,EAAM9C,EAAInN,KAAK,OACrBmN,EAAI/C,SAAS,IAAK+G,EAAWjB,EAAQC,QAAQT,OAASrR,IAClD8O,EAAIjN,MAAM+P,EAAS7C,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQV,WAAWpR,MAC9D8O,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAG+R,+BAAkC,IAAM9C,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAG+R,kBAAoB,EAAI7C,EAAU1J,WAAWwM,EAAQC,QAAQf,aAAcjD,EAAGkF,aAChLlE,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAG+R,eAAsB7C,EAAUhQ,GAAK,GAAG+O,EAAGmF,iBAAiBvD,KACvF5B,EAAGzK,KAAK6P,UACRpE,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAG+R,WAAcmB,GAC7CjE,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAG+R,SAAYf,OAoBvD,MAAMsC,EAAI,CACNzD,QAAS,IAAIX,EAAUxQ,KAAK,WAC5BkR,WAAY,IAAIV,EAAUxQ,KAAK,cAC/B6U,OAAQ,IAAIrE,EAAUxQ,KAAK,UAC3B8U,aAAc,IAAItE,EAAUxQ,KAAK,gBACjC8T,QAAS,IAAItD,EAAUxQ,KAAK,WAC5BwP,OAAQ,IAAIgB,EAAUxQ,KAAK,UAC3B+U,aAAc,IAAIvE,EAAUxQ,KAAK,iBAErC,SAASsU,EAAgBN,EAAK9K,EAAOgL,GACjC,MAAMc,aAAEA,GAAiBhB,EAAIzE,GAC7B,OAAqB,IAAjByF,EACWxE,EAAUlP,CAAG,KAGhC,SAAqB0S,EAAK9K,EAAOgL,EAAa,IAC1C,MAAM3D,IAAEA,EAAGhB,GAAEA,GAAOyE,EACdtH,EAAY,CACduI,EAAkB1F,EAAI2E,GACtBgB,EAAgBlB,EAAKE,IAGzB,OAeJ,SAAyBF,GAAKa,OAAEA,EAAMf,QAAEA,GAAWpH,GAC/C,MAAMyE,QAAEA,EAAOmB,KAAEA,EAAIkC,YAAEA,EAAWjF,GAAEA,GAAOyE,GACrClP,KAAEA,EAAIgQ,aAAEA,EAAY7D,aAAEA,EAAYC,WAAEA,GAAe3B,EACzD7C,EAAU/K,KAAK,CAACiT,EAAEzD,QAASA,GAAU,CAACyD,EAAEC,OAAyB,mBAAVA,EAAuBA,EAAOb,GAAOa,GAAcrE,EAAUlP,CAAG,OACnHwD,EAAKqQ,UACLzI,EAAU/K,KAAK,CAACiT,EAAEd,QAA2B,mBAAXA,EAAwBA,EAAQE,GAAOF,IAEzEhP,EAAK6P,SACLjI,EAAU/K,KAAK,CAACiT,EAAEpF,OAAQgF,GAAc,CAACI,EAAEG,aAAkBvE,EAAUlP,CAAG,GAAG2P,IAAeC,KAAe,CAACoC,EAAQC,QAAQjB,KAAMA,IAElIwC,GACApI,EAAU/K,KAAK,CAACiT,EAAEE,aAAcA,IA3BpCM,CAAgBpB,EAAK9K,EAAOwD,GACrB6D,EAAI9D,UAAUC,GATd2I,CAAYrB,EAAK9K,EAAOgL,GAWnC,SAASe,GAAkBR,UAAEA,IAAajC,aAAEA,IACxC,MAAM8C,EAAW9C,EACPhC,EAAUhQ,GAAK,GAAGiU,KAAY,EAAIc,EAAOC,cAAchD,EAAc+C,EAAO3D,KAAK6D,OACrFhB,EACN,MAAO,CAACnB,EAAQC,QAAQf,cAAc,EAAIhC,EAAU1J,WAAWwM,EAAQC,QAAQf,aAAc8C,IAEjG,SAASJ,GAAgB/D,QAAEA,EAAS5B,IAAImF,cAAEA,KAAmBxD,WAAEA,EAAU6D,aAAEA,IACvE,IAAIW,EAAUX,EAAeL,EAAoBlE,EAAUhQ,GAAK,GAAGkU,KAAiBvD,IAIpF,OAHID,IACAwE,EAAclF,EAAUhQ,GAAK,GAAGkV,KAAU,EAAIH,EAAOC,cAActE,EAAYqE,EAAO3D,KAAK6D,QAExF,CAACb,EAAE1D,WAAYwE,wBC1G1B/V,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,oBAA4BA,4BAA+B,EAI3D,MAAM6V,EAAY,CACd7B,QAAS,2BA2Bb,SAAS8B,EAAiBrG,EAAI4E,GAC1B,MAAM5D,IAAEA,EAAG+B,KAAEA,GAAS/C,GAYtB,EAAIsG,EAASC,aAVD,CACRvF,IAAAA,EACAY,QAAS,eACTmB,KAAAA,EACA9C,QAAQ,EACRuG,YAAY,EACZvB,aAAa,EACbK,OAAQ,GACRtF,GAAAA,GAE2BoG,OAAW1T,EAAWkS,GAzBzDrU,uBAbA,SAA8ByP,GAC1B,MAAMgB,IAAEA,EAAGf,OAAEA,EAAMkE,aAAEA,GAAiBnE,GACvB,IAAXC,EACAoG,EAAiBrG,GAAI,GAEC,iBAAVC,IAAwC,IAAlBA,EAAOoE,OACzCrD,EAAIxC,OAAOuF,EAAQC,QAAQjB,OAG3B/B,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAGoS,WAAuB,MACtDnD,EAAIxC,QAAO,KAcnBjO,oBAVA,SAA2ByP,EAAIyG,GAC3B,MAAMzF,IAAEA,EAAGf,OAAEA,GAAWD,GACT,IAAXC,GACAe,EAAI9M,IAAIuS,GAAO,GACfJ,EAAiBrG,IAGjBgB,EAAI9M,IAAIuS,GAAO,wBC7BvBrW,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,WAAmBA,kBAAqB,EACxC,MACMmW,EAAY,IAAIjK,IADH,CAAC,SAAU,SAAU,UAAW,UAAW,OAAQ,SAAU,UAKhFlM,aAHA,SAAoBuC,GAChB,MAAmB,iBAALA,GAAiB4T,EAAU3R,IAAIjC,IAkBjDvC,WAfA,WACI,MAAMoW,EAAS,CACXC,OAAQ,CAAEC,KAAM,SAAUzG,MAAO,IACjC0G,OAAQ,CAAED,KAAM,SAAUzG,MAAO,IACjC2G,MAAO,CAAEF,KAAM,QAASzG,MAAO,IAC/BlD,OAAQ,CAAE2J,KAAM,SAAUzG,MAAO,KAErC,MAAO,CACH4G,MAAO,IAAKL,EAAQM,SAAS,EAAMC,SAAS,EAAMC,MAAM,GACxD/G,MAAO,CAAC,CAAEA,MAAO,IAAMuG,EAAOC,OAAQD,EAAOG,OAAQH,EAAOI,MAAOJ,EAAOzJ,QAC1EkK,KAAM,CAAEhH,MAAO,IACfqB,IAAK,GACLnB,SAAU,yBCblB,SAAS+G,EAAepH,EAAQqH,GAC5B,OAAOA,EAAMlH,MAAMrE,KAAMwL,GAASC,EAAcvH,EAAQsH,IAG5D,SAASC,EAAcvH,EAAQsH,GAC3B,IAAI7V,EACJ,YAAiCgB,IAAzBuN,EAAOsH,EAAK3F,WACuB,QAArClQ,EAAK6V,EAAKE,WAAWC,kBAA+B,IAAPhW,OAAgB,EAASA,EAAGqK,KAAM4L,QAAwBjV,IAAhBuN,EAAO0H,KAdxGvX,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,gBAAwBA,iBAAyBA,6BAAgC,EAKjFA,wBAJA,UAA+B0P,OAAEA,EAAMC,KAAEA,GAAQ2G,GAC7C,MAAMS,EAAQpH,EAAKG,MAAM2G,MAAMH,GAC/B,OAAOS,IAAmB,IAAVA,GAAkBD,EAAepH,EAAQqH,IAM7D/W,iBAAyB8W,EAMzB9W,gBAAwBiX,sBCTxB,IAAII,EAsBJ,SAASC,EAAaC,GAClB,MAAMd,EAAQjU,MAAMC,QAAQ8U,GAAMA,EAAKA,EAAK,CAACA,GAAM,GACnD,GAAId,EAAMe,MAAMC,EAAQC,YACpB,OAAOjB,EACX,MAAM,IAAIjW,MAAM,wCAA0CiW,EAAM/T,KAAK,MAjCzE7C,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,kBAA0BA,iBAAyBA,gBAAwBA,yBAAiCA,eAAuBA,iBAAyBA,gBAAmB,EAO/K,SAAWqX,GACPA,EAASA,EAAkB,QAAI,GAAK,UACpCA,EAASA,EAAgB,MAAI,GAAK,QAFtC,CAGGA,EAAWrX,EAAQqX,WAAarX,WAAmB,KAiBtDA,iBAhBA,SAAwB0P,GACpB,MAAM+G,EAAQa,EAAa5H,EAAO4G,MAElC,GADgBG,EAAMkB,SAAS,SAE3B,IAAwB,IAApBjI,EAAOkI,SACP,MAAM,IAAIpX,MAAM,8CAEnB,CACD,IAAKiW,EAAMxV,aAA8BkB,IAApBuN,EAAOkI,SACxB,MAAM,IAAIpX,MAAM,6CAEI,IAApBkP,EAAOkI,UACPnB,EAAM5U,KAAK,QAEnB,OAAO4U,GASXzW,eAAuBsX,EAiBvBtX,yBAhBA,SAAgCyP,EAAIgH,GAChC,MAAMhG,IAAEA,EAAG+B,KAAEA,EAAIxN,KAAEA,GAASyK,EACtBoI,EAgBV,SAAuBpB,EAAOqB,GAC1B,OAAOA,EACDrB,EAAMsB,OAAQC,GAAMC,EAAUzT,IAAIwT,IAAuB,UAAhBF,GAAiC,UAANE,GACpE,GAnBWE,CAAczB,EAAOzR,EAAK8S,aACrCK,EAAa1B,EAAMxV,OAAS,KACR,IAApB4W,EAAS5W,QAAiC,IAAjBwV,EAAMxV,SAAgB,EAAImX,EAAgBC,uBAAuB5I,EAAIgH,EAAM,KAC1G,GAAI0B,EAAY,CACZ,MAAMG,EAAYC,EAAe9B,EAAOjE,EAAMxN,EAAKwT,cAAenB,EAASoB,OAC3EhI,EAAI5D,GAAGyL,EAAW,KACVT,EAAS5W,OAezB,SAAoBwO,EAAIgH,EAAOoB,GAC3B,MAAMpH,IAAEA,EAAG+B,KAAEA,EAAIxN,KAAEA,GAASyK,EACtBiJ,EAAWjI,EAAI/M,IAAI,WAAgBgN,EAAUlP,CAAG,UAAUgR,KAC1DmG,EAAUlI,EAAI/M,IAAI,UAAegN,EAAUlP,CAAG,aAC3B,UAArBwD,EAAK8S,aACLrH,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGkX,kCAAyClG,SAAYA,gBAAoB,IAAM/B,EACrGhE,OAAO+F,EAAU9B,EAAUlP,CAAG,GAAGgR,QACjC/F,OAAOiM,EAAchI,EAAUlP,CAAG,UAAUgR,KAC5C3F,GAAG0L,EAAe9B,EAAOjE,EAAMxN,EAAKwT,eAAgB,IAAM/H,EAAIhE,OAAOkM,EAASnG,KAEvF/B,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGmX,mBAC3B,IAAK,MAAMX,KAAKH,GACRI,EAAUzT,IAAIwT,IAAa,UAANA,GAAsC,UAArBhT,EAAK8S,cAC3Cc,EAAmBZ,GAU3B,SAASY,EAAmBZ,GACxB,OAAQA,GACJ,IAAK,SAMD,YALAvH,EACKvD,OAAWwD,EAAUlP,CAAG,GAAGkX,oBAA2BA,kBACtDjM,OAAOkM,EAAajI,EAAUlP,CAAG,QAAQgR,KACzCtF,OAAWwD,EAAUlP,CAAG,GAAGgR,cAC3B/F,OAAOkM,EAAajI,EAAUlP,CAAG,MAE1C,IAAK,SAKD,YAJAiP,EACKvD,OAAWwD,EAAUlP,CAAG,GAAGkX,qBAA4BlG;oBACxDkG,oBAA2BlG,QAAWA,SAAYA,MACjD/F,OAAOkM,EAAajI,EAAUlP,CAAG,IAAIgR,KAE9C,IAAK,UAKD,YAJA/B,EACKvD,OAAWwD,EAAUlP,CAAG,GAAGkX,sBAA6BlG;oBACzDkG,qBAA4BlG,QAAWA,SAAYA,UAAaA,WAC/D/F,OAAOkM,EAAajI,EAAUlP,CAAG,IAAIgR,KAE9C,IAAK,UAMD,YALA/B,EACKvD,OAAWwD,EAAUlP,CAAG,GAAGgR,oBAAuBA,cAAiBA,cACnE/F,OAAOkM,GAAS,GAChBzL,OAAWwD,EAAUlP,CAAG,GAAGgR,mBAAsBA,WACjD/F,OAAOkM,GAAS,GAEzB,IAAK,OAGD,OAFAlI,EAAIvD,OAAWwD,EAAUlP,CAAG,GAAGgR,eAAkBA,cAAiBA,oBAClE/B,EAAIhE,OAAOkM,EAAS,MAExB,IAAK,QACDlI,EACKvD,OAAWwD,EAAUlP,CAAG,GAAGkX,qBAA4BA;mBACzDA,sBAA6BlG,cAC3B/F,OAAOkM,EAAajI,EAAUlP,CAAG,IAAIgR,OA3CtD/B,EAAIxG,OACJ4O,EAAgBpJ,GAChBgB,EAAIxD,QACJwD,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGmX,kBAAyB,KAChDlI,EAAIhE,OAAO+F,EAAMmG,GA2CzB,UAA0BlI,IAAEA,EAAGkC,WAAEA,EAAUC,mBAAEA,GAAsB7Q,GAE/D0O,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGmR,kBAA4B,IAAMlC,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAGmR,KAAcC,KAAuB7Q,IA5C7H+W,CAAiBrJ,EAAIkJ,KAnCbI,CAAWtJ,EAAIgH,EAAOoB,GAEtBgB,EAAgBpJ,KAG5B,OAAO0I,GAGX,MAAMF,EAAY,IAAI/L,IAAI,CAAC,SAAU,SAAU,UAAW,UAAW,SAyErE,SAAS8M,EAAcN,EAAUlG,EAAMyG,EAAYC,EAAU7B,EAAS8B,SAClE,MAAMzR,EAAKwR,IAAY7B,EAAS8B,QAAUzI,EAAUhE,UAAUhF,GAAKgJ,EAAUhE,UAAU/E,IACvF,IAAIuC,EACJ,OAAQwO,GACJ,IAAK,OACD,OAAWhI,EAAUlP,CAAG,GAAGgR,KAAQ9K,SACvC,IAAK,QACDwC,EAAWwG,EAAUlP,CAAG,iBAAiBgR,KACzC,MACJ,IAAK,SACDtI,EAAWwG,EAAUlP,CAAG,GAAGgR,eAAkBA,mCAAsCA,KACnF,MACJ,IAAK,UACDtI,EAAOkP,EAAY1I,EAAUlP,CAAG,KAAKgR,oBAAuBA,MAC5D,MACJ,IAAK,SACDtI,EAAOkP,IACP,MACJ,QACI,OAAW1I,EAAUlP,CAAG,UAAUgR,KAAQ9K,KAAMgR,IAExD,OAAOQ,IAAY7B,EAAS8B,QAAUjP,GAAO,EAAIwG,EAAUrG,KAAKH,GAChE,SAASkP,EAAQC,EAAQ3I,EAAUpL,KAC/B,OAAO,EAAIoL,EAAU4I,KAAS5I,EAAUlP,CAAG,UAAUgR,gBAAoB6G,EAAOJ,EAAiBvI,EAAUlP,CAAG,YAAYgR,KAAU9B,EAAUpL,MAItJ,SAASiT,EAAegB,EAAW/G,EAAMyG,EAAYC,GACjD,GAAyB,IAArBK,EAAUtY,OACV,OAAO+X,EAAcO,EAAU,GAAI/G,EAAMyG,EAAYC,GAEzD,IAAIhP,EACJ,MAAMuM,GAAQ,EAAIhB,EAAO+D,QAAQD,GACjC,GAAI9C,EAAMD,OAASC,EAAM9J,OAAQ,CAC7B,MAAM8M,EAAa/I,EAAUlP,CAAG,UAAUgR,gBAC1CtI,EAAOuM,EAAMG,KAAO6C,EAAa/I,EAAUlP,CAAG,IAAIgR,QAAWiH,WACtDhD,EAAMG,YACNH,EAAMD,aACNC,EAAM9J,YAGbzC,EAAOwG,EAAUpL,IAEjBmR,EAAMJ,eACCI,EAAMC,QACjB,IAAK,MAAMsB,KAAKvB,EACZvM,GAAO,EAAIwG,EAAU4I,KAAKpP,EAAM8O,EAAchB,EAAGxF,EAAMyG,EAAYC,IACvE,OAAOhP,EArBXlK,gBAAwBgZ,EAuBxBhZ,iBAAyBuY,EACzB,MAAMmB,EAAY,CACd1F,QAAS,EAAGtE,OAAAA,KAAa,WAAWA,IACpCqF,OAAQ,EAAGrF,OAAAA,EAAQgF,YAAAA,KAAmC,iBAAVhF,EAAyBgB,EAAUlP,CAAG,UAAUkO,KAAgBgB,EAAUlP,CAAG,UAAUkT,MAEvI,SAASmE,EAAgBpJ,GACrB,MAAMyE,EAIV,SAA6BzE,GACzB,MAAMgB,IAAEA,EAAG+B,KAAEA,EAAI9C,OAAEA,GAAWD,EACxBwG,GAAa,EAAIR,EAAOkE,gBAAgBlK,EAAIC,EAAQ,QAC1D,MAAO,CACHe,IAAAA,EACAY,QAAS,OACTmB,KAAAA,EACA9C,OAAQA,EAAO4G,KACfL,WAAAA,EACAvB,YAAauB,EACbhB,aAAcvF,EACdqF,OAAQ,GACRtF,GAAAA,GAhBQmK,CAAoBnK,IAChC,EAAIsG,EAASC,aAAa9B,EAAKwF,GAEnC1Z,kBAA0B6Y,sBCxK1B,SAASgB,EAAcpK,EAAIqK,EAAMC,GAC7B,MAAMtJ,IAAEA,EAAG6D,cAAEA,EAAa9B,KAAEA,EAAIxN,KAAEA,GAASyK,EAC3C,QAAqBtN,IAAjB4X,EACA,OACJ,MAAMC,EAAgBtJ,EAAUlP,CAAG,GAAGgR,KAAO,EAAI9B,EAAUzJ,aAAa6S,KACxE,GAAIxF,EAEA,YADA,EAAImB,EAAOzF,iBAAiBP,EAAI,2BAA2BuK,KAG/D,IAAIhQ,EAAgB0G,EAAUlP,CAAG,GAAGwY,kBACX,UAArBhV,EAAKiV,cACLjQ,EAAgB0G,EAAUlP,CAAG,GAAGwI,QAAgBgQ,iBAAyBA,YAI7EvJ,EAAI5D,GAAG7C,EAAe0G,EAAUlP,CAAG,GAAGwY,QAAe,EAAItJ,EAAU3N,WAAWgX,MA/BlFla,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,sBAAyB,EAczBA,iBAXA,SAAwByP,EAAIyK,GACxB,MAAMC,WAAEA,EAAU1O,MAAEA,GAAUgE,EAAGC,OACjC,GAAW,WAAPwK,GAAmBC,EACnB,IAAK,MAAMhX,KAAOgX,EACdN,EAAcpK,EAAItM,EAAKgX,EAAWhX,GAAKsQ,aAG/B,UAAPyG,GAAkB1X,MAAMC,QAAQgJ,IACrCA,EAAMhF,QAAQ,CAAC2T,EAAKzY,IAAMkY,EAAcpK,EAAI9N,EAAGyY,EAAI3G,+BCZ3D5T,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,gBAAwBA,gBAAwBA,aAAqBA,mBAA2BA,mBAA2BA,sBAA8BA,mBAA2BA,iBAAyBA,gBAAwBA,cAAsBA,oBAA4BA,mBAA2BA,8BAAiC,EAInV,MAAMqa,EAAS5E,EAkBf,SAAS6E,EAAY7J,GACjB,OAAOA,EAAIzE,WAAW,OAAQ,CAE1BxG,IAAK3F,OAAO0a,UAAUC,eACtBzZ,KAAU2P,EAAUlP,CAAG,oCAI/B,SAASiZ,EAAchK,EAAK+B,EAAM5N,GAC9B,OAAW8L,EAAUlP,CAAG,GAAG8Y,EAAY7J,WAAa+B,MAAS5N,KAQjE,SAAS8V,EAAiBjK,EAAK+B,EAAM5N,EAAUmJ,GAC3C,MAAM7D,EAAWwG,EAAUlP,CAAG,GAAGgR,KAAO,EAAI9B,EAAUzJ,aAAarC,mBACnE,OAAOmJ,GAAgB,EAAI2C,EAAUiK,IAAIzQ,GAAM,EAAIwG,EAAUrG,KAAKoQ,EAAchK,EAAK+B,EAAM5N,KAAcsF,EAG7G,SAAS0Q,EAAoBC,GACzB,OAAOA,EAAYhb,OAAOkR,KAAK8J,GAAW9C,OAAQ/G,GAAY,cAANA,GAAqB,GAjCjFhR,yBAPA,SAAgCkU,EAAK4F,GACjC,MAAMrJ,IAAEA,EAAG+B,KAAEA,EAAI/C,GAAEA,GAAOyE,EAC1BzD,EAAI5D,GAAG6N,EAAiBjK,EAAK+B,EAAMsH,EAAMrK,EAAGzK,KAAK+I,eAAgB,KAC7DmG,EAAI4G,UAAU,CAAEC,gBAAqBrK,EAAUlP,CAAG,GAAGsY,MAAU,GAC/D5F,EAAI9K,WAOZpJ,mBAHA,UAA0ByQ,IAAEA,EAAG+B,KAAEA,EAAM/C,IAAIzK,KAAEA,IAAUmV,EAAYa,GAC/D,OAAO,EAAItK,EAAUiK,OAAOR,EAAWc,IAAKnB,IAAS,EAAIpJ,EAAU4I,KAAKoB,EAAiBjK,EAAK+B,EAAMsH,EAAM9U,EAAK+I,eAAoB2C,EAAUlP,CAAG,GAAGwZ,OAAalB,QAOpK9Z,oBAJA,SAA2BkU,EAAK8G,GAC5B9G,EAAI4G,UAAU,CAAEC,gBAAiBC,IAAW,GAC5C9G,EAAI9K,SAURpJ,cAAsBsa,EAItBta,gBAAwBya,EAKxBza,iBAJA,SAAwByQ,EAAK+B,EAAM5N,EAAUmJ,GACzC,MAAM7D,EAAWwG,EAAUlP,CAAG,GAAGgR,KAAO,EAAI9B,EAAUzJ,aAAarC,mBACnE,OAAOmJ,EAAoB2C,EAAUlP,CAAG,GAAG0I,QAAWuQ,EAAchK,EAAK+B,EAAM5N,KAAcsF,GAOjGlK,mBAA2B0a,EAI3B1a,sBAA8B4a,EAI9B5a,mBAHA,SAA0ByP,EAAIoL,GAC1B,OAAOD,EAAoBC,GAAW9C,OAAQ/G,KAAO,EAAIyE,EAAOyF,mBAAmBzL,EAAIoL,EAAU7J,MAgBrGhR,mBAbA,UAA0BiW,WAAEA,EAAUzD,KAAEA,EAAM/C,IAAIgB,IAAEA,EAAGU,aAAEA,EAAYC,WAAEA,EAAUuD,UAAEA,GAAWlF,GAAEA,GAAMV,EAAMoM,EAASC,GACjH,MAAMC,EAAgBD,EAAiB1K,EAAUlP,CAAG,GAAGyU,MAAezD,MAASrB,IAAeC,IAAeoB,EACvGC,EAAS,CACX,CAACe,EAAQC,QAAQf,cAAc,EAAIhC,EAAU1J,WAAWwM,EAAQC,QAAQf,aAAciC,IACtF,CAACnB,EAAQC,QAAQd,WAAYlD,EAAGkD,YAChC,CAACa,EAAQC,QAAQb,mBAAoBnD,EAAGmD,oBACxC,CAACY,EAAQC,QAAQZ,SAAUW,EAAQC,QAAQZ,WAE3CpD,EAAGzK,KAAKsW,YACR7I,EAAO5Q,KAAK,CAAC2R,EAAQC,QAAQX,eAAgBU,EAAQC,QAAQX,iBACjE,MAAMpR,EAAWgP,EAAUlP,CAAG,GAAG6Z,MAAkB5K,EAAI9D,UAAU8F,KACjE,OAAO0I,IAAYzK,EAAUpL,IAAUoL,EAAUlP,CAAG,GAAGuN,UAAaoM,MAAYzZ,KAAcgP,EAAUlP,CAAG,GAAGuN,KAAQrN,MAG1H,MAAM6Z,EAAgB7K,EAAUlP,CAAG,aAWnCxB,aAVA,UAAoByQ,IAAEA,EAAKhB,IAAIzK,KAAEA,IAAUwW,GACvC,MAAMC,EAAIzW,EAAK0W,cAAgB,IAAM,IAC/BC,OAAEA,GAAW3W,EAAKjE,KAClBqC,EAAKuY,EAAOH,EAASC,GAC3B,OAAOhL,EAAIzE,WAAW,UAAW,CAC7B7I,IAAKC,EAAGzC,WACR6E,IAAKpC,EACLrC,KAAU2P,EAAUlP,CAAG,GAAmB,eAAhBma,EAAO5a,KAAwBwa,GAAY,EAAIlB,EAAOuB,SAASnL,EAAKkL,MAAWH,MAAYC,QA2B7Hzb,gBAvBA,SAAuBkU,GACnB,MAAMzD,IAAEA,EAAG+B,KAAEA,EAAInB,QAAEA,EAAO5B,GAAEA,GAAOyE,EAC7BgC,EAAQzF,EAAInN,KAAK,SACvB,GAAImM,EAAG8E,UAAW,CACd,MAAMsH,EAAWpL,EAAI/M,IAAI,SAAS,GAElC,OADAoY,EAAc,IAAMrL,EAAIhE,OAAOoP,GAAU,IAClCA,EAIX,OAFApL,EAAI9M,IAAIuS,GAAO,GACf4F,EAAc,IAAMrL,EAAIzC,SACjBkI,EACP,SAAS4F,EAAcC,GACnB,MAAMnN,EAAM6B,EAAIjN,MAAM,MAAWkN,EAAUlP,CAAG,GAAGgR,YACjD/B,EAAI/C,SAAS,IAAK,EAAGkB,EAAMjN,IACvBuS,EAAI8H,UAAU,CACV3K,QAAAA,EACAc,SAAUxQ,EACVyQ,aAAcqD,EAAO3D,KAAKS,KAC3B2D,GACHzF,EAAI5D,IAAG,EAAI6D,EAAUrG,KAAK6L,GAAQ6F,OA8B9C/b,gBAzBA,SAAuBkU,GACnB,MAAMzD,IAAEA,EAAGf,OAAEA,EAAM2B,QAAEA,EAAO5B,GAAEA,GAAOyE,EAErC,IAAK1R,MAAMC,QAAQiN,GACf,MAAM,IAAIlP,MAAM,4BAEpB,GADoBkP,EAAOlE,KAAM4O,IAAQ,EAAI3E,EAAOyF,mBAAmBzL,EAAI2K,MACvD3K,EAAGzK,KAAKiX,YACxB,OACJ,MAAM/F,EAAQzF,EAAI/M,IAAI,SAAS,GACzBwY,EAAWzL,EAAInN,KAAK,UAC1BmN,EAAIjC,MAAM,IAAMkB,EAAOjJ,QAAQ,CAAC0V,EAAMxa,KAClC,MAAMya,EAASlI,EAAI8H,UAAU,CACzB3K,QAAAA,EACAgL,WAAY1a,EACZ2S,eAAe,GAChB4H,GACHzL,EAAIhE,OAAOyJ,EAAWxF,EAAUlP,CAAG,GAAG0U,QAAYgG,KACnChI,EAAIoI,oBAAoBF,EAAQF,IAI3CzL,EAAI5D,IAAG,EAAI6D,EAAUrG,KAAK6L,OAElChC,EAAIqI,OAAOrG,EAAO,IAAMhC,EAAIsI,QAAS,IAAMtI,EAAI9K,OAAM,yBC1DzD,SAASqT,EAAWvI,GAChB,MAAMzD,IAAEA,EAAG+B,KAAEA,EAAI/C,GAAEA,GAAOyE,EAC1BzD,EAAI5D,GAAG4C,EAAGkD,WAAY,IAAMlC,EAAIhE,OAAO+F,EAAU9B,EAAUlP,CAAG,GAAGiO,EAAGkD,cAAclD,EAAGmD,wBAezF,SAAS8J,EAAWjM,EAAKY,EAASkL,GAC9B,QAAepa,IAAXoa,EACA,MAAM,IAAI/b,MAAM,YAAY6Q,wBAChC,OAAOZ,EAAIzE,WAAW,UAA4B,mBAAVuQ,EAAuB,CAAE/W,IAAK+W,GAAW,CAAE/W,IAAK+W,EAAQxb,MAAM,EAAI2P,EAAU3N,WAAWwZ,KAxFnI1c,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,uBAA+BA,kBAA0BA,kBAA0BA,wBAA2B,EAqB9GA,mBAhBA,SAA0BkU,EAAKyI,GAC3B,MAAMlM,IAAEA,EAAGY,QAAEA,EAAO3B,OAAEA,EAAMuF,aAAEA,EAAYxF,GAAEA,GAAOyE,EAC7C0I,EAAcD,EAAIE,MAAMC,KAAKrN,EAAGE,KAAMD,EAAQuF,EAAcxF,GAC5DsN,EAAYL,EAAWjM,EAAKY,EAASuL,IACZ,IAA3BnN,EAAGzK,KAAKgY,gBACRvN,EAAGE,KAAKqN,eAAeJ,GAAa,GACxC,MAAM1G,EAAQzF,EAAInN,KAAK,SACvB4Q,EAAI8H,UAAU,CACVtM,OAAQkN,EACRxL,WAAYV,EAAUpL,IACtBsP,cAAe,GAAGnF,EAAGmF,iBAAiBvD,IACtCF,aAAc4L,EACdzI,eAAe,GAChB4B,GACHhC,EAAI+I,KAAK/G,EAAO,IAAMhC,EAAI9K,OAAM,KA+CpCpJ,kBA5CA,SAAyBkU,EAAKyI,GAC1B,IAAIxb,EACJ,MAAMsP,IAAEA,EAAGY,QAAEA,EAAO3B,OAAEA,EAAMuF,aAAEA,EAAY3D,MAAEA,EAAK7B,GAAEA,GAAOyE,GAwD9D,UAA2BL,UAAEA,GAAa8I,GACtC,GAAIA,EAAI3R,QAAU6I,EAAUC,OACxB,MAAM,IAAItT,MAAM,gCAzDpB0c,CAAkBzN,EAAIkN,GACtB,MAAMQ,GAAY7L,GAASqL,EAAIS,QAAUT,EAAIS,QAAQN,KAAKrN,EAAGE,KAAMD,EAAQuF,EAAcxF,GAAMkN,EAAIQ,SAC7FE,EAAcX,EAAWjM,EAAKY,EAAS8L,GACvCjH,EAAQzF,EAAI/M,IAAI,SA4BtB,SAAS4Z,EAAYC,GAASZ,EAAI3R,MAAY0F,EAAUlP,CAAG,SAAWkP,EAAUpL,MAG5EmL,EAAIhE,OAAOyJ,EAAWxF,EAAUlP,CAAG,GAAG+b,KAAS,EAAI9Z,EAAO+Z,kBAAkBtJ,EAAKmJ,EAFjE5N,EAAGzK,KAAKyY,YAAcjK,EAAQC,QAAQhT,KAAO+S,EAAQC,QAAQ9D,OACvD,YAAagN,IAAQrL,IAAyB,IAAfqL,EAAIjN,WAC6DiN,EAAIe,WAE9H,SAASC,EAAW3K,GAChB,IAAI7R,EACJsP,EAAI5D,IAAG,EAAI6D,EAAUrG,KAA0B,QAApBlJ,EAAKwb,EAAIzG,aAA0B,IAAP/U,EAAgBA,EAAK+U,GAAQlD,GAlCxFkB,EAAI0J,WAAW1H,EAEf,WACI,IAAmB,IAAfyG,EAAI3J,OACJsK,IACIX,EAAIe,WACJjB,EAAWvI,GACfyJ,EAAW,IAAMzJ,EAAI9K,aAEpB,CACD,MAAMyU,EAAWlB,EAAI3R,MAM7B,WACI,MAAM6S,EAAWpN,EAAI/M,IAAI,WAAY,MAErC,OADA+M,EAAIvC,IAAI,IAAMoP,EAAgB5M,EAAUlP,CAAG,UAAY2I,GAAMsG,EAAIhE,OAAOyJ,GAAO,GAAOrJ,GAAO6D,EAAUlP,CAAG,GAAG2I,gBAAgBsF,EAAGsE,kBAAmB,IAAMtD,EAAIhE,OAAOoR,EAAcnN,EAAUlP,CAAG,GAAG2I,YAAa,IAAMsG,EAAIlC,MAAMpE,KACxN0T,EAT0BC,GAWrC,WACI,MAAMC,EAAmBrN,EAAUlP,CAAG,GAAG6b,WAGzC,OAFA5M,EAAIhE,OAAOsR,EAAc,MACzBT,EAAY5M,EAAUpL,KACfyY,EAf4CC,GAC3CrB,EAAIe,WACJjB,EAAWvI,GACfyJ,EAAW,IA6BvB,SAAiBzJ,EAAKP,GAClB,MAAMlD,IAAEA,GAAQyD,EAChBzD,EAAI5D,GAAO6D,EAAUlP,CAAG,iBAAiBmS,KAAS,KAC9ClD,EACKhE,OAAO+G,EAAQC,QAAQV,QAAarC,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQV,sBAAsBY,OAAUH,EAAQC,QAAQV,kBAAkBY,MACtIlH,OAAO+G,EAAQC,QAAQT,OAAYtC,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQV,mBACxE,EAAIgD,EAASkI,cAAc/J,IAC5B,IAAMA,EAAI9K,SApCY8U,CAAQhK,EAAK2J,OAZtC3J,EAAIiK,GAAwB,QAApBhd,EAAKwb,EAAIzG,aAA0B,IAAP/U,EAAgBA,EAAK+U,IAoE7DlW,kBATA,SAAyB0P,EAAQuE,EAAYmK,GAAiB,GAE1D,OAASnK,EAAWhT,QAChBgT,EAAWzI,KAAM6S,GAAc,UAAPA,EAClB7b,MAAMC,QAAQiN,GACP,WAAP2O,EACI3O,GAA2B,iBAAVA,IAAuBlN,MAAMC,QAAQiN,UAC/CA,GAAU2O,GAAOD,QAAmC,IAAV1O,IAwBnE1P,uBArBA,UAA8B0P,OAAEA,EAAM1K,KAAEA,EAAI2K,KAAEA,EAAIiF,cAAEA,GAAiB+H,EAAKtL,GAEtE,GAAI7O,MAAMC,QAAQka,EAAItL,UAAYsL,EAAItL,QAAQsG,SAAStG,GAAWsL,EAAItL,UAAYA,EAC9E,MAAM,IAAI7Q,MAAM,4BAEpB,MAAM8d,EAAO3B,EAAI4B,aACjB,GAAID,MAAAA,OAAmC,EAASA,EAAK9S,KAAM4L,IAASvX,OAAO0a,UAAUC,eAAesC,KAAKpN,EAAQ0H,IAC7G,MAAM,IAAI5W,MAAM,2CAA2C6Q,MAAYiN,EAAK5b,KAAK,QAErF,GAAIia,EAAIK,iBACUL,EAAIK,eAAetN,EAAO2B,IAC5B,CACR,MAAMU,EAAM,YAAYV,gCAAsCuD,OAC1DjF,EAAK6O,WAAW7B,EAAIK,eAAehK,QACvC,GAA4B,QAAxBhO,EAAKgY,eAGL,MAAM,IAAIxc,MAAMuR,GAFhBpC,EAAKsC,OAAO7I,MAAM2I,yBCnHlClS,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,sBAA8BA,sBAA8BA,oBAAuB,EAkCnFA,eA/BA,SAAsByP,GAAI4B,QAAEA,EAAOgL,WAAEA,EAAU3M,OAAEA,EAAM0B,WAAEA,EAAUwD,cAAEA,EAAazD,aAAEA,IAChF,QAAgBhP,IAAZkP,QAAoClP,IAAXuN,EACzB,MAAM,IAAIlP,MAAM,wDAEpB,QAAgB2B,IAAZkP,EAAuB,CACvB,MAAM+I,EAAM3K,EAAGC,OAAO2B,GACtB,YAAsBlP,IAAfka,EACD,CACE3M,OAAQ0K,EACRhJ,WAAgBV,EAAUlP,CAAG,GAAGiO,EAAG2B,cAAa,EAAIV,EAAUzJ,aAAaoK,KAC3EuD,cAAe,GAAGnF,EAAGmF,iBAAiBvD,KAExC,CACE3B,OAAQ0K,EAAIiC,GACZjL,WAAgBV,EAAUlP,CAAG,GAAGiO,EAAG2B,cAAa,EAAIV,EAAUzJ,aAAaoK,MAAW,EAAIX,EAAUzJ,aAAaoV,KACjHzH,cAAe,GAAGnF,EAAGmF,iBAAiBvD,MAAW,EAAIoE,EAAOgJ,gBAAgBpC,MAGxF,QAAela,IAAXuN,EAAsB,CACtB,QAAmBvN,IAAfiP,QAA8CjP,IAAlByS,QAAgDzS,IAAjBgP,EAC3D,MAAM,IAAI3Q,MAAM,+EAEpB,MAAO,CACHkP,OAAAA,EACA0B,WAAAA,EACAD,aAAAA,EACAyD,cAAAA,GAGR,MAAM,IAAIpU,MAAM,gDAkCpBR,sBA/BA,SAA6Bgc,EAAWvM,GAAI0C,SAAEA,EAAUC,aAAcsM,EAAMlM,KAAEA,EAAI+G,UAAEA,EAASvE,aAAEA,IAC3F,QAAa7S,IAATqQ,QAAmCrQ,IAAbgQ,EACtB,MAAM,IAAI3R,MAAM,uDAEpB,MAAMiQ,IAAEA,GAAQhB,EAChB,QAAiBtN,IAAbgQ,EAAwB,CACxB,MAAMwC,UAAEA,EAASgK,YAAEA,EAAW3Z,KAAEA,GAASyK,EAEzCmP,EADiBnO,EAAI/M,IAAI,OAAYgN,EAAUlP,CAAG,GAAGiO,EAAG+C,QAAO,EAAI9B,EAAUzJ,aAAakL,MAAa,IAEvG6J,EAAUrH,UAAgBjE,EAAUhQ,GAAK,GAAGiU,KAAY,EAAIc,EAAOC,cAAcvD,EAAUuM,EAAQ1Z,EAAKqN,oBACxG2J,EAAUpJ,mBAAyBlC,EAAUlP,CAAG,GAAG2Q,IACnD6J,EAAU2C,YAAc,IAAIA,EAAa3C,EAAUpJ,oBAWvD,SAASgM,EAAiBC,GACtB7C,EAAUxJ,KAAOqM,EACjB7C,EAAU8C,UAAYrP,EAAGqP,UAAY,EACrC9C,EAAUzC,UAAY,GACtB9J,EAAGsP,kBAAoB,IAAI7S,IAC3B8P,EAAUrJ,WAAalD,EAAG+C,KAC1BwJ,EAAUgD,UAAY,IAAIvP,EAAGuP,UAAWH,QAf/B1c,IAATqQ,IAEAoM,EADiBpM,aAAgB9B,EAAUxQ,KAAOsS,EAAO/B,EAAI/M,IAAI,OAAQ8O,GAAM,SAE1DrQ,IAAjB6S,IACAgH,EAAUhH,aAAeA,IAG7BuE,IACAyC,EAAUzC,UAAYA,IAqB9BvZ,sBAVA,SAA6Bgc,GAAWiD,iBAAEA,EAAgBC,YAAEA,EAAW5K,cAAEA,EAAaY,aAAEA,EAAYX,UAAEA,SAC5EpS,IAAlBmS,IACA0H,EAAU1H,cAAgBA,QACTnS,IAAjB+S,IACA8G,EAAU9G,aAAeA,QACX/S,IAAdoS,IACAyH,EAAUzH,UAAYA,GAC1ByH,EAAUiD,iBAAmBA,EAC7BjD,EAAUkD,YAAcA,OCvEX,SAASC,EAAMxc,EAAGC,GACjC,GAAID,IAAMC,EAAG,OAAO,EAEpB,GAAID,GAAKC,GAAiB,iBAALD,GAA6B,iBAALC,EAAe,CAC1D,GAAID,EAAExC,cAAgByC,EAAEzC,YAAa,OAAO,EAE5C,IAAIc,EAAQU,EAAGoP,EACf,GAAIvO,MAAMC,QAAQE,GAAI,CAEpB,IADA1B,EAAS0B,EAAE1B,SACG2B,EAAE3B,OAAQ,OAAO,EAC/B,IAAKU,EAAIV,EAAgB,GAARU,KACf,IAAKwd,EAAMxc,EAAEhB,GAAIiB,EAAEjB,IAAK,OAAO,EACjC,OAAO,EAKT,GAAIgB,EAAExC,cAAgBif,OAAQ,OAAOzc,EAAE0c,SAAWzc,EAAEyc,QAAU1c,EAAE2c,QAAU1c,EAAE0c,MAC5E,GAAI3c,EAAE4c,UAAY1f,OAAO0a,UAAUgF,QAAS,OAAO5c,EAAE4c,YAAc3c,EAAE2c,UACrE,GAAI5c,EAAEhC,WAAad,OAAO0a,UAAU5Z,SAAU,OAAOgC,EAAEhC,aAAeiC,EAAEjC,WAIxE,IADAM,GADA8P,EAAOlR,OAAOkR,KAAKpO,IACL1B,UACCpB,OAAOkR,KAAKnO,GAAG3B,OAAQ,OAAO,EAE7C,IAAKU,EAAIV,EAAgB,GAARU,KACf,IAAK9B,OAAO0a,UAAUC,eAAesC,KAAKla,EAAGmO,EAAKpP,IAAK,OAAO,EAEhE,IAAKA,EAAIV,EAAgB,GAARU,KAAY,CAC3B,IAAIwB,EAAM4N,EAAKpP,GAEf,IAAKwd,EAAMxc,EAAEQ,GAAMP,EAAEO,IAAO,OAAO,EAGrC,OAAO,EAIT,OAAOR,GAAIA,GAAKC,GAAIA,mBC1CtB,IAAI4c,EAAWC,UAAiB,SAAU/P,EAAQ1K,EAAM0a,GAEnC,mBAAR1a,IACT0a,EAAK1a,EACLA,EAAO,IAOT2a,EAAU3a,EAHc,mBADxB0a,EAAK1a,EAAK0a,IAAMA,GACsBA,EAAKA,EAAGE,KAAO,aAC1CF,EAAG7I,MAAQ,aAEKnH,EAAQ,GAAIA,IAqDzC,SAASiQ,EAAU3a,EAAM4a,EAAK/I,EAAMnH,EAAQmQ,EAASC,EAAYC,EAAeC,EAAe/K,EAAcgL,GAC3G,GAAIvQ,GAA2B,iBAAVA,IAAuBlN,MAAMC,QAAQiN,GAAS,CAEjE,IAAK,IAAIvM,KADTyc,EAAIlQ,EAAQmQ,EAASC,EAAYC,EAAeC,EAAe/K,EAAcgL,GAC7DvQ,EAAQ,CACtB,IAAI0K,EAAM1K,EAAOvM,GACjB,GAAIX,MAAMC,QAAQ2X,IAChB,GAAIjX,KAAOqc,EAASU,cAClB,IAAK,IAAIve,EAAE,EAAGA,EAAEyY,EAAInZ,OAAQU,IAC1Bge,EAAU3a,EAAM4a,EAAK/I,EAAMuD,EAAIzY,GAAIke,EAAU,IAAM1c,EAAM,IAAMxB,EAAGme,EAAYD,EAAS1c,EAAKuM,EAAQ/N,QAEnG,GAAIwB,KAAOqc,EAASW,eACzB,GAAI/F,GAAqB,iBAAPA,EAChB,IAAK,IAAIN,KAAQM,EACfuF,EAAU3a,EAAM4a,EAAK/I,EAAMuD,EAAIN,GAAO+F,EAAU,IAAM1c,EAAM,IAAoB2W,EAY/E9W,QAAQ,KAAM,MAAMA,QAAQ,MAAO,MAZmD8c,EAAYD,EAAS1c,EAAKuM,EAAQoK,QAEpH3W,KAAOqc,EAASzP,UAAa/K,EAAKob,WAAajd,KAAOqc,EAASa,gBACxEV,EAAU3a,EAAM4a,EAAK/I,EAAMuD,EAAKyF,EAAU,IAAM1c,EAAK2c,EAAYD,EAAS1c,EAAKuM,GAGnFmH,EAAKnH,EAAQmQ,EAASC,EAAYC,EAAeC,EAAe/K,EAAcgL,IApElFT,EAASzP,SAAW,CAClBuQ,iBAAiB,EACjB7U,OAAO,EACP8U,UAAU,EACVC,sBAAsB,EACtBC,eAAe,EACfpW,KAAK,EACLwC,IAAI,EACJ6T,MAAM,EACNzW,MAAM,GAGRuV,EAASU,cAAgB,CACvBzU,OAAO,EACPkV,OAAO,EACPC,OAAO,EACPC,OAAO,GAGTrB,EAASW,cAAgB,CACvBW,OAAO,EACPC,aAAa,EACb5G,YAAY,EACZ6G,mBAAmB,EACnBzC,cAAc,GAGhBiB,EAASa,aAAe,CACtB5M,SAAS,EACTwN,MAAM,EACNzd,OAAO,EACP0d,UAAU,EACVC,SAAS,EACTC,SAAS,EACTC,kBAAkB,EAClBC,kBAAkB,EAClBC,YAAY,EACZC,WAAW,EACXC,WAAW,EACXjG,SAAS,EACTkG,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,eAAe,EACfC,eAAe,uBC7DjBliB,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,gBAAwBA,aAAqBA,cAAsBA,eAAuBA,cAAsBA,iBAAoB,EAKpI,MAAMgiB,EAAiB,IAAI9V,IAAI,CAC3B,OACA,SACA,UACA,YACA,YACA,gBACA,gBACA,WACA,WACA,UACA,UACA,cACA,aACA,WACA,OACA,UAWJlM,YATA,SAAmB0P,EAAQuS,GAAQ,GAC/B,MAAqB,kBAAVvS,KAEG,IAAVuS,GACQC,EAAOxS,KACduS,GAEEE,EAAUzS,IAAWuS,IAGhC,MAAMG,EAAe,IAAIlW,IAAI,CACzB,OACA,gBACA,mBACA,cACA,mBAEJ,SAASgW,EAAOxS,GACZ,IAAK,MAAMvM,KAAOuM,EAAQ,CACtB,GAAI0S,EAAa5d,IAAIrB,GACjB,OAAO,EACX,MAAMiX,EAAM1K,EAAOvM,GACnB,GAAIX,MAAMC,QAAQ2X,IAAQA,EAAI5O,KAAK0W,GAC/B,OAAO,EACX,GAAkB,iBAAP9H,GAAmB8H,EAAO9H,GACjC,OAAO,EAEf,OAAO,EAEX,SAAS+H,EAAUzS,GACf,IAAI2S,EAAQ,EACZ,IAAK,MAAMlf,KAAOuM,EAAQ,CACtB,GAAY,SAARvM,EACA,OAAOmf,SAEX,GADAD,KACIL,EAAexd,IAAIrB,KAEG,iBAAfuM,EAAOvM,KACd,EAAIsS,EAAO8M,UAAU7S,EAAOvM,GAAOiX,GAASiI,GAASF,EAAU/H,IAErDkI,WAAVD,GACA,OAAOC,SAEf,OAAOD,EAEX,SAASG,EAAYC,EAAUC,EAAK,GAAIC,IAClB,IAAdA,IACAD,EAAKE,EAAYF,IACrB,MAAM1R,EAAIyR,EAASI,MAAMH,GACzB,OAAOI,EAAaL,EAAUzR,GAGlC,SAAS8R,EAAaL,EAAUzR,GAE5B,OADmByR,EAASM,UAAU/R,GACpBgS,MAAM,KAAK,GAAK,IAHtChjB,cAAsBwiB,EAKtBxiB,eAAuB8iB,EACvB,MAAMG,EAAsB,QAC5B,SAASL,EAAYF,GACjB,OAAOA,EAAKA,EAAG1f,QAAQigB,EAAqB,IAAM,GAEtDjjB,cAAsB4iB,EAKtB5iB,aAJA,SAAoByiB,EAAUS,EAAQR,GAElC,OADAA,EAAKE,EAAYF,GACVD,EAASU,QAAQD,EAAQR,IAGpC,MAAMU,EAAS,wBA6DfpjB,gBA5DA,SAAuB0P,EAAQwT,GAC3B,GAAqB,kBAAVxT,EACP,MAAO,GACX,MAAM2T,SAAEA,EAAQC,YAAEA,GAAgB7iB,KAAKuE,KACjCue,EAAQX,EAAYlT,EAAO2T,IAAaH,GACxCM,EAAU,CAAE,GAAID,GAChBE,EAAajB,EAAYc,EAAaC,GAAO,GAC7CG,EAAY,GACZC,EAAa,IAAIzX,IA2CvB,OA1CAsT,EAAS9P,EAAQ,CAAE0Q,SAAS,GAAQ,CAAChG,EAAKyF,EAASre,EAAGue,KAClD,QAAsB5d,IAAlB4d,EACA,OACJ,MAAM6D,EAAWH,EAAa5D,EAC9B,IAAIqD,EAASM,EAAQzD,GAMrB,SAAS8D,EAAOre,GAIZ,GADAA,EAAMod,EAAYM,GAASY,EADVrjB,KAAKuE,KAAKse,YAAYH,SACHD,EAAQ1d,GAAOA,GAC/Cme,EAAWnf,IAAIgB,GACf,MAAMue,EAASve,GACnBme,EAAWxX,IAAI3G,GACf,IAAIwe,EAAWvjB,KAAKwjB,KAAKze,GAezB,MAduB,iBAAZwe,IACPA,EAAWvjB,KAAKwjB,KAAKD,IACF,iBAAZA,EACPE,EAAiB9J,EAAK4J,EAAStU,OAAQlK,GAElCA,IAAQod,EAAYgB,KACV,MAAXpe,EAAI,IACJ0e,EAAiB9J,EAAKsJ,EAAUle,GAAMA,GACtCke,EAAUle,GAAO4U,GAGjB3Z,KAAKwjB,KAAKze,GAAOoe,GAGlBpe,EAEX,SAAS2e,EAAUC,GACf,GAAqB,iBAAVA,EAAoB,CAC3B,IAAKhB,EAAO7iB,KAAK6jB,GACb,MAAM,IAAI5jB,MAAM,mBAAmB4jB,MACvCP,EAAO/G,KAAKrc,KAAM,IAAI2jB,MAjCF,iBAAjBhK,EAAIiJ,KACXH,EAASW,EAAO/G,KAAKrc,KAAM2Z,EAAIiJ,KACnCc,EAAUrH,KAAKrc,KAAM2Z,EAAIiK,SACzBF,EAAUrH,KAAKrc,KAAM2Z,EAAIkK,gBACzBd,EAAQ3D,GAAWqD,IAiChBQ,EACP,SAASQ,EAAiBK,EAAMC,EAAMhf,GAClC,QAAarD,IAATqiB,IAAuBrF,EAAMoF,EAAMC,GACnC,MAAMT,EAASve,GAEvB,SAASue,EAASve,GACd,OAAO,IAAIhF,MAAM,cAAcgF,6DCrJvC3F,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,UAAkBA,aAAqBA,4BAA+B,EAItE,MAAMykB,EAAaC,EAqBnB,SAASC,GAAiBlU,IAAEA,EAAGmD,aAAEA,EAAYlE,OAAEA,EAAMmE,UAAEA,EAAS7O,KAAEA,GAAQyJ,GAClEzJ,EAAKjE,KAAK4F,IACV8J,EAAI1B,KAAK6E,EAAkBlD,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQjB,SAASgB,EAAQC,QAAQhB,SAAUoB,EAAUC,OAAQ,KAC5GrD,EAAI1P,KAAS2P,EAAUlP,CAAG,iBAAiBojB,EAAclV,EAAQ1K,MAY7E,SAA8ByL,EAAKzL,GAC/ByL,EAAI5D,GAAG2G,EAAQC,QAAQhB,OAAQ,KAC3BhC,EAAI9M,IAAI6P,EAAQC,QAAQf,aAAkBhC,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQhB,UAAUe,EAAQC,QAAQf,gBACpGjC,EAAI9M,IAAI6P,EAAQC,QAAQd,WAAgBjC,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQhB,UAAUe,EAAQC,QAAQd,cAClGlC,EAAI9M,IAAI6P,EAAQC,QAAQb,mBAAwBlC,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQhB,UAAUe,EAAQC,QAAQb,sBAC1GnC,EAAI9M,IAAI6P,EAAQC,QAAQZ,SAAcnC,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQhB,UAAUe,EAAQC,QAAQZ,YAC5F7N,EAAKsW,YACL7K,EAAI9M,IAAI6P,EAAQC,QAAQX,eAAoBpC,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQhB,UAAUe,EAAQC,QAAQX,mBAC3G,KACCrC,EAAI9M,IAAI6P,EAAQC,QAAQf,aAAkBhC,EAAUlP,CAAG,MACvDiP,EAAI9M,IAAI6P,EAAQC,QAAQd,WAAgBjC,EAAUlP,CAAG,aACrDiP,EAAI9M,IAAI6P,EAAQC,QAAQb,mBAAwBlC,EAAUlP,CAAG,aAC7DiP,EAAI9M,IAAI6P,EAAQC,QAAQZ,SAAUW,EAAQC,QAAQjB,MAC9CxN,EAAKsW,YACL7K,EAAI9M,IAAI6P,EAAQC,QAAQX,eAAoBpC,EAAUlP,CAAG,QAzBzDqjB,CAAqBpU,EAAKzL,GAC1ByL,EAAI1P,KAAK0N,KAIbgC,EAAI1B,KAAK6E,EAAkBlD,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQjB,SAGnE,SAA2BxN,GACvB,OAAW0L,EAAUlP,CAAG,IAAIgS,EAAQC,QAAQf,oBAAoBc,EAAQC,QAAQd,eAAea,EAAQC,QAAQb,uBAAuBY,EAAQC,QAAQZ,YAAYW,EAAQC,QAAQjB,OAAOxN,EAAKsW,WAAiB5K,EAAUlP,CAAG,KAAKgS,EAAQC,QAAQX,oBAAsBpC,EAAUpL,UAJzMwf,CAAkB9f,KAAS6O,EAAUC,OAAQ,IAAMrD,EAAI1P,KAAK6jB,EAAclV,EAAQ1K,IAAOjE,KAAK0N,IA6C1K,SAASmW,EAAclV,EAAQ1K,GAC3B,MAAMue,EAAyB,iBAAV7T,GAAsBA,EAAO1K,EAAKqe,UACvD,OAAOE,IAAUve,EAAKjE,KAAKse,QAAUra,EAAKjE,KAAKgkB,SAAerU,EAAUlP,CAAG,iBAAiB+hB,OAAa7S,EAAUpL,IAavH,SAAS0f,GAAkBtV,OAAEA,EAAMC,KAAEA,IACjC,GAAqB,kBAAVD,EACP,OAAQA,EACZ,IAAK,MAAMvM,KAAOuM,EACd,GAAIC,EAAKG,MAAMoB,IAAI/N,GACf,OAAO,EACf,OAAO,EAEX,SAAS8hB,EAAYxV,GACjB,MAA2B,kBAAbA,EAAGC,OAarB,SAASwV,EAAczV,IACnB,EAAIgG,EAAOjG,mBAAmBC,GAUlC,SAA8BA,GAC1B,MAAMC,OAAEA,EAAMkF,cAAEA,EAAa5P,KAAEA,EAAI2K,KAAEA,GAASF,EAC1CC,EAAOyV,MAAQngB,EAAKogB,wBAAyB,EAAI3P,EAAO4P,sBAAsB3V,EAAQC,EAAKG,QAC3FH,EAAKsC,OAAOC,KAAK,6CAA6C0C,MAZlE0Q,CAAqB7V,GAEzB,SAAS8V,EAAgB9V,EAAIgF,GACzB,GAAIhF,EAAGzK,KAAKwgB,IACR,OAAOC,EAAehW,EAAI,IAAI,EAAOgF,GACzC,MAAMgC,GAAQ,EAAIiO,EAAWgB,gBAAgBjW,EAAGC,QAEhD+V,EAAehW,EAAIgH,IADE,EAAIiO,EAAWiB,wBAAwBlW,EAAIgH,GACvBhC,GAuB7C,SAASmR,GAAenV,IAAEA,EAAGoD,UAAEA,EAASnE,OAAEA,EAAMkF,cAAEA,EAAa5P,KAAEA,IAC7D,MAAM+M,EAAMrC,EAAOmW,SACnB,IAAsB,IAAlB7gB,EAAK6gB,SACLpV,EAAI1P,KAAS2P,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQ9D,mBAAmBoC,WAE/D,GAA4B,mBAAjB/M,EAAK6gB,SAAwB,CACzC,MAAMzU,EAAiBV,EAAUhQ,GAAK,GAAGkU,aACnCkR,EAAWrV,EAAIzE,WAAW,OAAQ,CAAExG,IAAKqO,EAAUkS,OACzDtV,EAAI1P,KAAS2P,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQ9D,sBAAsBoC,MAAQX,MAAe0U,cAsBlG,SAASL,EAAehW,EAAIgH,EAAOuP,EAAYvR,GAC3C,MAAMhE,IAAEA,EAAGf,OAAEA,EAAM8C,KAAEA,EAAI+B,UAAEA,EAASvP,KAAEA,EAAI2K,KAAEA,GAASF,GAC/CK,MAAEA,GAAUH,EAYlB,SAASsW,EAAclP,IACd,EAAIqB,EAAgBtB,gBAAgBpH,EAAQqH,KAE7CA,EAAMT,MACN7F,EAAI5D,IAAG,EAAI4X,EAAWzL,eAAejC,EAAMT,KAAM9D,EAAMxN,EAAKwT,gBAC5D0N,EAAgBzW,EAAIsH,GACC,IAAjBN,EAAMxV,QAAgBwV,EAAM,KAAOM,EAAMT,MAAQ0P,IACjDvV,EAAIxG,QACJ,EAAIwa,EAAW5L,iBAAiBpJ,IAEpCgB,EAAIxD,SAGJiZ,EAAgBzW,EAAIsH,GAGnBxC,GACD9D,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQT,cAAcyB,GAAa,OA5B1E/E,EAAOyV,OAASngB,EAAKogB,wBAA0B,EAAI3P,EAAO4P,sBAAsB3V,EAAQI,IAIvF9K,EAAKwgB,KAuCd,SAA0B/V,EAAIgH,IACtBhH,EAAGoE,UAAUsS,MAAS1W,EAAGzK,KAAKohB,cAOtC,SAA2B3W,EAAIgH,GACtBA,EAAMxV,SAENwO,EAAG8J,UAAUtY,QAIlBwV,EAAMhQ,QAASuR,IACNqO,EAAa5W,EAAG8J,UAAWvB,IAC5BsO,EAAiB7W,EAAI,SAASuI,8BAA8BvI,EAAG8J,UAAU7W,KAAK,WAGtF+M,EAAG8J,UAAY9J,EAAG8J,UAAUxB,OAAQC,GAAMqO,EAAa5P,EAAOuB,KAR1DvI,EAAG8J,UAAY9C,GATnB8P,CAAkB9W,EAAIgH,GACjBhH,EAAGzK,KAAKwhB,iBAkBjB,SAA4B/W,EAAI8H,GACxBA,EAAGtW,OAAS,IAAqB,IAAdsW,EAAGtW,SAAgBsW,EAAGI,SAAS,UAClD2O,EAAiB7W,EAAI,mDAnBrBgX,CAAmBhX,EAAIgH,GAsB/B,SAA2BhH,EAAI8H,GAC3B,MAAM1H,EAAQJ,EAAGE,KAAKG,MAAMoB,IAC5B,IAAK,MAAMG,KAAWxB,EAAO,CACzB,MAAMmH,EAAOnH,EAAMwB,GACnB,GAAmB,iBAAR2F,IAAoB,EAAIoB,EAAgBnB,eAAexH,EAAGC,OAAQsH,GAAO,CAChF,MAAMV,KAAEA,GAASU,EAAKE,WAClBZ,EAAKrV,SAAWqV,EAAK9K,KAAMwM,IAAM0O,OAMtBC,EANwCpP,GAOlDI,SADiBiP,EANqC5O,IAO1B,WAAT4O,GAAqBD,EAAMhP,SAAS,WADxE,IAA2BgP,EAAOC,KALlBN,EAAiB7W,EAAI,iBAAiB6G,EAAK5T,KAAK,sBAAsB2O,QA5BlFwV,CAAkBpX,EAAIA,EAAG8J,YA5CrBuN,CAAiBrX,EAAIgH,GACzBhG,EAAIjC,MAAM,KACN,IAAK,MAAMuI,KAASjH,EAAMD,MACtBoW,EAAclP,GAClBkP,EAAcnW,EAAM+G,SARpBpG,EAAIjC,MAAM,IAAMuY,EAAYtX,EAAI,OAAQK,EAAMoB,IAAIiU,KAAKjO,aA8B/D,SAASgP,EAAgBzW,EAAIsH,GACzB,MAAMtG,IAAEA,EAAGf,OAAEA,EAAQ1K,MAAMiV,YAAEA,IAAmBxK,EAC5CwK,IACA,EAAI+M,EAAWC,gBAAgBxX,EAAIsH,EAAMT,MAC7C7F,EAAIjC,MAAM,KACN,IAAK,MAAMwI,KAAQD,EAAMlH,OACjB,EAAIuI,EAAgBnB,eAAevH,EAAQsH,IAC3C+P,EAAYtX,EAAIuH,EAAK3F,QAAS2F,EAAKE,WAAYH,EAAMT,QA+CrE,SAAS+P,EAAa9O,EAAIS,GACtB,OAAOT,EAAGI,SAASK,IAAa,YAANA,GAAmBT,EAAGI,SAAS,UAE7D,SAAS2O,EAAiB7W,EAAIsC,IAG1B,EAAI0D,EAAOzF,iBAAiBP,EAD5BsC,GAAO,QADYtC,EAAGoE,UAAUqP,OAASzT,EAAGmF,+BAEPnF,EAAGzK,KAAKohB,aAxPjDpmB,uBAVA,SAA8ByP,GACtBwV,EAAYxV,KACZyV,EAAczV,GACVuV,EAAkBvV,IAwC9B,SAA0BA,GACtB,MAAMC,OAAEA,EAAM1K,KAAEA,EAAIyL,IAAEA,GAAQhB,EAC9BkV,EAAiBlV,EAAI,KACbzK,EAAK6gB,UAAYnW,EAAOmW,UACxBD,EAAenW,GAwE3B,SAAwBA,GACpB,MAAMC,OAAEA,EAAM1K,KAAEA,GAASyK,OACFtN,IAAnBuN,EAAO+D,SAAyBzO,EAAKiV,aAAejV,EAAK4K,eACzD,EAAI6F,EAAOzF,iBAAiBP,EAAI,yCA1EhCyX,CAAezX,GACfgB,EAAI/M,IAAI8P,EAAQC,QAAQV,QAAS,MACjCtC,EAAI/M,IAAI8P,EAAQC,QAAQT,OAAQ,GAC5BhO,EAAKiX,aAOjB,SAAwBxM,GAEpB,MAAMgB,IAAEA,EAAGmD,aAAEA,GAAiBnE,EAC9BA,EAAG0X,UAAY1W,EAAIjN,MAAM,YAAiBkN,EAAUlP,CAAG,GAAGoS,eAC1DnD,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGiO,EAAG0X,yBAA0B,IAAM1W,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAGiO,EAAG0X,kBAAuBzW,EAAUlP,CAAG,cACpIiP,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGiO,EAAG0X,yBAA0B,IAAM1W,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAGiO,EAAG0X,kBAAuBzW,EAAUlP,CAAG,cAX5H4lB,CAAe3X,GACnB8V,EAAgB9V,GA4FxB,SAAuBA,GACnB,MAAMgB,IAAEA,EAAGoD,UAAEA,EAASD,aAAEA,EAAYG,gBAAEA,EAAe/O,KAAEA,GAASyK,EAC5DoE,EAAUC,OAEVrD,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQT,eAAgB,IAAMvC,EAAIxC,OAAOuF,EAAQC,QAAQjB,MAAO,IAAM/B,EAAIlC,MAAUmC,EAAUlP,CAAG,OAAOuS,KAAmBP,EAAQC,QAAQV,cAG9KtC,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAGoS,WAAuBJ,EAAQC,QAAQV,SAClE/N,EAAKiX,aAKjB,UAAyBxL,IAAEA,EAAG0W,UAAEA,EAAStW,MAAEA,EAAKpF,MAAEA,IAC1CoF,aAAiBH,EAAUxQ,MAC3BuQ,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAG2lB,UAAmBtW,GAClDpF,aAAiBiF,EAAUxQ,MAC3BuQ,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAG2lB,UAAmB1b,GAR9C4b,CAAgB5X,GACpBgB,EAAIxC,OAAWyC,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQT,iBArG/CsU,CAAc7X,KAlDV8X,CAAiB9X,GAIzBkV,EAAiBlV,EAAI,KAAM,EAAI+X,EAAaC,sBAAsBhY,KA4PtE,MAAMiY,EACFvnB,YAAYsP,EAAIkN,EAAKtL,GAcjB,IAbA,EAAIsW,EAAUC,sBAAsBnY,EAAIkN,EAAKtL,GAC7C5Q,KAAKgQ,IAAMhB,EAAGgB,IACdhQ,KAAK8T,UAAY9E,EAAG8E,UACpB9T,KAAK4Q,QAAUA,EACf5Q,KAAK+R,KAAO/C,EAAG+C,KACf/R,KAAKiP,OAASD,EAAGC,OAAO2B,GACxB5Q,KAAK6Q,MAAQqL,EAAIrL,OAAS7B,EAAGzK,KAAKsM,OAAS7Q,KAAKiP,QAAUjP,KAAKiP,OAAO4B,MACtE7Q,KAAKiU,aAAc,EAAIe,EAAOkE,gBAAgBlK,EAAIhP,KAAKiP,OAAQ2B,EAAS5Q,KAAK6Q,OAC7E7Q,KAAKwT,WAAa0I,EAAI1I,WACtBxT,KAAKwU,aAAexF,EAAGC,OACvBjP,KAAKsU,OAAS,GACdtU,KAAKgP,GAAKA,EACVhP,KAAKkc,IAAMA,EACPlc,KAAK6Q,MACL7Q,KAAKwV,WAAaxG,EAAGgB,IAAIjN,MAAM,UAAWqkB,EAAQpnB,KAAK6Q,MAAO7B,SAI9D,GADAhP,KAAKwV,WAAaxV,KAAKiU,cAClB,EAAIiT,EAAUG,iBAAiBrnB,KAAKiP,OAAQiN,EAAI1I,WAAY0I,EAAIyB,gBACjE,MAAM,IAAI5d,MAAM,GAAG6Q,mBAAyBvO,KAAKC,UAAU4Z,EAAI1I,gBAGnE,SAAU0I,EAAMA,EAAIoL,aAA6B,IAAfpL,EAAI3J,UACtCvS,KAAKgU,UAAYhF,EAAGgB,IAAIjN,MAAM,QAASgQ,EAAQC,QAAQT,SAG/DuJ,OAAOvS,EAAWge,EAAeC,GAC7BxnB,KAAKynB,YAAW,EAAIxX,EAAUrG,KAAKL,GAAYge,EAAeC,GAElEC,WAAWle,EAAWge,EAAeC,GACjCxnB,KAAKgQ,IAAI5D,GAAG7C,GACRie,EACAA,IAEAxnB,KAAK2I,QACL4e,GACAvnB,KAAKgQ,IAAIxG,OACT+d,IACIvnB,KAAK8T,WACL9T,KAAKgQ,IAAIxD,SAGTxM,KAAK8T,UACL9T,KAAKgQ,IAAIxD,QAETxM,KAAKgQ,IAAIxG,OAGrBgT,KAAKjT,EAAWie,GACZxnB,KAAKynB,YAAW,EAAIxX,EAAUrG,KAAKL,QAAY7H,EAAW8lB,GAE9DE,KAAKne,GACD,QAAkB7H,IAAd6H,EAIA,OAHAvJ,KAAK2I,aACA3I,KAAK8T,WACN9T,KAAKgQ,IAAI5D,IAAG,IAGpBpM,KAAKgQ,IAAI5D,GAAG7C,GACZvJ,KAAK2I,QACD3I,KAAK8T,UACL9T,KAAKgQ,IAAIxD,QAETxM,KAAKgQ,IAAIxG,OAEjBme,UAAUpe,GACN,IAAKvJ,KAAK6Q,MACN,OAAO7Q,KAAK0nB,KAAKne,GACrB,MAAMiM,WAAEA,GAAexV,KACvBA,KAAK0nB,KAASzX,EAAUlP,CAAG,GAAGyU,wBAAgC,EAAIvF,EAAUiK,IAAIla,KAAK4nB,eAAgBre,OAEzGZ,MAAMkf,EAAQC,EAAanU,GACvB,GAAImU,EAIA,OAHA9nB,KAAKqa,UAAUyN,GACf9nB,KAAK+nB,OAAOF,EAAQlU,QACpB3T,KAAKqa,UAAU,IAGnBra,KAAK+nB,OAAOF,EAAQlU,GAExBoU,OAAOF,EAAQlU,IAEVkU,EAASvS,EAAS0S,iBAAmB1S,EAASC,aAAavV,KAAMA,KAAKkc,IAAIvT,MAAOgL,GAEtFsU,cACI,EAAI3S,EAASC,aAAavV,KAAMA,KAAKkc,IAAI+L,YAAc3S,EAAS4S,mBAEpEnM,QACI,QAAuBra,IAAnB1B,KAAKgU,UACL,MAAM,IAAIjU,MAAM,4CACpB,EAAIuV,EAAS6S,kBAAkBnoB,KAAKgQ,IAAKhQ,KAAKgU,WAElD0J,GAAGjU,GACMzJ,KAAK8T,WACN9T,KAAKgQ,IAAI5D,GAAG3C,GAEpB4Q,UAAUhN,EAAKrB,GACPA,EACA5M,OAAO4M,OAAOhM,KAAKsU,OAAQjH,GAE3BrN,KAAKsU,OAASjH,EAEtB8P,WAAW1H,EAAO2S,EAAWC,EAAapY,EAAUpL,KAChD7E,KAAKgQ,IAAIjC,MAAM,KACX/N,KAAKsoB,WAAW7S,EAAO4S,GACvBD,MAGRE,WAAW7S,EAAQxF,EAAUpL,IAAKwjB,EAAapY,EAAUpL,KACrD,IAAK7E,KAAK6Q,MACN,OACJ,MAAMb,IAAEA,EAAGwF,WAAEA,EAAUhC,WAAEA,EAAU0I,IAAEA,GAAQlc,KAC7CgQ,EAAI5D,IAAG,EAAI6D,EAAUiK,IAAQjK,EAAUlP,CAAG,GAAGyU,kBAA4B6S,IACrE5S,IAAUxF,EAAUpL,KACpBmL,EAAIhE,OAAOyJ,GAAO,IAClBjC,EAAWhT,QAAU0b,EAAIK,kBACzBvM,EAAIvD,OAAOzM,KAAK4nB,gBAChB5nB,KAAKioB,aACDxS,IAAUxF,EAAUpL,KACpBmL,EAAIhE,OAAOyJ,GAAO,IAE1BzF,EAAIxG,OAERoe,eACI,MAAM5X,IAAEA,EAAGwF,WAAEA,EAAUhC,WAAEA,EAAU0I,IAAEA,EAAGlN,GAAEA,GAAOhP,KACjD,OAAO,EAAIiQ,EAAUiK,IACrB,WACI,GAAI1G,EAAWhT,OAAQ,CAEnB,KAAMgV,aAAsBvF,EAAUxQ,MAClC,MAAM,IAAIM,MAAM,4BACpB,MAAM6d,EAAK7b,MAAMC,QAAQwR,GAAcA,EAAa,CAACA,GACrD,OAAWvD,EAAUlP,CAAG,IAAG,EAAIijB,EAAWlM,gBAAgB8F,EAAIpI,EAAYxG,EAAGzK,KAAKwT,cAAeiM,EAAWpN,SAASoB,SAEzH,OAAO/H,EAAUpL,IATI0jB,GAWzB,WACI,GAAIrM,EAAIK,eAAgB,CACpB,MAAMiM,EAAoBxY,EAAIzE,WAAW,gBAAiB,CAAExG,IAAKmX,EAAIK,iBACrE,OAAWtM,EAAUlP,CAAG,IAAIynB,KAAqBhT,KAErD,OAAOvF,EAAUpL,IAhBsB4jB,IAmB/ClN,UAAUmN,EAAMjT,GACZ,MAAM8F,GAAY,EAAIoN,EAAYC,cAAc5oB,KAAKgP,GAAI0Z,IACzD,EAAIC,EAAYE,qBAAqBtN,EAAWvb,KAAKgP,GAAI0Z,IACzD,EAAIC,EAAYG,qBAAqBvN,EAAWmN,GAChD,MAAMK,EAAc,IAAK/oB,KAAKgP,MAAOuM,EAAWvQ,WAAOtJ,EAAW0O,WAAO1O,GAEzE,OAtVR,SAAuBsN,EAAIyG,GACnB+O,EAAYxV,KACZyV,EAAczV,GACVuV,EAAkBvV,IAkB9B,SAA0BA,EAAIyG,GAC1B,MAAMxG,OAAEA,EAAMe,IAAEA,EAAGzL,KAAEA,GAASyK,EAC1BzK,EAAK6gB,UAAYnW,EAAOmW,UACxBD,EAAenW,GA+BvB,SAAuBA,GACnB,MAAM8T,EAAQ9T,EAAGC,OAAOD,EAAGzK,KAAKqe,UAC5BE,IACA9T,EAAGyT,QAAS,EAAIuG,EAAUC,YAAYja,EAAGzK,KAAKse,YAAa7T,EAAGyT,OAAQK,IAjC1EoG,CAAcla,GAmClB,SAA0BA,GACtB,GAAIA,EAAGC,OAAOoE,SAAWrE,EAAGoE,UAAUC,OAClC,MAAM,IAAItT,MAAM,+BApCpBopB,CAAiBna,GACjB,MAAMgF,EAAYhE,EAAIjN,MAAM,QAASgQ,EAAQC,QAAQT,QACrDuS,EAAgB9V,EAAIgF,GAEpBhE,EAAI9M,IAAIuS,EAAWxF,EAAUlP,CAAG,GAAGiT,SAAiBjB,EAAQC,QAAQT,UA1B5D6W,CAAiBpa,EAAIyG,IAI7B,EAAIsR,EAAasC,mBAAmBra,EAAIyG,GA6UpC6T,CAAcP,EAAatT,GACpBsT,EAEXQ,eAAeC,EAAWhmB,GACtB,MAAMwL,GAAEA,EAAEgB,IAAEA,GAAQhQ,KACfgP,EAAGzK,KAAKiX,eAEI,IAAbxM,EAAGoB,YAAsC1O,IAApB8nB,EAAUpZ,QAC/BpB,EAAGoB,MAAQ4E,EAAOuU,eAAenZ,MAAMJ,EAAKwZ,EAAUpZ,MAAOpB,EAAGoB,MAAO5M,KAE1D,IAAbwL,EAAGhE,YAAsCtJ,IAApB8nB,EAAUxe,QAC/BgE,EAAGhE,MAAQgK,EAAOuU,eAAeve,MAAMgF,EAAKwZ,EAAUxe,MAAOgE,EAAGhE,MAAOxH,KAG/EqY,oBAAoB2N,EAAW/T,GAC3B,MAAMzG,GAAEA,EAAEgB,IAAEA,GAAQhQ,KACpB,GAAIgP,EAAGzK,KAAKiX,eAA6B,IAAbxM,EAAGoB,QAA+B,IAAbpB,EAAGhE,OAEhD,OADAgF,EAAI5D,GAAGqJ,EAAO,IAAMzV,KAAKupB,eAAeC,EAAWvZ,EAAUxQ,QACtD,GAKnB,SAAS6mB,EAAYtX,EAAI4B,EAASsL,EAAKuN,GACnC,MAAMhW,EAAM,IAAIwT,EAAWjY,EAAIkN,EAAKtL,GAChC,SAAUsL,EACVA,EAAI5b,KAAKmT,EAAKgW,GAEThW,EAAI5C,OAASqL,EAAIQ,UACtB,EAAIwK,EAAUwC,iBAAiBjW,EAAKyI,GAE/B,UAAWA,GAChB,EAAIgL,EAAUyC,kBAAkBlW,EAAKyI,IAEhCA,EAAIS,SAAWT,EAAIQ,YACxB,EAAIwK,EAAUwC,iBAAiBjW,EAAKyI,GAb5C3c,aAAqB0nB,EAgBrB,MAAM2C,EAAe,sBACfC,EAAwB,mCAC9B,SAASzC,EAAQvW,GAAOwN,UAAEA,EAASE,UAAEA,EAASL,YAAEA,IAC5C,IAAI4L,EACA/X,EACJ,GAAc,KAAVlB,EACA,OAAOkC,EAAQC,QAAQZ,SAC3B,GAAiB,MAAbvB,EAAM,GAAY,CAClB,IAAK+Y,EAAa9pB,KAAK+Q,GACnB,MAAM,IAAI9Q,MAAM,yBAAyB8Q,KAC7CiZ,EAAcjZ,EACdkB,EAAOgB,EAAQC,QAAQZ,aAEtB,CACD,MAAM2X,EAAUF,EAAsBG,KAAKnZ,GAC3C,IAAKkZ,EACD,MAAM,IAAIhqB,MAAM,yBAAyB8Q,KAC7C,MAAMoZ,GAAMF,EAAQ,GAEpB,GADAD,EAAcC,EAAQ,GACF,MAAhBD,EAAqB,CACrB,GAAIG,GAAM5L,EACN,MAAM,IAAIte,MAAMmqB,EAAS,iBAAkBD,IAC/C,OAAO/L,EAAYG,EAAY4L,GAEnC,GAAIA,EAAK5L,EACL,MAAM,IAAIte,MAAMmqB,EAAS,OAAQD,IAErC,GADAlY,EAAOwM,EAAUF,EAAY4L,IACxBH,EACD,OAAO/X,EAEf,IAAIzQ,EAAOyQ,EACX,MAAMoY,EAAWL,EAAYvH,MAAM,KACnC,IAAK,MAAM6H,KAAWD,EACdC,IACArY,EAAW9B,EAAUlP,CAAG,GAAGgR,KAAO,EAAI9B,EAAUzJ,cAAa,EAAIwO,EAAOtF,qBAAqB0a,MAC7F9oB,EAAW2O,EAAUlP,CAAG,GAAGO,QAAWyQ,KAG9C,OAAOzQ,EACP,SAAS4oB,EAASG,EAAaJ,GAC3B,MAAO,iBAAiBI,KAAeJ,iCAAkC5L,KAGjF9e,UAAkB6nB,IC1flB,MAAM9T,UAAwBvT,MAC1BL,YAAY6S,GACR3S,MAAM,qBACNI,KAAKuS,OAASA,EACdvS,KAAKsqB,IAAMtqB,KAAKuqB,YAAa,GAGrC,kDAAkBjX,4BCNlB,MAAMkX,UAAwBzqB,MAC1BL,YAAYsiB,EAAUS,EAAQ1d,EAAKuM,GAC/B1R,MAAM0R,GAAO,2BAA2BvM,aAAe0d,KACvDziB,KAAKyqB,YAAa,EAAIzB,EAAUC,YAAYjH,EAAUS,EAAQ1d,GAC9D/E,KAAK0qB,eAAgB,EAAI1B,EAAU7G,cAAa,EAAI6G,EAAUjH,aAAaC,EAAUhiB,KAAKyqB,cAGlG,kDAAkBD,8CCTlBprB,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,gBAAwBA,qBAA6BA,aAAqBA,gBAAwBA,iBAAoB,EAOtH,MAAMorB,EACFjrB,YAAYkrB,GACR,IAAIlqB,EAGJ,IAAIuO,EAFJjP,KAAKwjB,KAAO,GACZxjB,KAAKqS,eAAiB,GAEG,iBAAduY,EAAI3b,SACXA,EAAS2b,EAAI3b,QACjBjP,KAAKiP,OAAS2b,EAAI3b,OAClBjP,KAAK4iB,SAAWgI,EAAIhI,SACpB5iB,KAAKslB,KAAOsF,EAAItF,MAAQtlB,KACxBA,KAAKyiB,OAA+B,QAArB/hB,EAAKkqB,EAAInI,cAA2B,IAAP/hB,EAAgBA,GAAK,EAAIsoB,EAAU7G,aAAalT,MAAAA,OAAuC,EAASA,EAAO2b,EAAIhI,UAAY,QACnK5iB,KAAK2Q,WAAaia,EAAIja,WACtB3Q,KAAKijB,UAAY2H,EAAI3H,UACrBjjB,KAAK0lB,KAAOkF,EAAIlF,KAChB1lB,KAAKqT,OAASpE,MAAAA,OAAuC,EAASA,EAAOoE,OACrErT,KAAKwjB,KAAO,IAOpB,SAASqH,EAAclR,GAEnB,MAAM+B,EAAOoP,EAAmBzO,KAAKrc,KAAM2Z,GAC3C,GAAI+B,EACA,OAAOA,EACX,MAAMqP,GAAS,EAAI/B,EAAUjH,aAAa/hB,KAAKuE,KAAKse,YAAalJ,EAAI2L,KAAK7C,SACpEvc,IAAEA,EAAGtB,MAAEA,GAAU5E,KAAKuE,KAAKjE,MAC3BgN,cAAEA,GAAkBtN,KAAKuE,KACzByL,EAAM,IAAIC,EAAU+a,QAAQhrB,KAAK0E,MAAO,CAAEwB,IAAAA,EAAKtB,MAAAA,EAAO0I,cAAAA,IAC5D,IAAI2d,EACAtR,EAAItG,SACJ4X,EAAmBjb,EAAIzE,WAAW,QAAS,CACvCxG,IAAKmmB,EAAmBlY,QACxB1S,KAAU2P,EAAUlP,CAAG,0DAG/B,MAAMoS,EAAenD,EAAIxK,UAAU,YACnCmU,EAAIxG,aAAeA,EACnB,MAAMqW,EAAY,CACdxZ,IAAAA,EACA8D,UAAW9T,KAAKuE,KAAKuP,UACrB/B,KAAMgB,EAAQC,QAAQjB,KACtBG,WAAYa,EAAQC,QAAQd,WAC5BC,mBAAoBY,EAAQC,QAAQb,mBACpCoM,UAAW,CAACxL,EAAQC,QAAQjB,MAC5BmM,YAAa,CAACjO,EAAUpL,KACxBwZ,UAAW,EACXvF,UAAW,GACXwF,kBAAmB,IAAI7S,IACvBiF,aAAcV,EAAIzE,WAAW,UAAoC,IAA1BvL,KAAKuE,KAAKjE,KAAKse,OAChD,CAAE7Z,IAAK4U,EAAI1K,OAAQ3O,MAAM,EAAI2P,EAAU3N,WAAWqX,EAAI1K,SACtD,CAAElK,IAAK4U,EAAI1K,SACjBkE,aAAAA,EACAG,gBAAiB2X,EACjBhc,OAAQ0K,EAAI1K,OACZmE,UAAWuG,EACXoR,OAAAA,EACAtI,OAAQ9I,EAAI8I,QAAUsI,EACtBpa,WAAYV,EAAUpL,IACtBsP,cAAewF,EAAIhJ,aAAe3Q,KAAKuE,KAAKwgB,IAAM,GAAK,KACvD7Q,UAAejE,EAAUlP,CAAG,KAC5BwD,KAAMvE,KAAKuE,KACX2K,KAAMlP,MAEV,IAAImrB,EACJ,IACInrB,KAAKorB,cAAc1f,IAAIiO,IACvB,EAAI0R,EAAWC,sBAAsB9B,GACrCxZ,EAAIpO,SAAS5B,KAAKuE,KAAKjE,KAAKsB,UAE5B,MAAM2pB,EAAevb,EAAI9P,WACzBirB,EAAa,GAAGnb,EAAIzK,UAAUwN,EAAQC,QAAQtO,gBAAgB6mB,IAE1DvrB,KAAKuE,KAAKjE,KAAKgkB,UACf6G,EAAanrB,KAAKuE,KAAKjE,KAAKgkB,QAAQ6G,EAAYxR,IAEpD,MACM+C,EADe,IAAI8O,SAAS,GAAGzY,EAAQC,QAAQ9D,OAAQ,GAAG6D,EAAQC,QAAQtO,QAASymB,EACxEM,CAAazrB,KAAMA,KAAK0E,MAAMI,OAU/C,GATA9E,KAAK0E,MAAMpF,MAAM6T,EAAc,CAAEpO,IAAK2X,IACtCA,EAASnK,OAAS,KAClBmK,EAASzN,OAAS0K,EAAI1K,OACtByN,EAAStJ,UAAYuG,EACjBA,EAAItG,SACJqJ,EAASrJ,QAAS,IACQ,IAA1BrT,KAAKuE,KAAKjE,KAAKse,SACflC,EAASkC,OAAS,CAAEzL,aAAAA,EAAcoY,aAAAA,EAAcG,YAAa1b,EAAIxL,UAEjExE,KAAKuE,KAAKiX,YAAa,CACvB,MAAMpL,MAAEA,EAAKpF,MAAEA,GAAUwe,EACzB9M,EAASgK,UAAY,CACjBtW,MAAOA,aAAiBH,EAAUxQ,UAAOiC,EAAY0O,EACrDpF,MAAOA,aAAiBiF,EAAUxQ,UAAOiC,EAAYsJ,EACrD2gB,aAAcvb,aAAiBH,EAAUxQ,KACzCmsB,aAAc5gB,aAAiBiF,EAAUxQ,MAEzCid,EAASkC,SACTlC,EAASkC,OAAO8H,WAAY,EAAIzW,EAAU3N,WAAWoa,EAASgK,YAGtE,OADA/M,EAAI+C,SAAWA,EACR/C,EAEX,MAAOjQ,GAMH,aALOiQ,EAAI+C,gBACJ/C,EAAIxG,aACPgY,GACAnrB,KAAKwR,OAAO7I,MAAM,yCAA0CwiB,GAE1DzhB,UAGN1J,KAAKorB,cAAcS,OAAOlS,IAsBlC,SAASmS,EAAgBnS,GACrB,OAAI,EAAIqP,EAAU+C,WAAWpS,EAAI1K,OAAQjP,KAAKuE,KAAKynB,YACxCrS,EAAI1K,OACR0K,EAAI+C,SAAW/C,EAAMkR,EAAcxO,KAAKrc,KAAM2Z,GAGzD,SAASmR,EAAmBmB,GACxB,IAAK,MAAMtS,KAAO3Z,KAAKorB,cACnB,IAKec,EALGvS,GAMZ1K,UADakd,EALIF,GAMHhd,QAAUid,EAAG5G,OAAS6G,EAAG7G,MAAQ4G,EAAGzJ,SAAW0J,EAAG1J,OALlE,OAAO9I,EAInB,IAAuBuS,EAAIC,EAK3B,SAASzJ,EAAQ4C,EACjBvgB,GAEI,IAAI4U,EACJ,KAAwC,iBAAzBA,EAAM3Z,KAAKwjB,KAAKze,KAC3BA,EAAM4U,EACV,OAAOA,GAAO3Z,KAAKosB,QAAQrnB,IAAQsnB,EAAchQ,KAAKrc,KAAMslB,EAAMvgB,GAGtE,SAASsnB,EAAc/G,EACvBvgB,GAEI,MAAMwL,EAAIvQ,KAAKuE,KAAKse,YAAYT,MAAMrd,GAChCunB,GAAU,EAAItD,EAAU3G,cAAcriB,KAAKuE,KAAKse,YAAatS,GACnE,IAAIkS,GAAS,EAAIuG,EAAUjH,aAAa/hB,KAAKuE,KAAKse,YAAayC,EAAK7C,YAAQ/gB,GAE5E,GAAItC,OAAOkR,KAAKgV,EAAKrW,QAAQzO,OAAS,GAAK8rB,IAAY7J,EACnD,OAAO8J,EAAelQ,KAAKrc,KAAMuQ,EAAG+U,GAExC,MAAMrD,GAAK,EAAI+G,EAAU7G,aAAamK,GAChC/I,EAAWvjB,KAAKwjB,KAAKvB,IAAOjiB,KAAKosB,QAAQnK,GAC/C,GAAuB,iBAAZsB,EAAsB,CAC7B,MAAM5J,EAAM0S,EAAchQ,KAAKrc,KAAMslB,EAAM/B,GAC3C,GAAsE,iBAA1D5J,MAAAA,OAAiC,EAASA,EAAI1K,QACtD,OACJ,OAAOsd,EAAelQ,KAAKrc,KAAMuQ,EAAGoJ,GAExC,GAAqF,iBAAzE4J,MAAAA,OAA2C,EAASA,EAAStU,QAAzE,CAIA,GAFKsU,EAAS7G,UACVmO,EAAcxO,KAAKrc,KAAMujB,GACzBtB,KAAO,EAAI+G,EAAU7G,aAAapd,GAAM,CACxC,MAAMkK,OAAEA,GAAWsU,GACbX,SAAEA,GAAa5iB,KAAKuE,KACpBue,EAAQ7T,EAAO2T,GAGrB,OAFIE,IACAL,GAAS,EAAIuG,EAAUC,YAAYjpB,KAAKuE,KAAKse,YAAaJ,EAAQK,IAC/D,IAAI6H,EAAU,CAAE1b,OAAAA,EAAQ2T,SAAAA,EAAU0C,KAAAA,EAAM7C,OAAAA,IAEnD,OAAO8J,EAAelQ,KAAKrc,KAAMuQ,EAAGgT,IA7KxChkB,YAAoBorB,EAiGpBprB,gBAAwBsrB,EAkBxBtrB,aAjBA,SAAoB+lB,EAAM7C,EAAQ1d,GAC9B,IAAIrE,EACJqE,GAAM,EAAIikB,EAAUC,YAAYjpB,KAAKuE,KAAKse,YAAaJ,EAAQ1d,GAC/D,MAAMynB,EAAYlH,EAAK9B,KAAKze,GAC5B,GAAIynB,EACA,OAAOA,EACX,IAAI9Q,EAAOgH,EAAQrG,KAAKrc,KAAMslB,EAAMvgB,GACpC,QAAarD,IAATga,EAAoB,CACpB,MAAMzM,EAAmC,QAAzBvO,EAAK4kB,EAAKrC,iBAA8B,IAAPviB,OAAgB,EAASA,EAAGqE,IACvE6d,SAAEA,GAAa5iB,KAAKuE,KACtB0K,IACAyM,EAAO,IAAIiP,EAAU,CAAE1b,OAAAA,EAAQ2T,SAAAA,EAAU0C,KAAAA,EAAM7C,OAAAA,KAEvD,YAAa/gB,IAATga,EAEI4J,EAAK9B,KAAKze,GAAO+mB,EAAgBzP,KAAKrc,KAAM0b,QAFpD,GAiBJnc,qBAA6BurB,EA+C7BvrB,gBAAwB8sB,EACxB,MAAMI,EAAuB,IAAIhhB,IAAI,CACjC,aACA,oBACA,OACA,eACA,gBAEJ,SAAS8gB,EAAeG,GAAWjK,OAAEA,EAAMxT,OAAEA,EAAMqW,KAAEA,IACjD,IAAI5kB,EACJ,GAA+E,OAA5C,QAA7BA,EAAKgsB,EAAUC,gBAA6B,IAAPjsB,OAAgB,EAASA,EAAG,IACnE,OACJ,IAAK,MAAMksB,KAAQF,EAAUC,SAASvqB,MAAM,GAAGmgB,MAAM,KAAM,CACvD,GAAsB,kBAAXtT,EACP,OACJ,MAAM4d,EAAa5d,GAAO,EAAI+F,EAAO8X,kBAAkBF,IACvD,QAAmBlrB,IAAfmrB,EACA,OAGJ,MAAM/J,EAA0B,iBAFhC7T,EAAS4d,IAEmC5d,EAAOjP,KAAKuE,KAAKqe,WACxD6J,EAAqB1oB,IAAI6oB,IAAS9J,IACnCL,GAAS,EAAIuG,EAAUC,YAAYjpB,KAAKuE,KAAKse,YAAaJ,EAAQK,IAG1E,IAAI8H,EACJ,GAAqB,kBAAV3b,GAAuBA,EAAOyV,QAAS,EAAI1P,EAAO4P,sBAAsB3V,EAAQjP,KAAKqP,OAAQ,CACpG,MAAMqV,GAAO,EAAIsE,EAAUC,YAAYjpB,KAAKuE,KAAKse,YAAaJ,EAAQxT,EAAOyV,MAC7EkG,EAAMyB,EAAchQ,KAAKrc,KAAMslB,EAAMZ,GAIzC,MAAM9B,SAAEA,GAAa5iB,KAAKuE,KAE1B,OADAqmB,EAAMA,GAAO,IAAID,EAAU,CAAE1b,OAAAA,EAAQ2T,SAAAA,EAAU0C,KAAAA,EAAM7C,OAAAA,IACjDmI,EAAI3b,SAAW2b,EAAItF,KAAKrW,OACjB2b,OADX,yWCxOerrB,GAEnB,SAASwtB,IACL,IAAK,IAAIC,EAAOC,UAAUzsB,OAAQ0sB,EAAOnrB,MAAMirB,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IACzED,EAAKC,GAAQF,UAAUE,GAG3B,GAAID,EAAK1sB,OAAS,EAAG,CACjB0sB,EAAK,GAAKA,EAAK,GAAG9qB,MAAM,GAAI,GAE5B,IADA,IAAIgrB,EAAKF,EAAK1sB,OAAS,EACdsB,EAAI,EAAGA,EAAIsrB,IAAMtrB,EACtBorB,EAAKprB,GAAKorB,EAAKprB,GAAGM,MAAM,GAAI,GAGhC,OADA8qB,EAAKE,GAAMF,EAAKE,GAAIhrB,MAAM,GACnB8qB,EAAKjrB,KAAK,IAEjB,OAAOirB,EAAK,GAGpB,SAASG,EAAOptB,GACZ,MAAO,MAAQA,EAAM,IAEzB,SAASqtB,EAAOC,GACZ,YAAa7rB,IAAN6rB,EAAkB,YAAoB,OAANA,EAAa,OAASnuB,OAAO0a,UAAU5Z,SAASmc,KAAKkR,GAAGhL,MAAM,KAAKnU,MAAMmU,MAAM,KAAKiL,QAAQC,cAEvI,SAASC,EAAYztB,GACjB,OAAOA,EAAIytB,cAef,SAASC,EAAUC,OACXC,EAAU,WAEVC,EAAU,QAEVC,EAAWhB,EAAMe,EAAS,YAI1BE,EAAeX,EAAOA,EAAO,UAAYU,EAAW,IAAMA,EAAWA,EAAW,IAAMA,EAAWA,GAAY,IAAMV,EAAO,cAAgBU,EAAW,IAAMA,EAAWA,GAAY,IAAMV,EAAO,IAAMU,EAAWA,IAGhNE,EAAe,sCACfC,EAAanB,EAFF,0BAEsBkB,GAGrCE,EAAaP,EAAQ,oBAAsB,KAE3CQ,EAAerB,EAAMc,EAASC,EAAS,iBAJvBF,EAAQ,8EAAgF,MAK1FP,EAAOQ,EAAUd,EAAMc,EAASC,EAAS,eAAiB,KACxDT,EAAOA,EAAOW,EAAe,IAAMjB,EAAMqB,EAAcH,EAAc,UAAY,SAE7FI,EAAqBhB,EAAOA,EAAO,WAAa,IAAMA,EAAO,eAAsB,IAAMA,EAAO,eAA2B,IAAMA,EAAO,gBAAuB,QAAUS,GAE7KQ,EAAejB,EAAOgB,EAAqB,MAAQA,EAAqB,MAAQA,EAAqB,MAAQA,GACzGE,EAAOlB,EAAOU,EAAW,SACzBS,EAAQnB,EAAOA,EAAOkB,EAAO,MAAQA,GAAQ,IAAMD,GACnDG,EAAgBpB,EAAOA,EAAOkB,EAAO,OAAS,MAAQC,GAE1DE,EAAgBrB,EAAO,SAAWA,EAAOkB,EAAO,OAAS,MAAQC,GAEjEG,EAAgBtB,EAAOA,EAAOkB,GAAQ,UAAYlB,EAAOkB,EAAO,OAAS,MAAQC,GAEjFI,EAAgBvB,EAAOA,EAAOA,EAAOkB,EAAO,OAAS,QAAUA,GAAQ,UAAYlB,EAAOkB,EAAO,OAAS,MAAQC,GAElHK,EAAgBxB,EAAOA,EAAOA,EAAOkB,EAAO,OAAS,QAAUA,GAAQ,UAAYlB,EAAOkB,EAAO,OAAS,MAAQC,GAElHM,EAAgBzB,EAAOA,EAAOA,EAAOkB,EAAO,OAAS,QAAUA,GAAQ,UAAYA,EAAO,MAAQC,GAElGO,EAAgB1B,EAAOA,EAAOA,EAAOkB,EAAO,OAAS,QAAUA,GAAQ,UAAYC,GAEnFQ,EAAgB3B,EAAOA,EAAOA,EAAOkB,EAAO,OAAS,QAAUA,GAAQ,UAAYA,GAEnFU,EAAgB5B,EAAOA,EAAOA,EAAOkB,EAAO,OAAS,QAAUA,GAAQ,WAEvEW,EAAe7B,EAAO,CAACoB,EAAeC,EAAeC,EAAeC,EAAeC,EAAeC,EAAeC,EAAeC,EAAeC,GAAehtB,KAAK,MAC/JktB,EAAU9B,EAAOA,EAAOe,EAAe,IAAMJ,GAAgB,KAMpDX,EAAO,OAASU,EAAW,OAAShB,EAAMqB,EAAcH,EAAc,SAAW,KAGlFZ,EAAOA,EAAOW,EAAe,IAAMjB,EAAMqB,EAAcH,IAAiB,SAIhFmB,EAAS/B,EAAOW,EAAe,IAAMjB,EAAMqB,EAAcH,EAAc,aA0B3E,OAvBqBZ,EAAOA,EAAOW,EAAe,IAAMjB,EAAMqB,EAAcH,EAAc,UAAY,KAUzFZ,EAAOA,EAAO+B,EAAS,IAAMrC,EAAM,WAAYoB,IAAe,KAapE,CACHkB,WAAY,IAAI1Q,OAAOoO,EAAM,MAAOc,EAASC,EAAS,eAAgB,KACtEwB,aAAc,IAAI3Q,OAAOoO,EAAM,YAAaqB,EAAcH,GAAe,KACzEsB,SAAU,IAAI5Q,OAAOoO,EAAM,kBAAmBqB,EAAcH,GAAe,KAC3EuB,SAAU,IAAI7Q,OAAOoO,EAAM,kBAAmBqB,EAAcH,GAAe,KAC3EwB,kBAAmB,IAAI9Q,OAAOoO,EAAM,eAAgBqB,EAAcH,GAAe,KACjFyB,UAAW,IAAI/Q,OAAOoO,EAAM,SAAUqB,EAAcH,EAAc,iBAAkBE,GAAa,KACjGwB,aAAc,IAAIhR,OAAOoO,EAAM,SAAUqB,EAAcH,EAAc,kBAAmB,KACxF2B,OAAQ,IAAIjR,OAAOoO,EAAM,MAAOqB,EAAcH,GAAe,KAC7D4B,WAAY,IAAIlR,OAAOyP,EAAc,KACrC0B,YAAa,IAAInR,OAAOoO,EAAM,SAAUqB,EAAcF,GAAa,KACnE6B,YAAa,IAAIpR,OAAOqP,EAAc,KACtCgC,YAAa,IAAIrR,OAAO,KAAO2P,EAAe,MAC9C2B,YAAa,IAAItR,OAAO,SAAWuQ,EAAe,IAAM7B,EAAOA,EAAO,eAAiBU,EAAW,QAAU,IAAMoB,EAAU,KAAO,WAG3I,IAAIe,EAAevC,GAAU,GAEzBwC,EAAexC,GAAU,GAEzByC,EA2BK,SAAUjjB,EAAKjM,GACpB,GAAIa,MAAMC,QAAQmL,GAChB,OAAOA,EACF,GAAIkjB,OAAOC,YAAYlxB,OAAO+N,GACnC,OA9BJ,SAAuBA,EAAKjM,GAC1B,IAAIqvB,EAAO,GACP5rB,GAAK,EACL6rB,GAAK,EACLC,OAAK/uB,EAET,IACE,IAAK,IAAiCgvB,EAA7BC,EAAKxjB,EAAIkjB,OAAOC,cAAmB3rB,GAAM+rB,EAAKC,EAAGC,QAAQC,QAChEN,EAAKnvB,KAAKsvB,EAAGpxB,QAET4B,GAAKqvB,EAAK/vB,SAAWU,GAH8CyD,GAAK,IAK9E,MAAOmO,GACP0d,GAAK,EACLC,EAAK3d,UAEL,KACOnO,GAAMgsB,EAAW,QAAGA,EAAW,iBAEpC,GAAIH,EAAI,MAAMC,GAIlB,OAAOF,EAOEO,CAAc3jB,EAAKjM,GAE1B,MAAM,IAAI6vB,UAAU,yDA6BtBC,EAAS,WAGTC,EAAO,GAUPC,EAAgB,QAChBC,EAAgB,aAChBC,EAAkB,4BAGlB7e,EAAS,CACZ8e,SAAY,kDACZ,YAAa,iDACb,gBAAiB,iBAKdC,EAAQpgB,KAAKogB,MACbC,EAAqBC,OAAOC,aAUhC,SAASC,EAAQ7b,GAChB,MAAM,IAAI8b,WAAWpf,EAAOsD,IA8B7B,SAAS+b,EAAU9b,EAAQ+b,GAC1B,IAAIC,EAAQhc,EAAOyM,MAAM,KACrBzG,EAAS,GAWb,OAVIgW,EAAMtxB,OAAS,IAGlBsb,EAASgW,EAAM,GAAK,IACpBhc,EAASgc,EAAM,IAMThW,EAhCR,SAAa/F,EAAO8b,GAGnB,IAFA,IAAI/V,EAAS,GACTtb,EAASuV,EAAMvV,OACZA,KACNsb,EAAOtb,GAAUqxB,EAAG9b,EAAMvV,IAE3B,OAAOsb,EAyBOtB,EAFd1E,EAASA,EAAOvT,QAAQ6uB,EAAiB,MACrB7O,MAAM,KACAsP,GAAI5vB,KAAK,KAkDpC,IAqCI8vB,EAAe,SAAsBC,EAAOC,GAG/C,OAAOD,EAAQ,GAAK,IAAMA,EAAQ,MAAgB,GAARC,IAAc,IAQrDC,EAAQ,SAAeC,EAAOC,EAAWC,GAC5C,IAAIC,EAAI,EAGR,IAFAH,EAAQE,EAAYf,EAAMa,EA7KhB,KA6KgCA,GAAS,EACnDA,GAASb,EAAMa,EAAQC,GACOD,EAAQI,IAA2BD,GAAKrB,EACrEkB,EAAQb,EAAMa,EA9JIlB,IAgKnB,OAAOK,EAAMgB,EAAI,GAAsBH,GAASA,EAnLtC,MAgdPK,EATU,SAAiBC,GAC9B,OAAOb,EAAUa,EAAO,SAAU3c,GACjC,OAAOqb,EAAcrxB,KAAKgW,GAAU,OAnLzB,SAAgB2c,GAC5B,IAAIC,EAAS,GAMTC,GAHJF,EA/LD,SAAoB3c,GAInB,IAHA,IAAI4c,EAAS,GACTE,EAAU,EACVpyB,EAASsV,EAAOtV,OACboyB,EAAUpyB,GAAQ,CACxB,IAAIlB,EAAQwW,EAAO+c,WAAWD,KAC9B,GAAItzB,GAAS,OAAUA,GAAS,OAAUszB,EAAUpyB,EAAQ,CAE3D,IAAIsyB,EAAQhd,EAAO+c,WAAWD,KACN,QAAX,MAARE,GAEJJ,EAAOtxB,OAAe,KAAR9B,IAAkB,KAAe,KAARwzB,GAAiB,QAIxDJ,EAAOtxB,KAAK9B,GACZszB,UAGDF,EAAOtxB,KAAK9B,GAGd,OAAOozB,EAyKCK,CAAWN,IAGKjyB,OAGpBuI,EA7RU,IA8RVopB,EAAQ,EACRa,EAhSa,GAmSbC,GAA4B,EAC5BC,GAAoB,EACpBC,OAAiBzxB,EAErB,IACC,IAAK,IAA0C0xB,EAAtCC,EAAYZ,EAAMpC,OAAOC,cAAsB2C,GAA6BG,EAAQC,EAAUzC,QAAQC,MAAOoC,GAA4B,EAAM,CACvJ,IAAIK,EAAiBF,EAAM9zB,MAEvBg0B,EAAiB,KACpBZ,EAAOtxB,KAAKmwB,EAAmB+B,KAGhC,MAAOxgB,GACRogB,GAAoB,EACpBC,EAAiBrgB,UAEjB,KACMmgB,GAA6BI,EAAU7lB,QAC3C6lB,EAAU7lB,iBAGX,GAAI0lB,EACH,MAAMC,GAKT,IAAII,EAAcb,EAAOlyB,OACrBgzB,EAAiBD,EAWrB,IALIA,GACHb,EAAOtxB,KApUO,KAwURoyB,EAAiBb,GAAa,CAIpC,IAAIc,EAAIzC,EACJ0C,GAA6B,EAC7BC,GAAqB,EACrBC,OAAkBlyB,EAEtB,IACC,IAAK,IAA2CmyB,EAAvCC,EAAarB,EAAMpC,OAAOC,cAAuBoD,GAA8BG,EAASC,EAAWlD,QAAQC,MAAO6C,GAA6B,EAAM,CAC7J,IAAIK,EAAeF,EAAOv0B,MAEtBy0B,GAAgBhrB,GAAKgrB,EAAeN,IACvCA,EAAIM,IAML,MAAOjhB,GACR6gB,GAAqB,EACrBC,EAAkB9gB,UAElB,KACM4gB,GAA8BI,EAAWtmB,QAC7CsmB,EAAWtmB,iBAGZ,GAAImmB,EACH,MAAMC,GAKT,IAAII,EAAwBR,EAAiB,EACzCC,EAAI1qB,EAAIuoB,GAAON,EAASmB,GAAS6B,IACpCtC,EAAQ,YAGTS,IAAUsB,EAAI1qB,GAAKirB,EACnBjrB,EAAI0qB,EAEJ,IAAIQ,GAA6B,EAC7BC,GAAqB,EACrBC,OAAkBzyB,EAEtB,IACC,IAAK,IAA2C0yB,EAAvCC,EAAa5B,EAAMpC,OAAOC,cAAuB2D,GAA8BG,EAASC,EAAWzD,QAAQC,MAAOoD,GAA6B,EAAM,CAC7J,IAAIK,EAAgBF,EAAO90B,MAK3B,GAHIg1B,EAAgBvrB,KAAOopB,EAAQnB,GAClCU,EAAQ,YAEL4C,GAAiBvrB,EAAG,CAGvB,IADA,IAAIwrB,EAAIpC,EACCG,EAAIrB,GAAyBqB,GAAKrB,EAAM,CAChD,IAAI1Z,EAAI+a,GAAKU,EAxYR,EAwYsBV,GAAKU,EAvY3B,GAAA,GAuYgDV,EAAIU,EACzD,GAAIuB,EAAIhd,EACP,MAED,IAAIid,EAAUD,EAAIhd,EACdkd,EAAaxD,EAAO1Z,EACxBmb,EAAOtxB,KAAKmwB,EAAmBQ,EAAaxa,EAAIid,EAAUC,EAAY,KACtEF,EAAIjD,EAAMkD,EAAUC,GAGrB/B,EAAOtxB,KAAKmwB,EAAmBQ,EAAawC,EAAG,KAC/CvB,EAAOd,EAAMC,EAAO6B,EAAuBR,GAAkBD,GAC7DpB,EAAQ,IACNqB,IAGH,MAAO1gB,GACRohB,GAAqB,EACrBC,EAAkBrhB,UAElB,KACMmhB,GAA8BI,EAAW7mB,QAC7C6mB,EAAW7mB,iBAGZ,GAAI0mB,EACH,MAAMC,KAKPhC,IACAppB,EAEH,OAAO2pB,EAAOzwB,KAAK,IAiC2ByyB,CAAO5e,GAAUA,KAO5D0c,EA1BY,SAAmBC,GAClC,OAAOb,EAAUa,EAAO,SAAU3c,GACjC,OAAOob,EAAcpxB,KAAKgW,GA3Pf,SAAgB2c,GAE5B,IAtDwCkC,EAsDpCjC,EAAS,GACTC,EAAcF,EAAMjyB,OACpBU,EAAI,EACJ6H,EA/LU,IAgMViqB,EAjMa,GAuMb4B,EAAQnC,EAAMoC,YArMH,KAsMXD,EAAQ,IACXA,EAAQ,GAGT,IAAK,IAAIE,EAAI,EAAGA,EAAIF,IAASE,EAExBrC,EAAMI,WAAWiC,IAAM,KAC1BpD,EAAQ,aAETgB,EAAOtxB,KAAKqxB,EAAMI,WAAWiC,IAM9B,IAAK,IAAIjxB,EAAQ+wB,EAAQ,EAAIA,EAAQ,EAAI,EAAG/wB,EAAQ8uB,GAAuC,CAQ1F,IADA,IAAIoC,EAAO7zB,EACF8zB,EAAI,EAAG1C,EAAIrB,GAAyBqB,GAAKrB,EAAM,CAEnDptB,GAAS8uB,GACZjB,EAAQ,iBAGT,IAAIM,GA9FkC2C,EA8FblC,EAAMI,WAAWhvB,MA7F5B,GAAO,GACf8wB,EAAY,GAEhBA,EAAY,GAAO,GACfA,EAAY,GAEhBA,EAAY,GAAO,GACfA,EAAY,GAEb1D,GAsFDe,GAASf,GAAQe,EAAQV,GAAON,EAAS9vB,GAAK8zB,KACjDtD,EAAQ,YAGTxwB,GAAK8wB,EAAQgD,EACb,IAAIzd,EAAI+a,GAAKU,EAhPL,EAgPmBV,GAAKU,EA/OxB,GAAA,GA+O6CV,EAAIU,EAEzD,GAAIhB,EAAQza,EACX,MAGD,IAAIkd,EAAaxD,EAAO1Z,EACpByd,EAAI1D,EAAMN,EAASyD,IACtB/C,EAAQ,YAGTsD,GAAKP,EAGN,IAAIQ,EAAMvC,EAAOlyB,OAAS,EAC1BwyB,EAAOd,EAAMhxB,EAAI6zB,EAAME,EAAa,GAARF,GAIxBzD,EAAMpwB,EAAI+zB,GAAOjE,EAASjoB,GAC7B2oB,EAAQ,YAGT3oB,GAAKuoB,EAAMpwB,EAAI+zB,GACf/zB,GAAK+zB,EAGLvC,EAAO/wB,OAAOT,IAAK,EAAG6H,GAGvB,OAAOyoB,OAAO0D,cAAcC,MAAM3D,OAAQkB,GA4KL0C,CAAOtf,EAAO1T,MAAM,GAAGqrB,eAAiB3X,KAkF1Euf,EAAU,GACd,SAASC,EAAWC,GAChB,IAAI10B,EAAI00B,EAAI1C,WAAW,GAGvB,OADIhyB,EAAI,GAAQ,KAAOA,EAAEX,SAAS,IAAIwtB,cAAuB7sB,EAAI,IAAS,IAAMA,EAAEX,SAAS,IAAIwtB,cAAuB7sB,EAAI,KAAU,KAAOA,GAAK,EAAI,KAAKX,SAAS,IAAIwtB,cAAgB,KAAW,GAAJ7sB,EAAS,KAAKX,SAAS,IAAIwtB,cAAuB,KAAO7sB,GAAK,GAAK,KAAKX,SAAS,IAAIwtB,cAAgB,KAAO7sB,GAAK,EAAI,GAAK,KAAKX,SAAS,IAAIwtB,cAAgB,KAAW,GAAJ7sB,EAAS,KAAKX,SAAS,IAAIwtB,cAG/X,SAAS8H,EAAYv1B,GAIjB,IAHA,IAAIw1B,EAAS,GACTv0B,EAAI,EACJw0B,EAAKz1B,EAAIO,OACNU,EAAIw0B,GAAI,CACX,IAAI70B,EAAI80B,SAAS11B,EAAI21B,OAAO10B,EAAI,EAAG,GAAI,IACvC,GAAIL,EAAI,IACJ40B,GAAUjE,OAAOC,aAAa5wB,GAC9BK,GAAK,OACF,GAAIL,GAAK,KAAOA,EAAI,IAAK,CAC5B,GAAI60B,EAAKx0B,GAAK,EAAG,CACb,IAAIuB,EAAKkzB,SAAS11B,EAAI21B,OAAO10B,EAAI,EAAG,GAAI,IACxCu0B,GAAUjE,OAAOC,cAAkB,GAAJ5wB,IAAW,EAAS,GAAL4B,QAE9CgzB,GAAUx1B,EAAI21B,OAAO10B,EAAG,GAE5BA,GAAK,OACF,GAAIL,GAAK,IAAK,CACjB,GAAI60B,EAAKx0B,GAAK,EAAG,CACb,IAAI20B,EAAKF,SAAS11B,EAAI21B,OAAO10B,EAAI,EAAG,GAAI,IACpC40B,EAAKH,SAAS11B,EAAI21B,OAAO10B,EAAI,EAAG,GAAI,IACxCu0B,GAAUjE,OAAOC,cAAkB,GAAJ5wB,IAAW,IAAW,GAALg1B,IAAY,EAAS,GAALC,QAEhEL,GAAUx1B,EAAI21B,OAAO10B,EAAG,GAE5BA,GAAK,OAELu0B,GAAUx1B,EAAI21B,OAAO10B,EAAG,GACxBA,GAAK,EAGb,OAAOu0B,EAEX,SAASM,EAA4BC,EAAYC,GAC7C,SAASC,EAAiBj2B,GACtB,IAAIk2B,EAASX,EAAYv1B,GACzB,OAAQk2B,EAAOC,MAAMH,EAASpG,YAAoBsG,EAANl2B,EAQhD,OANI+1B,EAAWK,SAAQL,EAAWK,OAAS7E,OAAOwE,EAAWK,QAAQ9zB,QAAQ0zB,EAASlG,YAAamG,GAAkBzI,cAAclrB,QAAQ0zB,EAAS5G,WAAY,UACpI3tB,IAAxBs0B,EAAWM,WAAwBN,EAAWM,SAAW9E,OAAOwE,EAAWM,UAAU/zB,QAAQ0zB,EAASlG,YAAamG,GAAkB3zB,QAAQ0zB,EAAS3G,aAAcgG,GAAY/yB,QAAQ0zB,EAASlG,YAAarC,SAC1LhsB,IAApBs0B,EAAWO,OAAoBP,EAAWO,KAAO/E,OAAOwE,EAAWO,MAAMh0B,QAAQ0zB,EAASlG,YAAamG,GAAkBzI,cAAclrB,QAAQ0zB,EAAS1G,SAAU+F,GAAY/yB,QAAQ0zB,EAASlG,YAAarC,SACxLhsB,IAApBs0B,EAAWQ,OAAoBR,EAAWQ,KAAOhF,OAAOwE,EAAWQ,MAAMj0B,QAAQ0zB,EAASlG,YAAamG,GAAkB3zB,QAAQyzB,EAAWK,OAASJ,EAASzG,SAAWyG,EAASxG,kBAAmB6F,GAAY/yB,QAAQ0zB,EAASlG,YAAarC,SAC1NhsB,IAArBs0B,EAAWS,QAAqBT,EAAWS,MAAQjF,OAAOwE,EAAWS,OAAOl0B,QAAQ0zB,EAASlG,YAAamG,GAAkB3zB,QAAQ0zB,EAASvG,UAAW4F,GAAY/yB,QAAQ0zB,EAASlG,YAAarC,SAC1KhsB,IAAxBs0B,EAAWrJ,WAAwBqJ,EAAWrJ,SAAW6E,OAAOwE,EAAWrJ,UAAUpqB,QAAQ0zB,EAASlG,YAAamG,GAAkB3zB,QAAQ0zB,EAAStG,aAAc2F,GAAY/yB,QAAQ0zB,EAASlG,YAAarC,IAC3MsI,EAGX,SAASU,EAAmBz2B,GACxB,OAAOA,EAAIsC,QAAQ,UAAW,OAAS,IAE3C,SAASo0B,EAAeJ,EAAMN,GAC1B,IAAIlM,EAAUwM,EAAKH,MAAMH,EAASjG,cAAgB,GAG9C4G,EADWxG,EAAcrG,EAAS,GACf,GAEvB,OAAI6M,EACOA,EAAQrU,MAAM,KAAK/H,IAAIkc,GAAoBz0B,KAAK,KAEhDs0B,EAGf,SAASM,EAAeN,EAAMN,GAC1B,IAAIlM,EAAUwM,EAAKH,MAAMH,EAAShG,cAAgB,GAE9C6G,EAAY1G,EAAcrG,EAAS,GACnC6M,EAAUE,EAAU,GACpBC,EAAOD,EAAU,GAErB,GAAIF,EAAS,CAYT,IAXA,IAAII,EAAwBJ,EAAQnJ,cAAclL,MAAM,MAAM0U,UAC1DC,EAAyB9G,EAAc4G,EAAuB,GAC9DG,EAAOD,EAAuB,GAC9BE,EAAQF,EAAuB,GAE/BG,EAAcD,EAAQA,EAAM7U,MAAM,KAAK/H,IAAIkc,GAAsB,GACjEY,EAAaH,EAAK5U,MAAM,KAAK/H,IAAIkc,GACjCa,EAAyBtB,EAASjG,YAAYlwB,KAAKw3B,EAAWA,EAAW92B,OAAS,IAClFg3B,EAAaD,EAAyB,EAAI,EAC1CE,EAAkBH,EAAW92B,OAASg3B,EACtCE,EAAS31B,MAAMy1B,GACV11B,EAAI,EAAGA,EAAI01B,IAAc11B,EAC9B41B,EAAO51B,GAAKu1B,EAAYv1B,IAAMw1B,EAAWG,EAAkB31B,IAAM,GAEjEy1B,IACAG,EAAOF,EAAa,GAAKb,EAAee,EAAOF,EAAa,GAAIvB,IAEpE,IAWI0B,EAXgBD,EAAO92B,OAAO,SAAUg3B,EAAKC,EAAOh0B,GACpD,IAAKg0B,GAAmB,MAAVA,EAAe,CACzB,IAAIC,EAAcF,EAAIA,EAAIp3B,OAAS,GAC/Bs3B,GAAeA,EAAYj0B,MAAQi0B,EAAYt3B,SAAWqD,EAC1Di0B,EAAYt3B,SAEZo3B,EAAIx2B,KAAK,CAAEyC,MAAOA,EAAOrD,OAAQ,IAGzC,OAAOo3B,GACR,IACmCG,KAAK,SAAU71B,EAAGC,GACpD,OAAOA,EAAE3B,OAAS0B,EAAE1B,SACrB,GACCw3B,OAAU,EACd,GAAIL,GAAqBA,EAAkBn3B,OAAS,EAAG,CACnD,IAAIy3B,EAAWP,EAAOt1B,MAAM,EAAGu1B,EAAkB9zB,OAC7Cq0B,EAAUR,EAAOt1B,MAAMu1B,EAAkB9zB,MAAQ8zB,EAAkBn3B,QACvEw3B,EAAUC,EAASh2B,KAAK,KAAO,KAAOi2B,EAAQj2B,KAAK,UAEnD+1B,EAAUN,EAAOz1B,KAAK,KAK1B,OAHI80B,IACAiB,GAAW,IAAMjB,GAEdiB,EAEP,OAAOzB,EAGf,IAAI4B,EAAY,kIACZC,OAAiD12B,IAAzB,GAAG00B,MAAM,SAAS,GAC9C,SAAShU,EAAMiW,GACX,IAAIC,EAAUrL,UAAUzsB,OAAS,QAAsBkB,IAAjBurB,UAAU,GAAmBA,UAAU,GAAK,GAE9E+I,EAAa,GACbC,GAA2B,IAAhBqC,EAAQC,IAAgBpI,EAAeD,EAC5B,WAAtBoI,EAAQE,YAAwBH,GAAaC,EAAQjC,OAASiC,EAAQjC,OAAS,IAAM,IAAM,KAAOgC,GACtG,IAAItO,EAAUsO,EAAUjC,MAAM+B,GAC9B,GAAIpO,EAAS,CACLqO,GAEApC,EAAWK,OAAStM,EAAQ,GAC5BiM,EAAWM,SAAWvM,EAAQ,GAC9BiM,EAAWO,KAAOxM,EAAQ,GAC1BiM,EAAWyC,KAAO9C,SAAS5L,EAAQ,GAAI,IACvCiM,EAAWQ,KAAOzM,EAAQ,IAAM,GAChCiM,EAAWS,MAAQ1M,EAAQ,GAC3BiM,EAAWrJ,SAAW5C,EAAQ,GAE1B2O,MAAM1C,EAAWyC,QACjBzC,EAAWyC,KAAO1O,EAAQ,MAK9BiM,EAAWK,OAAStM,EAAQ,SAAMroB,EAClCs0B,EAAWM,UAAuC,IAA5B+B,EAAUM,QAAQ,KAAc5O,EAAQ,QAAKroB,EACnEs0B,EAAWO,MAAoC,IAA7B8B,EAAUM,QAAQ,MAAe5O,EAAQ,QAAKroB,EAChEs0B,EAAWyC,KAAO9C,SAAS5L,EAAQ,GAAI,IACvCiM,EAAWQ,KAAOzM,EAAQ,IAAM,GAChCiM,EAAWS,OAAoC,IAA5B4B,EAAUM,QAAQ,KAAc5O,EAAQ,QAAKroB,EAChEs0B,EAAWrJ,UAAuC,IAA5B0L,EAAUM,QAAQ,KAAc5O,EAAQ,QAAKroB,EAE/Dg3B,MAAM1C,EAAWyC,QACjBzC,EAAWyC,KAAOJ,EAAUjC,MAAM,iCAAmCrM,EAAQ,QAAKroB,IAGtFs0B,EAAWO,OAEXP,EAAWO,KAAOM,EAAeF,EAAeX,EAAWO,KAAMN,GAAWA,IAM5ED,EAAWwC,eAHW92B,IAAtBs0B,EAAWK,aAAgD30B,IAAxBs0B,EAAWM,eAA8C50B,IAApBs0B,EAAWO,WAA0C70B,IAApBs0B,EAAWyC,MAAuBzC,EAAWQ,WAA6B90B,IAArBs0B,EAAWS,WAE5I/0B,IAAtBs0B,EAAWK,OACK,gBACQ30B,IAAxBs0B,EAAWrJ,SACK,WAEA,MANA,gBASvB2L,EAAQE,WAAmC,WAAtBF,EAAQE,WAA0BF,EAAQE,YAAcxC,EAAWwC,YACxFxC,EAAWrtB,MAAQqtB,EAAWrtB,OAAS,gBAAkB2vB,EAAQE,UAAY,eAGjF,IAAII,EAAgBvD,GAASiD,EAAQjC,QAAUL,EAAWK,QAAU,IAAI5I,eAExE,GAAK6K,EAAQO,gBAAoBD,GAAkBA,EAAcC,eAc7D9C,EAA4BC,EAAYC,OAdsC,CAE9E,GAAID,EAAWO,OAAS+B,EAAQQ,YAAcF,GAAiBA,EAAcE,YAEzE,IACI9C,EAAWO,KAAO/D,EAAiBwD,EAAWO,KAAKh0B,QAAQ0zB,EAASlG,YAAayF,GAAa/H,eAChG,MAAO/jB,GACLssB,EAAWrtB,MAAQqtB,EAAWrtB,OAAS,kEAAoEe,EAInHqsB,EAA4BC,EAAY9F,GAMxC0I,GAAiBA,EAAcxW,OAC/BwW,EAAcxW,MAAM4T,EAAYsC,QAGpCtC,EAAWrtB,MAAQqtB,EAAWrtB,OAAS,yBAE3C,OAAOqtB,EAGX,SAAS+C,EAAoB/C,EAAYsC,GACrC,IAAIrC,GAA2B,IAAhBqC,EAAQC,IAAgBpI,EAAeD,EAClD8I,EAAY,GAehB,YAd4Bt3B,IAAxBs0B,EAAWM,WACX0C,EAAU53B,KAAK40B,EAAWM,UAC1B0C,EAAU53B,KAAK,WAEKM,IAApBs0B,EAAWO,MAEXyC,EAAU53B,KAAKy1B,EAAeF,EAAenF,OAAOwE,EAAWO,MAAON,GAAWA,GAAU1zB,QAAQ0zB,EAAShG,YAAa,SAAUlvB,EAAGk4B,EAAIC,GACtI,MAAO,IAAMD,GAAMC,EAAK,MAAQA,EAAK,IAAM,OAGpB,iBAApBlD,EAAWyC,MAAgD,iBAApBzC,EAAWyC,OACzDO,EAAU53B,KAAK,KACf43B,EAAU53B,KAAKowB,OAAOwE,EAAWyC,QAE9BO,EAAUx4B,OAASw4B,EAAU/2B,KAAK,SAAMP,EAGnD,IAAIy3B,EAAO,WACPC,EAAO,cACPC,EAAO,gBACPC,EAAO,yBACX,SAASC,EAAkB9G,GAEvB,IADA,IAAIC,EAAS,GACND,EAAMjyB,QACT,GAAIiyB,EAAM2D,MAAM+C,GACZ1G,EAAQA,EAAMlwB,QAAQ42B,EAAM,SACzB,GAAI1G,EAAM2D,MAAMgD,GACnB3G,EAAQA,EAAMlwB,QAAQ62B,EAAM,UACzB,GAAI3G,EAAM2D,MAAMiD,GACnB5G,EAAQA,EAAMlwB,QAAQ82B,EAAM,KAC5B3G,EAAOtkB,WACJ,GAAc,MAAVqkB,GAA2B,OAAVA,EACxBA,EAAQ,OACL,CACH,IAAI+G,EAAK/G,EAAM2D,MAAMkD,GACrB,IAAIE,EAKA,MAAM,IAAIz5B,MAAM,oCAJhB,IAAIJ,EAAI65B,EAAG,GACX/G,EAAQA,EAAMrwB,MAAMzC,EAAEa,QACtBkyB,EAAOtxB,KAAKzB,GAMxB,OAAO+yB,EAAOzwB,KAAK,IAGvB,SAASqgB,EAAU0T,GACf,IAAIsC,EAAUrL,UAAUzsB,OAAS,QAAsBkB,IAAjBurB,UAAU,GAAmBA,UAAU,GAAK,GAE9EgJ,EAAWqC,EAAQC,IAAMpI,EAAeD,EACxC8I,EAAY,GAEZJ,EAAgBvD,GAASiD,EAAQjC,QAAUL,EAAWK,QAAU,IAAI5I,eAGxE,GADImL,GAAiBA,EAActW,WAAWsW,EAActW,UAAU0T,EAAYsC,GAC9EtC,EAAWO,KAEX,GAAIN,EAAShG,YAAYnwB,KAAKk2B,EAAWO,YAIpC,GAAI+B,EAAQQ,YAAcF,GAAiBA,EAAcE,WAEtD,IACI9C,EAAWO,KAAQ+B,EAAQC,IAAmG/F,EAAmBwD,EAAWO,MAA3H/D,EAAiBwD,EAAWO,KAAKh0B,QAAQ0zB,EAASlG,YAAayF,GAAa/H,eAC/G,MAAO/jB,GACLssB,EAAWrtB,MAAQqtB,EAAWrtB,OAAS,+CAAkD2vB,EAAQC,IAAgB,UAAV,SAAuB,kBAAoB7uB,EAKlKqsB,EAA4BC,EAAYC,GACd,WAAtBqC,EAAQE,WAA0BxC,EAAWK,SAC7C2C,EAAU53B,KAAK40B,EAAWK,QAC1B2C,EAAU53B,KAAK,MAEnB,IAAIq4B,EAAYV,EAAoB/C,EAAYsC,GAUhD,QATkB52B,IAAd+3B,IAC0B,WAAtBnB,EAAQE,WACRQ,EAAU53B,KAAK,MAEnB43B,EAAU53B,KAAKq4B,GACXzD,EAAWQ,MAAsC,MAA9BR,EAAWQ,KAAKkD,OAAO,IAC1CV,EAAU53B,KAAK,WAGCM,IAApBs0B,EAAWQ,KAAoB,CAC/B,IAAI72B,EAAIq2B,EAAWQ,KACd8B,EAAQqB,cAAkBf,GAAkBA,EAAce,eAC3Dh6B,EAAI45B,EAAkB55B,SAER+B,IAAd+3B,IACA95B,EAAIA,EAAE4C,QAAQ,QAAS,SAE3By2B,EAAU53B,KAAKzB,GAUnB,YARyB+B,IAArBs0B,EAAWS,QACXuC,EAAU53B,KAAK,KACf43B,EAAU53B,KAAK40B,EAAWS,aAEF/0B,IAAxBs0B,EAAWrJ,WACXqM,EAAU53B,KAAK,KACf43B,EAAU53B,KAAK40B,EAAWrJ,WAEvBqM,EAAU/2B,KAAK,IAG1B,SAAS23B,EAAkB3I,EAAM4I,GAC7B,IAAIvB,EAAUrL,UAAUzsB,OAAS,QAAsBkB,IAAjBurB,UAAU,GAAmBA,UAAU,GAAK,GAG9E6M,EAAS,GAqDb,OAvDwB7M,UAAU,KAI9BgE,EAAO7O,EAAME,EAAU2O,EAAMqH,GAAUA,GACvCuB,EAAWzX,EAAME,EAAUuX,EAAUvB,GAAUA,MAEnDA,EAAUA,GAAW,IACRyB,UAAYF,EAASxD,QAC9ByD,EAAOzD,OAASwD,EAASxD,OAEzByD,EAAOxD,SAAWuD,EAASvD,SAC3BwD,EAAOvD,KAAOsD,EAAStD,KACvBuD,EAAOrB,KAAOoB,EAASpB,KACvBqB,EAAOtD,KAAO+C,EAAkBM,EAASrD,MAAQ,IACjDsD,EAAOrD,MAAQoD,EAASpD,aAEE/0B,IAAtBm4B,EAASvD,eAA4C50B,IAAlBm4B,EAAStD,WAAwC70B,IAAlBm4B,EAASpB,MAE3EqB,EAAOxD,SAAWuD,EAASvD,SAC3BwD,EAAOvD,KAAOsD,EAAStD,KACvBuD,EAAOrB,KAAOoB,EAASpB,KACvBqB,EAAOtD,KAAO+C,EAAkBM,EAASrD,MAAQ,IACjDsD,EAAOrD,MAAQoD,EAASpD,QAEnBoD,EAASrD,MAQsB,MAA5BqD,EAASrD,KAAKkD,OAAO,GACrBI,EAAOtD,KAAO+C,EAAkBM,EAASrD,OAOrCsD,EAAOtD,UALY90B,IAAlBuvB,EAAKqF,eAAwC50B,IAAduvB,EAAKsF,WAAoC70B,IAAduvB,EAAKwH,MAAwBxH,EAAKuF,KAErFvF,EAAKuF,KAGCvF,EAAKuF,KAAKp0B,MAAM,EAAG6uB,EAAKuF,KAAK3B,YAAY,KAAO,GAAKgF,EAASrD,KAF9DqD,EAASrD,KAFT,IAAMqD,EAASrD,KAMjCsD,EAAOtD,KAAO+C,EAAkBO,EAAOtD,OAE3CsD,EAAOrD,MAAQoD,EAASpD,QAnBxBqD,EAAOtD,KAAOvF,EAAKuF,KAEfsD,EAAOrD,WADY/0B,IAAnBm4B,EAASpD,MACMoD,EAASpD,MAETxF,EAAKwF,OAkB5BqD,EAAOxD,SAAWrF,EAAKqF,SACvBwD,EAAOvD,KAAOtF,EAAKsF,KACnBuD,EAAOrB,KAAOxH,EAAKwH,MAEvBqB,EAAOzD,OAASpF,EAAKoF,QAEzByD,EAAOnN,SAAWkN,EAASlN,SACpBmN,EAmCX,SAASE,EAAkB/5B,EAAKq4B,GAC5B,OAAOr4B,GAAOA,EAAIC,WAAWqC,QAAS+1B,GAAYA,EAAQC,IAAiCpI,EAAaJ,YAAxCG,EAAaH,YAAwCyF,GAGzH,IAAIyE,EAAU,CACV5D,OAAQ,OACRyC,YAAY,EACZ1W,MAAO,SAAe4T,EAAYsC,GAK9B,OAHKtC,EAAWO,OACZP,EAAWrtB,MAAQqtB,EAAWrtB,OAAS,+BAEpCqtB,GAEX1T,UAAW,SAAmB0T,EAAYsC,GACtC,IAAI4B,EAAqD,UAA5C1I,OAAOwE,EAAWK,QAAQ5I,cAYvC,OAVIuI,EAAWyC,QAAUyB,EAAS,IAAM,KAA2B,KAApBlE,EAAWyC,OACtDzC,EAAWyC,UAAO/2B,GAGjBs0B,EAAWQ,OACZR,EAAWQ,KAAO,KAKfR,IAIXmE,EAAY,CACZ9D,OAAQ,QACRyC,WAAYmB,EAAQnB,WACpB1W,MAAO6X,EAAQ7X,MACfE,UAAW2X,EAAQ3X,WAGvB,SAAS8X,EAASC,GACd,MAAsC,kBAAxBA,EAAaH,OAAuBG,EAAaH,OAAuD,QAA9C1I,OAAO6I,EAAahE,QAAQ5I,cAGxG,IAAI6M,EAAY,CACZjE,OAAQ,KACRyC,YAAY,EACZ1W,MAAO,SAAe4T,EAAYsC,GAC9B,IAAI+B,EAAerE,EAOnB,OALAqE,EAAaH,OAASE,EAASC,GAE/BA,EAAaE,cAAgBF,EAAa7D,MAAQ,MAAQ6D,EAAa5D,MAAQ,IAAM4D,EAAa5D,MAAQ,IAC1G4D,EAAa7D,UAAO90B,EACpB24B,EAAa5D,WAAQ/0B,EACd24B,GAEX/X,UAAW,SAAmB+X,EAAc/B,GAWxC,GATI+B,EAAa5B,QAAU2B,EAASC,GAAgB,IAAM,KAA6B,KAAtBA,EAAa5B,OAC1E4B,EAAa5B,UAAO/2B,GAGW,kBAAxB24B,EAAaH,SACpBG,EAAahE,OAASgE,EAAaH,OAAS,MAAQ,KACpDG,EAAaH,YAASx4B,GAGtB24B,EAAaE,aAAc,CAC3B,IAAIC,EAAwBH,EAAaE,aAAahY,MAAM,KACxDkY,EAAyBrK,EAAcoK,EAAuB,GAC9DhE,EAAOiE,EAAuB,GAC9BhE,EAAQgE,EAAuB,GAEnCJ,EAAa7D,KAAOA,GAAiB,MAATA,EAAeA,OAAO90B,EAClD24B,EAAa5D,MAAQA,EACrB4D,EAAaE,kBAAe74B,EAIhC,OADA24B,EAAa1N,cAAWjrB,EACjB24B,IAIXK,EAAY,CACZrE,OAAQ,MACRyC,WAAYwB,EAAUxB,WACtB1W,MAAOkY,EAAUlY,MACjBE,UAAWgY,EAAUhY,WAGrBqY,EAAI,GAGJvM,EAAe,mGACfL,EAAW,cACXC,EAAeX,EAAOA,EAAO,sBAA6BU,EAAWA,EAAW,IAAMA,EAAWA,GAAY,IAAMV,EAAO,0BAAiCU,EAAWA,GAAY,IAAMV,EAAO,IAAMU,EAAWA,IAchN6M,EAAU7N,EADA,6DACe,aAEzB8C,EAAa,IAAIlR,OAAOyP,EAAc,KACtC2B,EAAc,IAAIpR,OAAOqP,EAAc,KACvC6M,GAAiB,IAAIlc,OAAOoO,EAAM,MANxB,wDAMwC,QAAS,QAAS6N,GAAU,KAC9EE,GAAa,IAAInc,OAAOoO,EAAM,MAAOqB,EAJrB,uCAImD,KACnE2M,GAAcD,GAClB,SAAS5E,GAAiBj2B,GACtB,IAAIk2B,EAASX,EAAYv1B,GACzB,OAAQk2B,EAAOC,MAAMvG,GAAoBsG,EAANl2B,EAEvC,IAAI+6B,GAAY,CACZ3E,OAAQ,SACRjU,MAAO,SAAkB4T,EAAYsC,GACjC,IAAI2C,EAAmBjF,EACnB9rB,EAAK+wB,EAAiB/wB,GAAK+wB,EAAiBzE,KAAOyE,EAAiBzE,KAAKjU,MAAM,KAAO,GAE1F,GADA0Y,EAAiBzE,UAAO90B,EACpBu5B,EAAiBxE,MAAO,CAIxB,IAHA,IAAIyE,GAAiB,EACjBC,EAAU,GACVC,EAAUH,EAAiBxE,MAAMlU,MAAM,KAClCzgB,EAAI,EAAGsrB,EAAKgO,EAAQ56B,OAAQsB,EAAIsrB,IAAMtrB,EAAG,CAC9C,IAAIu5B,EAASD,EAAQt5B,GAAGygB,MAAM,KAC9B,OAAQ8Y,EAAO,IACX,IAAK,KAED,IADA,IAAIC,EAAUD,EAAO,GAAG9Y,MAAM,KACrBgZ,EAAK,EAAGC,EAAMF,EAAQ96B,OAAQ+6B,EAAKC,IAAOD,EAC/CrxB,EAAG9I,KAAKk6B,EAAQC,IAEpB,MACJ,IAAK,UACDN,EAAiBQ,QAAUzB,EAAkBqB,EAAO,GAAI/C,GACxD,MACJ,IAAK,OACD2C,EAAiBjtB,KAAOgsB,EAAkBqB,EAAO,GAAI/C,GACrD,MACJ,QACI4C,GAAiB,EACjBC,EAAQnB,EAAkBqB,EAAO,GAAI/C,IAAY0B,EAAkBqB,EAAO,GAAI/C,IAItF4C,IAAgBD,EAAiBE,QAAUA,GAEnDF,EAAiBxE,WAAQ/0B,EACzB,IAAK,IAAIg6B,EAAM,EAAGC,EAAOzxB,EAAG1J,OAAQk7B,EAAMC,IAAQD,EAAK,CACnD,IAAIE,EAAO1xB,EAAGwxB,GAAKnZ,MAAM,KAEzB,GADAqZ,EAAK,GAAK5B,EAAkB4B,EAAK,IAC5BtD,EAAQO,eAQT+C,EAAK,GAAK5B,EAAkB4B,EAAK,GAAItD,GAAS7K,mBAN9C,IACImO,EAAK,GAAKpJ,EAAiBwH,EAAkB4B,EAAK,GAAItD,GAAS7K,eACjE,MAAO/jB,GACLuxB,EAAiBtyB,MAAQsyB,EAAiBtyB,OAAS,2EAA6Ee,EAKxIQ,EAAGwxB,GAAOE,EAAK35B,KAAK,KAExB,OAAOg5B,GAEX3Y,UAAW,SAAsB2Y,EAAkB3C,GAC/C,IA3wCSjrB,EA2wCL2oB,EAAaiF,EACb/wB,EA3wCDmD,OADMA,EA4wCQ4tB,EAAiB/wB,IA3wCKmD,aAAetL,MAAQsL,EAA4B,iBAAfA,EAAI7M,QAAuB6M,EAAIkV,OAASlV,EAAIwuB,aAAexuB,EAAIgP,KAAO,CAAChP,GAAOtL,MAAM+X,UAAU1X,MAAMia,KAAKhP,GAAO,GA4wC3L,GAAInD,EAAI,CACJ,IAAK,IAAIpI,EAAI,EAAGsrB,EAAKljB,EAAG1J,OAAQsB,EAAIsrB,IAAMtrB,EAAG,CACzC,IAAIg6B,EAAStK,OAAOtnB,EAAGpI,IACnBi6B,EAAQD,EAAOjH,YAAY,KAC3BmH,EAAYF,EAAO15B,MAAM,EAAG25B,GAAOx5B,QAAQwtB,EAAamG,IAAkB3zB,QAAQwtB,EAAarC,GAAanrB,QAAQs4B,GAAgBvF,GACpI2G,EAASH,EAAO15B,MAAM25B,EAAQ,GAElC,IACIE,EAAU3D,EAAQC,IAA2E/F,EAAmByJ,GAAxFzJ,EAAiBwH,EAAkBiC,EAAQ3D,GAAS7K,eAC9E,MAAO/jB,GACLssB,EAAWrtB,MAAQqtB,EAAWrtB,OAAS,wDAA2D2vB,EAAQC,IAAgB,UAAV,SAAuB,kBAAoB7uB,EAE/JQ,EAAGpI,GAAKk6B,EAAY,IAAMC,EAE9BjG,EAAWQ,KAAOtsB,EAAGjI,KAAK,KAE9B,IAAIk5B,EAAUF,EAAiBE,QAAUF,EAAiBE,SAAW,GACjEF,EAAiBQ,UAASN,EAAiB,QAAIF,EAAiBQ,SAChER,EAAiBjtB,OAAMmtB,EAAc,KAAIF,EAAiBjtB,MAC9D,IAAI0pB,EAAS,GACb,IAAK,IAAI70B,KAAQs4B,EACTA,EAAQt4B,KAAU83B,EAAE93B,IACpB60B,EAAOt2B,KAAKyB,EAAKN,QAAQwtB,EAAamG,IAAkB3zB,QAAQwtB,EAAarC,GAAanrB,QAAQu4B,GAAYxF,GAAc,IAAM6F,EAAQt4B,GAAMN,QAAQwtB,EAAamG,IAAkB3zB,QAAQwtB,EAAarC,GAAanrB,QAAQw4B,GAAazF,IAMtP,OAHIoC,EAAOl3B,SACPw1B,EAAWS,MAAQiB,EAAOz1B,KAAK,MAE5B+zB,IAIXkG,GAAY,kBAEZC,GAAY,CACZ9F,OAAQ,MACRjU,MAAO,SAAkB4T,EAAYsC,GACjC,IAAIvO,EAAUiM,EAAWQ,MAAQR,EAAWQ,KAAKJ,MAAM8F,IACnDE,EAAgBpG,EACpB,GAAIjM,EAAS,CACT,IAAIsM,EAASiC,EAAQjC,QAAU+F,EAAc/F,QAAU,MACnDgG,EAAMtS,EAAQ,GAAG0D,cACjB6O,EAAMvS,EAAQ,GAEd6O,EAAgBvD,EADJgB,EAAS,KAAOiC,EAAQ+D,KAAOA,IAE/CD,EAAcC,IAAMA,EACpBD,EAAcE,IAAMA,EACpBF,EAAc5F,UAAO90B,EACjBk3B,IACAwD,EAAgBxD,EAAcxW,MAAMga,EAAe9D,SAGvD8D,EAAczzB,MAAQyzB,EAAczzB,OAAS,yBAEjD,OAAOyzB,GAEX9Z,UAAW,SAAsB8Z,EAAe9D,GAC5C,IACI+D,EAAMD,EAAcC,IAEpBzD,EAAgBvD,GAHPiD,EAAQjC,QAAU+F,EAAc/F,QAAU,OAE9B,KAAOiC,EAAQ+D,KAAOA,IAE3CzD,IACAwD,EAAgBxD,EAActW,UAAU8Z,EAAe9D,IAE3D,IAAIiE,EAAgBH,EAGpB,OADAG,EAAc/F,MAAQ6F,GAAO/D,EAAQ+D,KAAO,IADlCD,EAAcE,IAEjBC,IAIXC,GAAO,2DAEPC,GAAY,CACZpG,OAAQ,WACRjU,MAAO,SAAega,EAAe9D,GACjC,IAAIoE,EAAiBN,EAMrB,OALAM,EAAeC,KAAOD,EAAeJ,IACrCI,EAAeJ,SAAM56B,EAChB42B,EAAQyB,UAAc2C,EAAeC,MAASD,EAAeC,KAAKvG,MAAMoG,MACzEE,EAAe/zB,MAAQ+zB,EAAe/zB,OAAS,sBAE5C+zB,GAEXpa,UAAW,SAAmBoa,EAAgBpE,GAC1C,IAAI8D,EAAgBM,EAGpB,OADAN,EAAcE,KAAOI,EAAeC,MAAQ,IAAIlP,cACzC2O,IAIf/G,EAAQ4E,EAAQ5D,QAAU4D,EAC1B5E,EAAQ8E,EAAU9D,QAAU8D,EAC5B9E,EAAQiF,EAAUjE,QAAUiE,EAC5BjF,EAAQqF,EAAUrE,QAAUqE,EAC5BrF,EAAQ2F,GAAU3E,QAAU2E,GAC5B3F,EAAQ8G,GAAU9F,QAAU8F,GAC5B9G,EAAQoH,GAAUpG,QAAUoG,GAE5Bl9B,EAAQ81B,QAAUA,EAClB91B,EAAQ+1B,WAAaA,EACrB/1B,EAAQi2B,YAAcA,EACtBj2B,EAAQ6iB,MAAQA,EAChB7iB,EAAQg6B,kBAAoBA,EAC5Bh6B,EAAQ+iB,UAAYA,EACpB/iB,EAAQq6B,kBAAoBA,EAC5Br6B,EAAQmjB,QAxTR,SAAiBka,EAASC,EAAavE,GACnC,IAAIwE,EA9jCR,SAAgBhD,EAAQlb,GACpB,IAAIvR,EA6jC2B,CAAEgpB,OAAQ,QA5jCzC,GAAIzX,EACA,IAAK,IAAIlc,KAAOkc,EACZvR,EAAI3K,GAAOkc,EAAOlc,GAG1B,OAAO2K,EAujCiBrB,CAAO,EAAoBssB,GACnD,OAAOhW,EAAUsX,EAAkBxX,EAAMwa,EAASE,GAAoB1a,EAAMya,EAAaC,GAAoBA,GAAmB,GAAOA,IAuT3Iv9B,EAAQ2iB,UApTR,SAAmB6a,EAAKzE,GAMpB,MALmB,iBAARyE,EACPA,EAAMza,EAAUF,EAAM2a,EAAKzE,GAAUA,GACd,WAAhBhL,EAAOyP,KACdA,EAAM3a,EAAME,EAAUya,EAAKzE,GAAUA,IAElCyE,GA+SXx9B,EAAQmf,MA5SR,SAAese,EAAMC,EAAM3E,GAWvB,MAVoB,iBAAT0E,EACPA,EAAO1a,EAAUF,EAAM4a,EAAM1E,GAAUA,GACf,WAAjBhL,EAAO0P,KACdA,EAAO1a,EAAU0a,EAAM1E,IAEP,iBAAT2E,EACPA,EAAO3a,EAAUF,EAAM6a,EAAM3E,GAAUA,GACf,WAAjBhL,EAAO2P,KACdA,EAAO3a,EAAU2a,EAAM3E,IAEpB0E,IAASC,GAkSpB19B,EAAQ29B,gBA/RR,SAAyBj9B,EAAKq4B,GAC1B,OAAOr4B,GAAOA,EAAIC,WAAWqC,QAAS+1B,GAAYA,EAAQC,IAA4BpI,EAAaP,OAAnCM,EAAaN,OAA8B0F,IA+R/G/1B,EAAQy6B,kBAAoBA,EAE5B56B,OAAOC,eAAeE,EAAS,aAAc,CAAED,OAAO,IA75CU69B,CAAQ59B,KCCxEw9B,EAAIz8B,KAAO,0CACX,kDAAkBy8B,8CCHlB39B,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,UAAkBA,OAAeA,MAAcA,YAAoBA,MAAcA,IAAYA,kBAAqB,EAElHH,OAAOC,eAAeE,EAAS,aAAc,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOumB,EAAWpE,cAEtG7nB,OAAOC,eAAeE,EAAS,IAAK,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOmL,EAAUlP,KAC5F3B,OAAOC,eAAeE,EAAS,MAAO,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOmL,EAAUhQ,OAC9Fb,OAAOC,eAAeE,EAAS,YAAa,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOmL,EAAU3N,aACpGlD,OAAOC,eAAeE,EAAS,MAAO,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOmL,EAAUpL,OAC9FzF,OAAOC,eAAeE,EAAS,OAAQ,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOmL,EAAUxQ,QAC/FL,OAAOC,eAAeE,EAAS,UAAW,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOmL,EAAU+a,WAKlG,MAAMoS,EAAYntB,EAMZotB,EAAgB,CAACp9B,EAAK4e,IAAU,IAAIF,OAAO1e,EAAK4e,GACtDwe,EAAc/8B,KAAO,aACrB,MAAMg9B,EAAsB,CAAC,mBAAoB,cAAe,eAC1DC,EAAkB,IAAI9xB,IAAI,CAC5B,WACA,YACA,QACA,UACA,OACA,SACA,UACA,UACA,UACA,gBACA,OACA,MACA,UAEE+xB,EAAiB,CACnBC,cAAe,GACfxc,OAAQ,gDACR9J,SAAU,8CACVumB,aAAc,mDACdC,WAAY,wDACZC,YAAa,sEACbC,YAAa,oEACb1S,WAAY,oCACZ2S,eAAgB,0CAChBC,eAAgB,0CAChB3c,YAAa,6CACb4c,eAAgB,+EAChBC,MAAO,8CACP3b,UAAW,8CACX4b,UAAW,sBAETC,EAAoB,CACtBxZ,sBAAuB,GACvB/S,iBAAkB,GAClBwsB,QAAS,sEAIb,SAASC,EAAgB9Q,GACrB,IAAI7sB,EAAIoD,EAAI+xB,EAAIrF,EAAIC,EAAI6N,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAItO,EAAIuO,EAAIC,EAAIC,EAAIC,EAAI7D,EAAI8D,EAAIC,EAAIC,EACpG,MAAM5/B,EAAI4tB,EAAEiS,OACNC,EAA0B,QAAjB/+B,EAAK6sB,EAAEjtB,YAAyB,IAAPI,OAAgB,EAASA,EAAGkB,SAC9DA,GAAqB,IAAV69B,QAA4B/9B,IAAV+9B,EAAsB,EAAIA,GAAS,EAChEvkB,EAAiF,QAAvE2a,EAAuB,QAAjB/xB,EAAKypB,EAAEjtB,YAAyB,IAAPwD,OAAgB,EAASA,EAAGoX,cAA2B,IAAP2a,EAAgBA,EAAKwH,EAC9Gxa,EAAuC,QAAxB2N,EAAKjD,EAAE1K,mBAAgC,IAAP2N,EAAgBA,EAAKkP,EAAM1sB,QAChF,MAAO,CACH7D,aAAkF,QAAnEmvB,EAA+B,QAAzB7N,EAAKlD,EAAEpe,oBAAiC,IAAPshB,EAAgBA,EAAK9wB,SAAsB,IAAP2+B,GAAgBA,EAC1GvmB,cAAoF,QAApEymB,EAAgC,QAA1BD,EAAKhR,EAAExV,qBAAkC,IAAPwmB,EAAgBA,EAAK5+B,SAAsB,IAAP6+B,GAAgBA,EAC5G7Y,YAAgF,QAAlE+Y,EAA8B,QAAxBD,EAAKlR,EAAE5H,mBAAgC,IAAP8Y,EAAgBA,EAAK9+B,SAAsB,IAAP++B,EAAgBA,EAAK,MAC7GiB,aAAkF,QAAnEf,EAA+B,QAAzBD,EAAKpR,EAAEoS,oBAAiC,IAAPhB,EAAgBA,EAAKh/B,SAAsB,IAAPi/B,EAAgBA,EAAK,MAC/GgB,eAAsF,QAArEd,EAAiC,QAA3BD,EAAKtR,EAAEqS,sBAAmC,IAAPf,EAAgBA,EAAKl/B,SAAsB,IAAPm/B,GAAgBA,EAC9Gx+B,KAAMitB,EAAEjtB,KAAO,IAAKitB,EAAEjtB,KAAMsB,SAAAA,EAAUsZ,OAAAA,GAAW,CAAEtZ,SAAAA,EAAUsZ,OAAAA,GAC7D2kB,aAAwC,QAAzBd,EAAKxR,EAAEsS,oBAAiC,IAAPd,EAAgBA,EAhBjD,IAiBfe,SAAgC,QAArBd,EAAKzR,EAAEuS,gBAA6B,IAAPd,EAAgBA,EAjBzC,IAkBftZ,KAAwB,QAAjBgL,EAAKnD,EAAE7H,YAAyB,IAAPgL,GAAgBA,EAChD9b,SAAgC,QAArBqqB,EAAK1R,EAAE3Y,gBAA6B,IAAPqqB,GAAgBA,EACxDjT,WAAoC,QAAvBkT,EAAK3R,EAAEvB,kBAA+B,IAAPkT,GAAgBA,EAC5Dtc,SAAgC,QAArBuc,EAAK5R,EAAE3K,gBAA6B,IAAPuc,EAAgBA,EAAK,MAC7DY,cAA0C,QAA1BX,EAAK7R,EAAEwS,qBAAkC,IAAPX,GAAgBA,EAClE7iB,eAA4C,QAA3Bgf,EAAKhO,EAAEhR,sBAAmC,IAAPgf,GAAgBA,EACpEyE,gBAA8C,QAA5BX,EAAK9R,EAAEyS,uBAAoC,IAAPX,GAAgBA,EACtEpkB,cAA0C,QAA1BqkB,EAAK/R,EAAEtS,qBAAkC,IAAPqkB,GAAgBA,EAClEW,WAAoC,QAAvBV,EAAKhS,EAAE0S,kBAA+B,IAAPV,GAAgBA,EAC5D1c,YAAaA,GAGrB,MAAMqd,EACFxgC,YAAY6E,EAAO,IACfvE,KAAKosB,QAAU,GACfpsB,KAAKwjB,KAAO,GACZxjB,KAAKmgC,QAAU,GACfngC,KAAKorB,cAAgB,IAAI3f,IACzBzL,KAAKogC,SAAW,GAChBpgC,KAAKqgC,OAAS,IAAIl7B,IAClBZ,EAAOvE,KAAKuE,KAAO,IAAKA,KAAS85B,EAAgB95B,IACjD,MAAM2B,IAAEA,EAAGtB,MAAEA,GAAU5E,KAAKuE,KAAKjE,KACjCN,KAAK0E,MAAQ,IAAI04B,EAAUx2B,WAAW,CAAElC,MAAO,GAAItB,SAAUm6B,EAAiBr3B,IAAAA,EAAKtB,MAAAA,IACnF5E,KAAKwR,OAobb,SAAmBA,GACf,IAAe,IAAXA,EACA,OAAO8uB,EACX,QAAe5+B,IAAX8P,EACA,OAAO+uB,QACX,GAAI/uB,EAAOgvB,KAAOhvB,EAAOC,MAAQD,EAAO7I,MACpC,OAAO6I,EACX,MAAM,IAAIzR,MAAM,qDA3bE0gC,CAAUl8B,EAAKiN,QAC7B,MAAMkvB,EAAYn8B,EAAKy7B,gBACvBz7B,EAAKy7B,iBAAkB,EACvBhgC,KAAKqP,OAAQ,EAAI2H,EAAQ2pB,YACzBC,EAAavkB,KAAKrc,KAAMw9B,EAAgBj5B,EAAM,iBAC9Cq8B,EAAavkB,KAAKrc,KAAMm+B,EAAmB55B,EAAM,aAAc,QAC/DvE,KAAK6gC,UAAYC,EAAqBzkB,KAAKrc,MACvCuE,EAAK47B,SACLY,EAAkB1kB,KAAKrc,MAC3BA,KAAKghC,mBACLhhC,KAAKihC,wBACD18B,EAAK+K,UACL4xB,EAAmB7kB,KAAKrc,KAAMuE,EAAK+K,UACf,iBAAb/K,EAAKmhB,MACZ1lB,KAAKmhC,cAAc58B,EAAKmhB,MAC5B0b,EAAkB/kB,KAAKrc,MACvBuE,EAAKy7B,gBAAkBU,EAE3BM,mBACIhhC,KAAKqhC,WAAW,UAEpBJ,wBACI,MAAMpwB,MAAEA,EAAK6U,KAAEA,EAAI9C,SAAEA,GAAa5iB,KAAKuE,KACvC,IAAI+8B,EAAiBC,EACJ,OAAb3e,IACA0e,EAAiB,IAAKC,GACtBD,EAAerf,GAAKqf,EAAeE,WAC5BF,EAAeE,KAEtB9b,GAAQ7U,GACR7Q,KAAKmhC,cAAcG,EAAgBA,EAAe1e,IAAW,GAErE6e,cACI,MAAM/b,KAAEA,EAAI9C,SAAEA,GAAa5iB,KAAKuE,KAChC,OAAQvE,KAAKuE,KAAKk9B,YAA6B,iBAAR/b,EAAmBA,EAAK9C,IAAa8C,OAAOhkB,EAEvFgb,SAASglB,EACT3vB,GAEI,IAAI4vB,EACJ,GAA2B,iBAAhBD,GAEP,GADAC,EAAI3hC,KAAK4hC,UAAUF,IACdC,EACD,MAAM,IAAI5hC,MAAM,8BAA8B2hC,WAGlDC,EAAI3hC,KAAK2c,QAAQ+kB,GAErB,MAAMjsB,EAAQksB,EAAE5vB,GAGhB,MAFM,WAAY4vB,IACd3hC,KAAKuS,OAASovB,EAAEpvB,QACbkD,EAEXkH,QAAQ1N,EAAQ4yB,GACZ,MAAMloB,EAAM3Z,KAAK8hC,WAAW7yB,EAAQ4yB,GACpC,OAAQloB,EAAI+C,UAAY1c,KAAK+hC,kBAAkBpoB,GAEnDqoB,aAAa/yB,EAAQyW,GACjB,GAAmC,mBAAxB1lB,KAAKuE,KAAK09B,WACjB,MAAM,IAAIliC,MAAM,2CAEpB,MAAMkiC,WAAEA,GAAejiC,KAAKuE,KAC5B,OAAO29B,EAAgB7lB,KAAKrc,KAAMiP,EAAQyW,GAC1Cnb,eAAe23B,EAAgBC,EAASN,SAC9BO,EAAe/lB,KAAKrc,KAAMmiC,EAAQE,SACxC,MAAM1oB,EAAM3Z,KAAK8hC,WAAWK,EAASN,GACrC,OAAOloB,EAAI+C,UAAY4lB,EAAcjmB,KAAKrc,KAAM2Z,GAEpDpP,eAAe63B,EAAe1d,GACtBA,IAAS1kB,KAAK4hC,UAAUld,UAClBwd,EAAgB7lB,KAAKrc,KAAM,CAAE0kB,KAAAA,IAAQ,GAGnDna,eAAe+3B,EAAc3oB,GACzB,IACI,OAAO3Z,KAAK+hC,kBAAkBpoB,GAElC,MAAOjQ,GACH,KAAMA,aAAa64B,EAAYvvB,SAC3B,MAAMtJ,EAGV,OAFA84B,EAAYnmB,KAAKrc,KAAM0J,SACjB+4B,EAAkBpmB,KAAKrc,KAAM0J,EAAEghB,eAC9B4X,EAAcjmB,KAAKrc,KAAM2Z,IAGxC,SAAS6oB,GAAc9X,cAAe3lB,EAAG0lB,WAAEA,IACvC,GAAIzqB,KAAKwjB,KAAKze,GACV,MAAM,IAAIhF,MAAM,aAAagF,mBAAqB0lB,wBAG1DlgB,eAAek4B,EAAkB19B,GAC7B,MAAMo9B,QAAgBO,EAAYrmB,KAAKrc,KAAM+E,GACxC/E,KAAKwjB,KAAKze,UACLq9B,EAAe/lB,KAAKrc,KAAMmiC,EAAQE,SACvCriC,KAAKwjB,KAAKze,IACX/E,KAAK2iC,UAAUR,EAASp9B,EAAK2gB,GAErCnb,eAAem4B,EAAY39B,GACvB,MAAMwL,EAAIvQ,KAAKogC,SAASr7B,GACxB,GAAIwL,EACA,OAAOA,EACX,IACI,aAAcvQ,KAAKogC,SAASr7B,GAAOk9B,EAAWl9B,mBAGvC/E,KAAKogC,SAASr7B,KAKjC49B,UAAU1zB,EACVvM,EACAm/B,EACAe,EAAkB5iC,KAAKuE,KAAKgY,gBAExB,GAAIxa,MAAMC,QAAQiN,GAAS,CACvB,IAAK,MAAM0K,KAAO1K,EACdjP,KAAK2iC,UAAUhpB,OAAKjY,EAAWmgC,EAAOe,GAC1C,OAAO5iC,KAEX,IAAIiiB,EACJ,GAAsB,iBAAXhT,EAAqB,CAC5B,MAAM2T,SAAEA,GAAa5iB,KAAKuE,KAE1B,GADA0d,EAAKhT,EAAO2T,QACDlhB,IAAPugB,GAAiC,iBAANA,EAC3B,MAAM,IAAIliB,MAAM,UAAU6iB,oBAMlC,OAHAlgB,GAAM,EAAIsmB,EAAU7G,aAAazf,GAAOuf,GACxCjiB,KAAK6iC,aAAangC,GAClB1C,KAAKosB,QAAQ1pB,GAAO1C,KAAK8hC,WAAW7yB,EAAQ4yB,EAAOn/B,EAAKkgC,GAAiB,GAClE5iC,KAIXmhC,cAAclyB,EAAQvM,EACtBkgC,EAAkB5iC,KAAKuE,KAAKgY,gBAGxB,OADAvc,KAAK2iC,UAAU1zB,EAAQvM,GAAK,EAAMkgC,GAC3B5iC,KAGXuc,eAAetN,EAAQ6zB,GACnB,GAAqB,kBAAV7zB,EACP,OAAO,EACX,IAAIozB,EAEJ,GADAA,EAAUpzB,EAAOozB,aACD3gC,IAAZ2gC,GAA2C,iBAAXA,EAChC,MAAM,IAAItiC,MAAM,4BAGpB,GADAsiC,EAAUA,GAAWriC,KAAKuE,KAAKk9B,aAAezhC,KAAKyhC,eAC9CY,EAGD,OAFAriC,KAAKwR,OAAOC,KAAK,6BACjBzR,KAAKuS,OAAS,MACP,EAEX,MAAMkD,EAAQzV,KAAK0c,SAAS2lB,EAASpzB,GACrC,IAAKwG,GAASqtB,EAAiB,CAC3B,MAAMvvB,EAAU,sBAAwBvT,KAAK+d,aAC7C,GAAiC,QAA7B/d,KAAKuE,KAAKgY,eAGV,MAAM,IAAIxc,MAAMwT,GAFhBvT,KAAKwR,OAAO7I,MAAM4K,GAI1B,OAAOkC,EAIXmsB,UAAUmB,GACN,IAAIppB,EACJ,KAAsD,iBAAvCA,EAAMqpB,EAAU3mB,KAAKrc,KAAM+iC,KACtCA,EAASppB,EACb,QAAYjY,IAARiY,EAAmB,CACnB,MAAMiJ,SAAEA,GAAa5iB,KAAKuE,KACpB+gB,EAAO,IAAI2d,EAAUtY,UAAU,CAAE1b,OAAQ,GAAI2T,SAAAA,IAEnD,GADAjJ,EAAMspB,EAAU5W,cAAchQ,KAAKrc,KAAMslB,EAAMyd,IAC1CppB,EACD,OACJ3Z,KAAKwjB,KAAKuf,GAAUppB,EAExB,OAAQA,EAAI+C,UAAY1c,KAAK+hC,kBAAkBpoB,GAMnDupB,aAAaxB,GACT,GAAIA,aAAwB/iB,OAGxB,OAFA3e,KAAKmjC,kBAAkBnjC,KAAKosB,QAASsV,GACrC1hC,KAAKmjC,kBAAkBnjC,KAAKwjB,KAAMke,GAC3B1hC,KAEX,cAAe0hC,GACX,IAAK,YAID,OAHA1hC,KAAKmjC,kBAAkBnjC,KAAKosB,SAC5BpsB,KAAKmjC,kBAAkBnjC,KAAKwjB,MAC5BxjB,KAAKqgC,OAAO+C,QACLpjC,KACX,IAAK,SAAU,CACX,MAAM2Z,EAAMqpB,EAAU3mB,KAAKrc,KAAM0hC,GAKjC,MAJkB,iBAAP/nB,GACP3Z,KAAKqgC,OAAOxU,OAAOlS,EAAI1K,eACpBjP,KAAKosB,QAAQsV,UACb1hC,KAAKwjB,KAAKke,GACV1hC,KAEX,IAAK,SAAU,CAEXA,KAAKqgC,OAAOxU,OADK6V,GAEjB,IAAIzf,EAAKyf,EAAa1hC,KAAKuE,KAAKqe,UAMhC,OALIX,IACAA,GAAK,EAAI+G,EAAU7G,aAAaF,UACzBjiB,KAAKosB,QAAQnK,UACbjiB,KAAKwjB,KAAKvB,IAEdjiB,KAEX,QACI,MAAM,IAAID,MAAM,wCAI5BsjC,cAAc/iB,GACV,IAAK,MAAMpE,KAAOoE,EACdtgB,KAAKqhC,WAAWnlB,GACpB,OAAOlc,KAEXqhC,WAAWiC,EAAUpnB,GAEjB,IAAItL,EACJ,GAAuB,iBAAZ0yB,EACP1yB,EAAU0yB,EACQ,iBAAPpnB,IACPlc,KAAKwR,OAAOC,KAAK,4DACjByK,EAAItL,QAAUA,OAGjB,CAAA,GAAuB,iBAAZ0yB,QAAgC5hC,IAARwa,EAQpC,MAAM,IAAInc,MAAM,kCALhB,GADA6Q,GADAsL,EAAMonB,GACQ1yB,QACV7O,MAAMC,QAAQ4O,KAAaA,EAAQpQ,OACnC,MAAM,IAAIT,MAAM,0DAOxB,GADAwjC,EAAalnB,KAAKrc,KAAM4Q,EAASsL,IAC5BA,EAED,OADA,EAAIlH,EAAO8M,UAAUlR,EAAU+F,GAAQ6sB,EAAQnnB,KAAKrc,KAAM2W,IACnD3W,KAEXyjC,EAAkBpnB,KAAKrc,KAAMkc,GAC7B,MAAMzF,EAAa,IACZyF,EACHrG,MAAM,EAAIoO,EAAWpN,cAAcqF,EAAIrG,MACvCrC,YAAY,EAAIyQ,EAAWpN,cAAcqF,EAAI1I,aAKjD,OAHA,EAAIwB,EAAO8M,UAAUlR,EAAoC,IAA3B6F,EAAWZ,KAAKrV,OACvC8xB,GAAMkR,EAAQnnB,KAAKrc,KAAMsyB,EAAG7b,GAC5B6b,GAAM7b,EAAWZ,KAAK7P,QAASuR,GAAMisB,EAAQnnB,KAAKrc,KAAMsyB,EAAG7b,EAAYc,KACvEvX,KAEX0jC,WAAW9yB,GACP,MAAM2F,EAAOvW,KAAKqP,MAAMoB,IAAIG,GAC5B,MAAsB,iBAAR2F,EAAmBA,EAAKE,aAAeF,EAGzDotB,cAAc/yB,GAEV,MAAMvB,MAAEA,GAAUrP,YACXqP,EAAMC,SAASsB,UACfvB,EAAMoB,IAAIG,GACjB,IAAK,MAAM0F,KAASjH,EAAMD,MAAO,CAC7B,MAAMlO,EAAIoV,EAAMlH,MAAMw0B,UAAWrtB,GAASA,EAAK3F,UAAYA,GACvD1P,GAAK,GACLoV,EAAMlH,MAAMzN,OAAOT,EAAG,GAE9B,OAAOlB,KAGX6jC,UAAUhhC,EAAMoe,GAIZ,MAHqB,iBAAVA,IACPA,EAAS,IAAItC,OAAOsC,IACxBjhB,KAAKmgC,QAAQt9B,GAAQoe,EACdjhB,KAEX+d,WAAWxL,EAASvS,KAAKuS,QACzBuxB,UAAEA,EAAY,KAAIC,QAAEA,EAAU,QAAW,IAErC,OAAKxxB,GAA4B,IAAlBA,EAAO/R,OAEf+R,EACFiI,IAAK9Q,GAAM,GAAGq6B,IAAUr6B,EAAEuI,gBAAgBvI,EAAE6J,WAC5C3S,OAAO,CAACojC,EAAM1yB,IAAQ0yB,EAAOF,EAAYxyB,GAHnC,YAKf2yB,gBAAgBC,EAAYC,GACxB,MAAM/0B,EAAQpP,KAAKqP,MAAMoB,IACzByzB,EAAa7hC,KAAK+f,MAAM/f,KAAKC,UAAU4hC,IACvC,IAAK,MAAMpa,KAAeqa,EAAsB,CAC5C,MAAMha,EAAWL,EAAYvH,MAAM,KAAKngB,MAAM,GAC9C,IAAIkN,EAAW40B,EACf,IAAK,MAAME,KAAOja,EACd7a,EAAWA,EAAS80B,GACxB,IAAK,MAAM1hC,KAAO0M,EAAO,CACrB,MAAMmH,EAAOnH,EAAM1M,GACnB,GAAmB,iBAAR6T,EACP,SACJ,MAAM1F,MAAEA,GAAU0F,EAAKE,WACjBxH,EAASK,EAAS5M,GACpBmO,GAAS5B,IACTK,EAAS5M,GAAO2hC,EAAap1B,KAGzC,OAAOi1B,EAEXf,kBAAkB/W,EAASkY,GACvB,IAAK,MAAMvB,KAAU3W,EAAS,CAC1B,MAAMzS,EAAMyS,EAAQ2W,GACfuB,IAASA,EAAMxkC,KAAKijC,KACH,iBAAPppB,SACAyS,EAAQ2W,GAEVppB,IAAQA,EAAI+L,OACjB1lB,KAAKqgC,OAAOxU,OAAOlS,EAAI1K,eAChBmd,EAAQ2W,MAK/BjB,WAAW7yB,EAAQyW,EAAMjD,EAAQlG,EAAiBvc,KAAKuE,KAAKgY,eAAgBomB,EAAY3iC,KAAKuE,KAAKw7B,eAC9F,IAAI9d,EACJ,MAAMW,SAAEA,GAAa5iB,KAAKuE,KAC1B,GAAqB,iBAAV0K,EACPgT,EAAKhT,EAAO2T,OAEX,CACD,GAAI5iB,KAAKuE,KAAKwgB,IACV,MAAM,IAAIhlB,MAAM,yBACf,GAAqB,kBAAVkP,EACZ,MAAM,IAAIlP,MAAM,oCAExB,IAAI4Z,EAAM3Z,KAAKqgC,OAAOv7B,IAAImK,GAC1B,QAAYvN,IAARiY,EACA,OAAOA,EACX8I,GAAS,EAAIuG,EAAU7G,aAAaF,GAAMQ,GAC1C,MAAMQ,EAAY+F,EAAUub,cAAcloB,KAAKrc,KAAMiP,EAAQwT,GAW7D,OAVA9I,EAAM,IAAIspB,EAAUtY,UAAU,CAAE1b,OAAAA,EAAQ2T,SAAAA,EAAU8C,KAAAA,EAAMjD,OAAAA,EAAQQ,UAAAA,IAChEjjB,KAAKqgC,OAAOj7B,IAAIuU,EAAI1K,OAAQ0K,GACxBgpB,IAAclgB,EAAO+hB,WAAW,OAE5B/hB,GACAziB,KAAK6iC,aAAapgB,GACtBziB,KAAKwjB,KAAKf,GAAU9I,GAEpB4C,GACAvc,KAAKuc,eAAetN,GAAQ,GACzB0K,EAEXkpB,aAAa5gB,GACT,GAAIjiB,KAAKosB,QAAQnK,IAAOjiB,KAAKwjB,KAAKvB,GAC9B,MAAM,IAAIliB,MAAM,0BAA0BkiB,qBAGlD8f,kBAAkBpoB,GAMd,GALIA,EAAI+L,KACJ1lB,KAAKykC,mBAAmB9qB,GAExBspB,EAAUpY,cAAcxO,KAAKrc,KAAM2Z,IAElCA,EAAI+C,SACL,MAAM,IAAI3c,MAAM,4BACpB,OAAO4Z,EAAI+C,SAEf+nB,mBAAmB9qB,GACf,MAAM+qB,EAAc1kC,KAAKuE,KACzBvE,KAAKuE,KAAOvE,KAAK6gC,UACjB,IACIoC,EAAUpY,cAAcxO,KAAKrc,KAAM2Z,WAGnC3Z,KAAKuE,KAAOmgC,IAOxB,SAAS9D,EAAa+D,EAAWrM,EAAShnB,EAAKkvB,EAAM,SACjD,IAAK,MAAM99B,KAAOiiC,EAAW,CACzB,MAAMC,EAAMliC,EACRkiC,KAAOtM,GACPt4B,KAAKwR,OAAOgvB,GAAK,GAAGlvB,aAAe5O,MAAQiiC,EAAUC,OAGjE,SAAS5B,EAAUD,GAEf,OADAA,GAAS,EAAI/Z,EAAU7G,aAAa4gB,GAC7B/iC,KAAKosB,QAAQ2W,IAAW/iC,KAAKwjB,KAAKuf,GAE7C,SAAS3B,IACL,MAAMyD,EAAc7kC,KAAKuE,KAAK6nB,QAC9B,GAAKyY,EAEL,GAAI9iC,MAAMC,QAAQ6iC,GACd7kC,KAAK2iC,UAAUkC,QAEf,IAAK,MAAMniC,KAAOmiC,EACd7kC,KAAK2iC,UAAUkC,EAAYniC,GAAMA,GAE7C,SAASq+B,IACL,IAAK,MAAMl+B,KAAQ7C,KAAKuE,KAAK47B,QAAS,CAClC,MAAMlf,EAASjhB,KAAKuE,KAAK47B,QAAQt9B,GAC7Boe,GACAjhB,KAAK6jC,UAAUhhC,EAAMoe,IAGjC,SAASigB,EAAmB4D,GACxB,GAAI/iC,MAAMC,QAAQ8iC,GACd9kC,KAAKqjC,cAAcyB,OADvB,CAIA9kC,KAAKwR,OAAOC,KAAK,oDACjB,IAAK,MAAMb,KAAWk0B,EAAM,CACxB,MAAM5oB,EAAM4oB,EAAKl0B,GACZsL,EAAItL,UACLsL,EAAItL,QAAUA,GAClB5Q,KAAKqhC,WAAWnlB,KAGxB,SAAS4kB,IACL,MAAMiE,EAAW,IAAK/kC,KAAKuE,MAC3B,IAAK,MAAMqgC,KAAOtH,SACPyH,EAASH,GACpB,OAAOG,EAhDXxlC,UAAkB2gC,EAClBA,EAAI5sB,gBAAkB4X,EAAmBlY,QACzCktB,EAAI1V,gBAAkB+X,EAAYvvB,QAgDlC,MAAMstB,EAAS,CAAEE,QAAW/uB,SAAY9I,WAUlCq8B,EAAe,0BACrB,SAASzB,EAAa3yB,EAASsL,GAC3B,MAAM7M,MAAEA,GAAUrP,KAOlB,IANA,EAAIgV,EAAO8M,UAAUlR,EAAU+F,IAC3B,GAAItH,EAAMC,SAASqH,GACf,MAAM,IAAI5W,MAAM,WAAW4W,wBAC/B,IAAKquB,EAAallC,KAAK6W,GACnB,MAAM,IAAI5W,MAAM,WAAW4W,wBAE9BuF,GAEDA,EAAIrL,SAAW,SAAUqL,MAAO,aAAcA,GAC9C,MAAM,IAAInc,MAAM,yDAGxB,SAASyjC,EAAQ5yB,EAAS6F,EAAYwB,GAClC,IAAIvX,EACJ,MAAM0V,EAAOK,MAAAA,OAA+C,EAASA,EAAWL,KAChF,GAAI6B,GAAY7B,EACZ,MAAM,IAAIrW,MAAM,+CACpB,MAAMsP,MAAEA,GAAUrP,KAClB,IAAIilC,EAAY7uB,EAAO/G,EAAM+G,KAAO/G,EAAMD,MAAM81B,KAAK,EAAGrvB,KAAM0B,KAAQA,IAAMU,GAM5E,GALKgtB,IACDA,EAAY,CAAEpvB,KAAMoC,EAAU7I,MAAO,IACrCC,EAAMD,MAAMhO,KAAK6jC,IAErB51B,EAAMC,SAASsB,IAAW,GACrB6F,EACD,OACJ,MAAMF,EAAO,CACT3F,QAAAA,EACA6F,WAAY,IACLA,EACHZ,MAAM,EAAIoO,EAAWpN,cAAcJ,EAAWZ,MAC9CrC,YAAY,EAAIyQ,EAAWpN,cAAcJ,EAAWjD,cAGxDiD,EAAW0uB,OACXC,EAAc/oB,KAAKrc,KAAMilC,EAAW1uB,EAAME,EAAW0uB,QAErDF,EAAU71B,MAAMhO,KAAKmV,GACzBlH,EAAMoB,IAAIG,GAAW2F,EACY,QAAhC7V,EAAK+V,EAAWC,kBAA+B,IAAPhW,GAAyBA,EAAGsF,QAAS2Q,GAAQ3W,KAAKqhC,WAAW1qB,IAE1G,SAASyuB,EAAcH,EAAW1uB,EAAM4uB,GACpC,MAAMjkC,EAAI+jC,EAAU71B,MAAMw0B,UAAWyB,GAAUA,EAAMz0B,UAAYu0B,GAC7DjkC,GAAK,EACL+jC,EAAU71B,MAAMzN,OAAOT,EAAG,EAAGqV,IAG7B0uB,EAAU71B,MAAMhO,KAAKmV,GACrBvW,KAAKwR,OAAOC,KAAK,QAAQ0zB,qBAGjC,SAAS1B,EAAkBvnB,GACvB,IAAIgoB,WAAEA,GAAehoB,OACFxa,IAAfwiC,IAEAhoB,EAAIrL,OAAS7Q,KAAKuE,KAAKsM,QACvBqzB,EAAaG,EAAaH,IAC9BhoB,EAAIK,eAAiBvc,KAAK2c,QAAQunB,GAAY,IAElD,MAAMoB,EAAW,CACb5gB,KAAM,kFAEV,SAAS2f,EAAap1B,GAClB,MAAO,CAAEkR,MAAO,CAAClR,EAAQq2B,qDCpmBjB,CACR10B,QAAS,KACTtQ,OACI,MAAM,IAAIP,MAAM,sGCJxBX,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,UAAkBA,mBAAsB,EAOxC,MAAM2c,EAAM,CACRtL,QAAS,OACT4C,WAAY,SACZlT,KAAKmT,GACD,MAAMzD,IAAEA,EAAKf,OAAQyV,EAAI1V,GAAEA,GAAOyE,GAC5BgP,OAAEA,EAAQrP,UAAWwX,EAAGzX,aAAEA,EAAY5O,KAAEA,EAAI2K,KAAEA,GAASF,GACvDsW,KAAEA,GAASsF,EACjB,IAAc,MAATlG,GAAyB,OAATA,IAAkBjC,IAAW6C,EAAK7C,OACnD,OAOJ,WACI,GAAImI,IAAQtF,EACR,OAAOigB,EAAQ9xB,EAAKN,EAAcyX,EAAKA,EAAIvX,QAC/C,MAAMgS,EAAWrV,EAAIzE,WAAW,OAAQ,CAAExG,IAAKugB,IAC/C,OAAOigB,EAAQ9xB,EAASxD,EAAUlP,CAAG,GAAGskB,aAAqBC,EAAMA,EAAKjS,QAXjEmyB,GACX,MAAMC,EAAWxC,EAAUyC,WAAWrpB,KAAKnN,EAAMoW,EAAM7C,EAAQiC,GAC/D,QAAiBhjB,IAAb+jC,EACA,MAAM,IAAIlD,EAAYvvB,QAAQhE,EAAGzK,KAAKse,YAAaJ,EAAQiC,GAC/D,OAAI+gB,aAAoBxC,EAAUtY,UASlC,SAAsBhR,GAClB,MAAMgoB,EAAIgE,EAAYlyB,EAAKkG,GAC3B4rB,EAAQ9xB,EAAKkuB,EAAGhoB,EAAKA,EAAItG,QAVlBuyB,CAAaH,GAYxB,SAAyB9rB,GACrB,MAAMksB,EAAU71B,EAAIzE,WAAW,UAA+B,IAArBhH,EAAKjE,KAAKse,OAAkB,CAAE7Z,IAAK4U,EAAKrZ,MAAM,EAAI2P,EAAU3N,WAAWqX,IAAS,CAAE5U,IAAK4U,IAC1HlE,EAAQzF,EAAInN,KAAK,SACjB8Y,EAASlI,EAAI8H,UAAU,CACzBtM,OAAQ0K,EACRb,UAAW,GACXnI,WAAYV,EAAUpL,IACtB6L,aAAcm1B,EACd1xB,cAAeuQ,GAChBjP,GACHhC,EAAI8V,eAAe5N,GACnBlI,EAAIiK,GAAGjI,GAtBJqwB,CAAgBL,KA0B/B,SAASE,EAAYlyB,EAAKkG,GACtB,MAAM3J,IAAEA,GAAQyD,EAChB,OAAOkG,EAAI+C,SACL1M,EAAIzE,WAAW,WAAY,CAAExG,IAAK4U,EAAI+C,WAClCzM,EAAUlP,CAAG,GAAGiP,EAAIzE,WAAW,UAAW,CAAExG,IAAK4U,eAG/D,SAAS4rB,EAAQ9xB,EAAKkuB,EAAGhoB,EAAKtG,GAC1B,MAAMrD,IAAEA,EAAGhB,GAAEA,GAAOyE,GACdK,UAAEA,EAAWV,UAAWwX,EAAGrmB,KAAEA,GAASyK,EACtC+2B,EAAUxhC,EAAKyY,YAAcjK,EAAQC,QAAQhT,KAAOiQ,EAAUpL,IAyBpE,SAASmhC,EAAcpnB,GACnB,MAAM1L,EAAWjD,EAAUlP,CAAG,GAAG6d,WACjC5O,EAAIhE,OAAO+G,EAAQC,QAAQV,QAAarC,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQV,sBAAsBY,OAAUH,EAAQC,QAAQV,kBAAkBY,MAC1IlD,EAAIhE,OAAO+G,EAAQC,QAAQT,OAAYtC,EAAUlP,CAAG,GAAGgS,EAAQC,QAAQV,kBAE3E,SAAS2zB,EAAiBrnB,GACtB,IAAIle,EACJ,IAAKsO,EAAGzK,KAAKiX,YACT,OACJ,MAAM0qB,EAAiF,QAAjExlC,EAAKiZ,MAAAA,OAAiC,EAASA,EAAI+C,gBAA6B,IAAPhc,OAAgB,EAASA,EAAGgmB,UAE3H,IAAiB,IAAb1X,EAAGoB,MACH,GAAI81B,IAAiBA,EAAava,kBACHjqB,IAAvBwkC,EAAa91B,QACbpB,EAAGoB,MAAQ4E,EAAOuU,eAAenZ,MAAMJ,EAAKk2B,EAAa91B,MAAOpB,EAAGoB,YAGtE,CACD,MAAMA,EAAQJ,EAAI9M,IAAI,QAAa+M,EAAUlP,CAAG,GAAG6d,qBACnD5P,EAAGoB,MAAQ4E,EAAOuU,eAAenZ,MAAMJ,EAAKI,EAAOpB,EAAGoB,MAAOH,EAAUxQ,MAG/E,IAAiB,IAAbuP,EAAGhE,MACH,GAAIk7B,IAAiBA,EAAata,kBACHlqB,IAAvBwkC,EAAal7B,QACbgE,EAAGhE,MAAQgK,EAAOuU,eAAeve,MAAMgF,EAAKk2B,EAAal7B,MAAOgE,EAAGhE,YAGtE,CACD,MAAMA,EAAQgF,EAAI9M,IAAI,QAAa+M,EAAUlP,CAAG,GAAG6d,qBACnD5P,EAAGhE,MAAQgK,EAAOuU,eAAeve,MAAMgF,EAAKhF,EAAOgE,EAAGhE,MAAOiF,EAAUxQ,OAtD/E4T,EAIJ,WACI,IAAKuX,EAAIvX,OACL,MAAM,IAAItT,MAAM,0CACpB,MAAM0V,EAAQzF,EAAI/M,IAAI,SACtB+M,EAAIvC,IAAI,KACJuC,EAAI1P,KAAS2P,EAAUlP,CAAG,UAAS,EAAIiC,EAAO+Z,kBAAkBtJ,EAAKkuB,EAAGoE,MACxEE,EAAiBtE,GACZ7tB,GACD9D,EAAIhE,OAAOyJ,GAAO,IACtB/L,IACAsG,EAAI5D,GAAO6D,EAAUlP,CAAG,KAAK2I,gBAAgBsF,EAAGsE,mBAAoB,IAAMtD,EAAIlC,MAAMpE,IACpFs8B,EAAct8B,GACToK,GACD9D,EAAIhE,OAAOyJ,GAAO,KAE1BhC,EAAIiK,GAAGjI,GAlBP0wB,GAqBA1yB,EAAIqI,QAAO,EAAI9Y,EAAO+Z,kBAAkBtJ,EAAKkuB,EAAGoE,GAAU,IAAME,EAAiBtE,GAAI,IAAMqE,EAAcrE,IA3BjHpiC,cAAsBomC,EAgEtBpmC,UAAkBgmC,EAClBhmC,UAAkB2c,kDCpHL,CACT,UACA,MACA,QACA,cACA,CAAEtL,QAAS,YACX,cACAw1B,EAAKpzB,QACLqzB,EAAMrzB,mCCTV,MAAMszB,EAAMr2B,EAAUhE,UAChBs6B,EAAO,CACT7lB,QAAS,CAAE8lB,MAAO,KAAM9oB,GAAI4oB,EAAIt/B,IAAK0gB,KAAM4e,EAAIz/B,IAC/C8Z,QAAS,CAAE6lB,MAAO,KAAM9oB,GAAI4oB,EAAIx/B,IAAK4gB,KAAM4e,EAAIv/B,IAC/C6Z,iBAAkB,CAAE4lB,MAAO,IAAK9oB,GAAI4oB,EAAIv/B,GAAI2gB,KAAM4e,EAAIx/B,KACtD+Z,iBAAkB,CAAE2lB,MAAO,IAAK9oB,GAAI4oB,EAAIz/B,GAAI6gB,KAAM4e,EAAIt/B,MAEpD2B,EAAQ,CACV4K,QAAS,EAAG3C,QAAAA,EAAS4E,WAAAA,KAAqBvF,EAAUhQ,GAAK,WAAWsmC,EAAK31B,GAAS41B,SAAShxB,IAC3FlB,OAAQ,EAAG1D,QAAAA,EAAS4E,WAAAA,KAAqBvF,EAAUlP,CAAG,gBAAgBwlC,EAAK31B,GAAS41B,iBAAiBhxB,MAEnG0G,EAAM,CACRtL,QAASxR,OAAOkR,KAAKi2B,GACrB1wB,KAAM,SACNrC,WAAY,SACZ3C,OAAO,QACPlI,EACArI,KAAKmT,GACD,MAAM7C,QAAEA,EAAOmB,KAAEA,EAAIyD,WAAEA,GAAe/B,EACtCA,EAAIkU,UAAc1X,EAAUlP,CAAG,GAAGgR,KAAQw0B,EAAK31B,GAAS8W,QAAQlS,cAAuBzD,QAG/F,kDAAkBmK,0EClBN,CACRtL,QAAS,aACTiF,KAAM,SACNrC,WAAY,SACZ3C,OAAO,QARG,CACV0C,QAAS,EAAGiC,WAAAA,KAAqBvF,EAAUhQ,GAAK,uBAAuBuV,IACvElB,OAAQ,EAAGkB,WAAAA,KAAqBvF,EAAUlP,CAAG,gBAAgByU,MAQ7DlV,KAAKmT,GACD,MAAMzD,IAAEA,EAAG+B,KAAEA,EAAIyD,WAAEA,EAAUxG,GAAEA,GAAOyE,EAEhCgzB,EAAOz3B,EAAGzK,KAAKmiC,oBACfllC,EAAMwO,EAAI/M,IAAI,OACd0jC,EAAUF,EACNx2B,EAAUlP,CAAG,uBAAuBS,QAAUA,WAAailC,IAC3Dx2B,EAAUlP,CAAG,GAAGS,kBAAoBA,KAC9CiS,EAAIkU,UAAc1X,EAAUlP,CAAG,IAAIyU,eAAwBhU,OAASuQ,KAAQyD,MAAemxB,mCCjBnG,SAASC,EAAW3mC,GAChB,MAAMkO,EAAMlO,EAAIO,OAChB,IAEIlB,EAFAkB,EAAS,EACTqmC,EAAM,EAEV,KAAOA,EAAM14B,GACT3N,IACAlB,EAAQW,EAAI4yB,WAAWgU,KACnBvnC,GAAS,OAAUA,GAAS,OAAUunC,EAAM14B,IAE5C7O,EAAQW,EAAI4yB,WAAWgU,GACE,QAAZ,MAARvnC,IACDunC,KAGZ,OAAOrmC,EAEX,MAAkBomC,EAClBA,EAAWtmC,KAAO,6KCVN,CACRsQ,QAAS,CAAC,YAAa,aACvBiF,KAAM,SACNrC,WAAY,SACZ3C,OAAO,QAXG,CACV0C,QAAO,EAAC3C,QAAEA,EAAO4E,WAAEA,KAEJvF,EAAUhQ,GAAK,iBADD,cAAZ2Q,EAA0B,OAAS,gBACQ4E,eAE5DlB,OAAQ,EAAGkB,WAAAA,KAAqBvF,EAAUlP,CAAG,WAAWyU,MAQxDlV,KAAKmT,GACD,MAAM7C,QAAEA,EAAOmB,KAAEA,EAAIyD,WAAEA,EAAUxG,GAAEA,GAAOyE,EACpCnL,EAAiB,cAAZsI,EAA0BX,EAAUhE,UAAUpF,GAAKoJ,EAAUhE,UAAUlF,GAC5EoH,GAA0B,IAApBa,EAAGzK,KAAK65B,QAAwBnuB,EAAUlP,CAAG,GAAGgR,WAAoB9B,EAAUlP,CAAG,IAAG,EAAIiU,EAAOmG,SAAS1H,EAAIzD,IAAK82B,EAAa9zB,YAAYjB,KACtJ0B,EAAIkU,UAAc1X,EAAUlP,CAAG,GAAGoN,KAAO7F,KAAMkN,+ECd3C,CACR5E,QAAS,UACTiF,KAAM,SACNrC,WAAY,SACZ3C,OAAO,QARG,CACV0C,QAAS,EAAGiC,WAAAA,KAAqBvF,EAAUhQ,GAAK,uBAAuBuV,KACvElB,OAAQ,EAAGkB,WAAAA,KAAqBvF,EAAUlP,CAAG,aAAayU,MAQ1DlV,KAAKmT,GACD,MAAM1B,KAAEA,EAAIlB,MAAEA,EAAK5B,OAAEA,EAAMuG,WAAEA,EAAUxG,GAAEA,GAAOyE,EAG1CyH,EAASrK,EAAYZ,EAAUlP,CAAG,eAAeyU,MAD7CxG,EAAGzK,KAAK0W,cAAgB,IAAM,QACsC,EAAIjY,EAAO+jC,YAAYtzB,EAAKxE,GAC1GwE,EAAIkU,UAAc1X,EAAUlP,CAAG,IAAIma,UAAenJ,gFCT9C,CACRnB,QAAS,CAAC,gBAAiB,iBAC3BiF,KAAM,SACNrC,WAAY,SACZ3C,OAAO,QAXG,CACV0C,QAAO,EAAC3C,QAAEA,EAAO4E,WAAEA,KAEJvF,EAAUhQ,GAAK,iBADD,kBAAZ2Q,EAA8B,OAAS,gBACI4E,eAE5DlB,OAAQ,EAAGkB,WAAAA,KAAqBvF,EAAUlP,CAAG,WAAWyU,MAQxDlV,KAAKmT,GACD,MAAM7C,QAAEA,EAAOmB,KAAEA,EAAIyD,WAAEA,GAAe/B,EAEtCA,EAAIkU,UAAc1X,EAAUlP,CAAG,eAAegR,aADvB,kBAAZnB,EAA8BX,EAAUhE,UAAUpF,GAAKoJ,EAAUhE,UAAUlF,MAClByO,+ECVhE,CACR5E,QAAS,WACTiF,KAAM,SACNrC,WAAY,QACZ3C,OAAO,QARG,CACV0C,QAAS,EAAGe,QAAUgG,gBAAAA,MAA4BrK,EAAUhQ,GAAK,gCAAgCqa,KACjGhG,OAAQ,EAAGA,QAAUgG,gBAAAA,MAA4BrK,EAAUlP,CAAG,qBAAqBuZ,MAQnFha,KAAKmT,GACD,MAAMzD,IAAEA,EAAGf,OAAEA,EAAMuG,WAAEA,EAAUzD,KAAEA,EAAIlB,MAAEA,EAAK7B,GAAEA,GAAOyE,GAC/ClP,KAAEA,GAASyK,EACjB,IAAK6B,GAA2B,IAAlB5B,EAAOzO,OACjB,OACJ,MAAMwmC,EAAU/3B,EAAOzO,QAAU+D,EAAKs7B,aAKtC,GAJI7wB,EAAG8E,UAeP,WACI,GAAIkzB,GAAWn2B,EACX4C,EAAI0J,WAAWlN,EAAUpL,IAAKoiC,QAG9B,IAAK,MAAM5tB,KAAQpK,GACf,EAAIjM,EAAOkkC,wBAAwBzzB,EAAK4F,GApBhD8tB,GAwBJ,WACI,MAAM5sB,EAAUvK,EAAI/M,IAAI,WACxB,GAAI+jC,GAAWn2B,EAAO,CAClB,MAAM4E,EAAQzF,EAAI/M,IAAI,SAAS,GAC/BwQ,EAAI0J,WAAW1H,EAAO,IAe9B,SAA0B8E,EAAS9E,GAC/BhC,EAAI4G,UAAU,CAAEC,gBAAiBC,IACjCvK,EAAI9C,MAAMqN,EAAS/E,EAAY,KAC3BxF,EAAIhE,OAAOyJ,GAAO,EAAIzS,EAAOokC,gBAAgBp3B,EAAK+B,EAAMwI,EAAShW,EAAK+I,gBACtE0C,EAAI5D,IAAG,EAAI6D,EAAUrG,KAAK6L,GAAQ,KAC9BhC,EAAI9K,QACJqH,EAAIzC,WAET0C,EAAUpL,KAvBmBwiC,CAAiB9sB,EAAS9E,IACtDhC,EAAIiK,GAAGjI,QAGPzF,EAAI5D,IAAG,EAAIpJ,EAAOskC,kBAAkB7zB,EAAKxE,EAAQsL,KACjD,EAAIvX,EAAOukC,mBAAmB9zB,EAAK8G,GACnCvK,EAAIxG,OAhCRg+B,GACAjjC,EAAKq7B,eAAgB,CACrB,MAAMxvB,EAAQqD,EAAIe,aAAakF,YACzB4E,kBAAEA,GAAsB7K,EAAIzE,GAClC,IAAK,MAAMy4B,KAAex4B,OACqDvN,KAAtE0O,MAAAA,OAAqC,EAASA,EAAMq3B,KAAgCnpB,EAAkBva,IAAI0jC,KAG3G,EAAIzyB,EAAOzF,iBAAiBP,EADhB,sBAAsBy4B,yBADfz4B,EAAGoE,UAAUqP,OAASzT,EAAGmF,kCAEPnF,EAAGzK,KAAKq7B,gBA2BzD,SAASqH,IACLj3B,EAAI9C,MAAM,OAAQsI,EAAa6D,IAC3B5F,EAAI4G,UAAU,CAAEC,gBAAiBjB,IACjCrJ,EAAI5D,IAAG,EAAIpJ,EAAOiX,kBAAkBjK,EAAK+B,EAAMsH,EAAM9U,EAAK+I,eAAgB,IAAMmG,EAAI9K,sFCpDxF,CACRiI,QAAS,CAAC,WAAY,YACtBiF,KAAM,QACNrC,WAAY,SACZ3C,OAAO,QAXG,CACV0C,QAAO,EAAC3C,QAAEA,EAAO4E,WAAEA,KAEJvF,EAAUhQ,GAAK,iBADD,aAAZ2Q,EAAyB,OAAS,gBACS4E,UAE5DlB,OAAQ,EAAGkB,WAAAA,KAAqBvF,EAAUlP,CAAG,WAAWyU,MAQxDlV,KAAKmT,GACD,MAAM7C,QAAEA,EAAOmB,KAAEA,EAAIyD,WAAEA,GAAe/B,EAEtCA,EAAIkU,UAAc1X,EAAUlP,CAAG,GAAGgR,YADX,aAAZnB,EAAyBX,EAAUhE,UAAUpF,GAAKoJ,EAAUhE,UAAUlF,MAC1ByO,iCCf/DkJ,EAAMpe,KAAO,4CACb,kDAAkBoe,0ECOC,CAEfgpB,EAAc10B,QACd20B,EAAa30B,QAEb40B,EAAc50B,QACd60B,EAAU70B,QAEV80B,EAAkB90B,QAClB+0B,EAAW/0B,QAEXg1B,EAAah1B,oDCbL,CACRpC,QAAS,cACTiF,KAAM,QACNrC,WAAY,UACZ3C,OAAO,QARG,CACV0C,QAAS,EAAGe,QAAUpT,EAAAA,EAAG4zB,EAAAA,MAAc7kB,EAAUhQ,GAAK,2CAA2C60B,SAAS5zB,mBAC1GoT,OAAQ,EAAGA,QAAUpT,EAAAA,EAAG4zB,EAAAA,MAAc7kB,EAAUlP,CAAG,OAAOG,SAAS4zB,MAQnEx0B,KAAKmT,GACD,MAAMzD,IAAEA,EAAG+B,KAAEA,EAAIlB,MAAEA,EAAK5B,OAAEA,EAAMuF,aAAEA,EAAYgB,WAAEA,EAAUxG,GAAEA,GAAOyE,EACnE,IAAK5C,IAAU5B,EACX,OACJ,MAAMwG,EAAQzF,EAAI/M,IAAI,SAChBglC,EAAYzzB,EAAaxJ,OAAQ,EAAIiZ,EAAWgB,gBAAgBzQ,EAAaxJ,OAAS,GAa5F,SAASk9B,EAAMhnC,EAAG4zB,GACd,MAAMr0B,EAAOuP,EAAInN,KAAK,QAChBgV,GAAY,EAAIoM,EAAWnM,gBAAgBmwB,EAAWxnC,EAAMuO,EAAGzK,KAAKwT,cAAekM,EAAWrN,SAASoB,OACvGmwB,EAAUn4B,EAAIjN,MAAM,UAAekN,EAAUlP,CAAG,MACtDiP,EAAIhD,IAAQiD,EAAUlP,CAAG,IAAIG,OAAQ,KACjC8O,EAAI/M,IAAIxC,EAAUwP,EAAUlP,CAAG,GAAGgR,KAAQ7Q,MAC1C8O,EAAI5D,GAAGyL,EAAe5H,EAAUlP,CAAG,YAC/BknC,EAAUznC,OAAS,GACnBwP,EAAI5D,GAAO6D,EAAUlP,CAAG,UAAUN,gBAAwBwP,EAAUlP,CAAG,GAAGN,YAC9EuP,EACK5D,GAAO6D,EAAUlP,CAAG,UAAUonC,KAAW1nC,iBAAqB,KAC/DuP,EAAIhE,OAAO8oB,EAAO7kB,EAAUlP,CAAG,GAAGonC,KAAW1nC,MAC7CgT,EAAI9K,QACJqH,EAAIhE,OAAOyJ,GAAO,GAAOlI,UAExBjN,KAAS2P,EAAUlP,CAAG,GAAGonC,KAAW1nC,QAAWS,OAG5D,SAASknC,EAAOlnC,EAAG4zB,GACf,MAAMuT,GAAM,EAAIrzB,EAAOmG,SAASnL,EAAKs4B,EAAQt1B,SACvCu1B,EAAQv4B,EAAInN,KAAK,SACvBmN,EAAIxH,MAAM+/B,GAAOv7B,IAAQiD,EAAUlP,CAAG,IAAIG,OAAQ,IAAM8O,EAAIhD,IAAQiD,EAAUlP,CAAG,GAAG+zB,OAAO5zB,MAAM4zB,OAAQ,IAAM9kB,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGsnC,KAAOt2B,KAAQ7Q,OAAO6Q,KAAQ+iB,MAAO,KAC3KrhB,EAAI9K,QACJqH,EAAIhE,OAAOyJ,GAAO,GAAOlI,MAAMg7B,OAnCvC90B,EAAI0J,WAAW1H,EAEf,WACI,MAAMvU,EAAI8O,EAAI/M,IAAI,IAASgN,EAAUlP,CAAG,GAAGgR,YACrC+iB,EAAI9kB,EAAI/M,IAAI,KAClBwQ,EAAI4G,UAAU,CAAEnZ,EAAAA,EAAG4zB,EAAAA,IACnB9kB,EAAIhE,OAAOyJ,GAAO,GAClBzF,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGG,QAAS,KAG7B+mC,EAAUznC,OAAS,IAAMynC,EAAUl9B,KAAMwM,GAAY,WAANA,GAAwB,UAANA,GAHb2wB,EAAQE,GAAQlnC,EAAG4zB,KAPnC7kB,EAAUlP,CAAG,GAAGyU,eAC/D/B,EAAIiK,GAAGjI,+BDCGzC,QAEd,CAAEpC,QAAS,OAAQ4C,WAAY,CAAC,SAAU,UAC1C,CAAE5C,QAAS,WAAY4C,WAAY,uDElB3B,CACR5C,QAAS,QACTC,OAAO,QANG,CACV0C,QAAS,4BACTe,OAAQ,EAAGkB,WAAAA,KAAqBvF,EAAUlP,CAAG,kBAAkByU,MAM/DlV,KAAKmT,GACD,MAAMzD,IAAEA,EAAG+B,KAAEA,EAAIlB,MAAEA,EAAK2E,WAAEA,EAAUvG,OAAEA,GAAWwE,EAC7C5C,GAAU5B,GAA2B,iBAAVA,EAC3BwE,EAAIkU,UAAc1X,EAAUlP,CAAG,KAAI,EAAIiU,EAAOmG,SAASnL,EAAKs4B,EAAQt1B,YAAYjB,MAASyD,MAGzF/B,EAAIiU,KAASzX,EAAUlP,CAAG,GAAGkO,SAAc8C,iCFS3CiB,oDGnBA,CACRpC,QAAS,OACT4C,WAAY,QACZ3C,OAAO,QAPG,CACV0C,QAAS,6CACTe,OAAQ,EAAGkB,WAAAA,KAAqBvF,EAAUlP,CAAG,mBAAmByU,MAOhElV,KAAKmT,GACD,MAAMzD,IAAEA,EAAG+B,KAAEA,EAAIlB,MAAEA,EAAK5B,OAAEA,EAAMuG,WAAEA,EAAUxG,GAAEA,GAAOyE,EACrD,IAAK5C,GAA2B,IAAlB5B,EAAOzO,OACjB,MAAM,IAAIT,MAAM,kCAEpB,IAAIsoC,EACJ,MAAMG,EAAS,IAAOH,MAAAA,EAAiCA,EAAOA,GAAM,EAAIrzB,EAAOmG,SAASnL,EAAKs4B,EAAQt1B,SACrG,IAAIyC,EACJ,GAJgBxG,EAAOzO,QAAUwO,EAAGzK,KAAKu7B,UAI1BjvB,EACX4E,EAAQzF,EAAI/M,IAAI,SAChBwQ,EAAI0J,WAAW1H,EAUnB,WACIzF,EAAIhE,OAAOyJ,GAAO,GAClBzF,EAAI9C,MAAM,IAAKsI,EAAamsB,GAAM3xB,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGynC,OAAYz2B,MAAS4vB,KAAM,IAAM3xB,EAAIhE,OAAOyJ,GAAO,GAAMlI,gBAVrH,CAED,IAAKxL,MAAMC,QAAQiN,GACf,MAAM,IAAIlP,MAAM,4BACpB,MAAM0oC,EAAUz4B,EAAIjN,MAAM,UAAWyS,GACrCC,GAAQ,EAAIxF,EAAUiK,OAAOjL,EAAOuL,IAAI,CAAC+gB,EAAIr6B,IAOjD,SAAmBunC,EAASvnC,GACxB,MAAMyY,EAAM1K,EAAO/N,GACnB,MAAsB,iBAARyY,GAA4B,OAARA,EACxB1J,EAAUlP,CAAG,GAAGynC,OAAYz2B,MAAS02B,KAAWvnC,MAChD+O,EAAUlP,CAAG,GAAGgR,SAAY4H,IAXa+uB,CAAUD,EAASvnC,KAE1EuS,EAAI+I,KAAK/G,+BHJNzC,qDI5BX5T,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,+BAAkC,EAGlC,MAIM2c,EAAM,CACRtL,QAAS,kBACTiF,KAAM,QACNrC,WAAY,CAAC,UAAW,UACxB2xB,OAAQ,cACRx8B,MATU,CACV4K,QAAS,EAAGe,QAAUnG,IAAAA,MAAgB8B,EAAUhQ,GAAK,2BAA2BkO,UAChFmG,OAAQ,EAAGA,QAAUnG,IAAAA,MAAgB8B,EAAUlP,CAAG,WAAWoN,MAQ7D7N,KAAKmT,GACD,MAAMe,aAAEA,EAAYxF,GAAEA,GAAOyE,GACvBzI,MAAEA,GAAUwJ,EACbzS,MAAMC,QAAQgJ,GAInB29B,EAAwBl1B,EAAKzI,IAHzB,EAAIgK,EAAOzF,iBAAiBP,EAAI,0EAM5C,SAAS25B,EAAwBl1B,EAAKzI,GAClC,MAAMgF,IAAEA,EAAGf,OAAEA,EAAM8C,KAAEA,EAAInB,QAAEA,EAAO5B,GAAEA,GAAOyE,EAC3CzE,EAAGhE,OAAQ,EACX,MAAMmD,EAAM6B,EAAIjN,MAAM,MAAWkN,EAAUlP,CAAG,GAAGgR,YACjD,IAAe,IAAX9C,EACAwE,EAAI4G,UAAU,CAAElM,IAAKnD,EAAMxK,SAC3BiT,EAAI+I,KAASvM,EAAUlP,CAAG,GAAGoN,QAAUnD,EAAMxK,eAE5C,GAAqB,iBAAVyO,KAAuB,EAAI+F,EAAOyF,mBAAmBzL,EAAIC,GAAS,CAC9E,MAAMwG,EAAQzF,EAAI9M,IAAI,QAAa+M,EAAUlP,CAAG,GAAGoN,QAAUnD,EAAMxK,UACnEwP,EAAI5D,IAAG,EAAI6D,EAAUrG,KAAK6L,GAAQ,IAGtC,SAAuBA,GACnBzF,EAAI/C,SAAS,IAAKjC,EAAMxK,OAAQ2N,EAAMjN,IAClCuS,EAAI8H,UAAU,CAAE3K,QAAAA,EAASc,SAAUxQ,EAAGyQ,aAAcqD,EAAO3D,KAAKS,KAAO2D,GAClEzG,EAAG8E,WACJ9D,EAAI5D,IAAG,EAAI6D,EAAUrG,KAAK6L,GAAQ,IAAMzF,EAAIzC,WAPZ8N,CAAc5F,IACtDhC,EAAIiK,GAAGjI,IAUflW,0BAAkCopC,EAClCppC,UAAkB2c,uBC9ClB9c,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,qBAAwB,EAIxB,MAAM2c,EAAM,CACRtL,QAAS,QACTiF,KAAM,QACNrC,WAAY,CAAC,SAAU,QAAS,WAChC2xB,OAAQ,cACR7kC,KAAKmT,GACD,MAAMxE,OAAEA,EAAMD,GAAEA,GAAOyE,EACvB,GAAI1R,MAAMC,QAAQiN,GACd,OAAO25B,EAAcn1B,EAAK,kBAAmBxE,GACjDD,EAAGhE,OAAQ,GACP,EAAIgK,EAAOyF,mBAAmBzL,EAAIC,IAEtCwE,EAAIiK,IAAG,EAAI1a,EAAO6lC,eAAep1B,MAGzC,SAASm1B,EAAcn1B,EAAKq1B,EAAYC,EAASt1B,EAAIxE,QACjD,MAAMe,IAAEA,EAAGwE,aAAEA,EAAYzC,KAAEA,EAAInB,QAAEA,EAAO5B,GAAEA,GAAOyE,GAiBjD,SAA0BkG,GACtB,MAAMpV,KAAEA,EAAI4P,cAAEA,GAAkBnF,EAC1Bg6B,EAAID,EAAOvoC,OAEb+D,EAAKo7B,eADSqJ,IAAMrvB,EAAIwH,UAAa6nB,IAAMrvB,EAAIuH,WAAgC,IAApBvH,EAAImvB,MAG/D,EAAI9zB,EAAOzF,iBAAiBP,EADhB,IAAI4B,SAAeo4B,qCAAqCF,6CAAsD30B,KACrF5P,EAAKo7B,cAtBlDsJ,CAAiBz0B,GACbxF,EAAGzK,KAAKiX,aAAeutB,EAAOvoC,SAAuB,IAAbwO,EAAGhE,QAC3CgE,EAAGhE,MAAQgK,EAAOuU,eAAeve,MAAMgF,EAAK+4B,EAAOvoC,OAAQwO,EAAGhE,QAElE,MAAMyK,EAAQzF,EAAInN,KAAK,SACjBsL,EAAM6B,EAAIjN,MAAM,MAAWkN,EAAUlP,CAAG,GAAGgR,YACjDg3B,EAAO/iC,QAAQ,CAAC2T,EAAKzY,MACb,EAAI8T,EAAOyF,mBAAmBzL,EAAI2K,KAEtC3J,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGoN,OAASjN,IAAK,IAAMuS,EAAI8H,UAAU,CACzD3K,QAAAA,EACAgL,WAAY1a,EACZwQ,SAAUxQ,GACXuU,IACHhC,EAAIiK,GAAGjI,MAYflW,gBAAwBqpC,EACxBrpC,UAAkB2c,mDC/CN,CACRtL,QAAS,cACTiF,KAAM,QACNrC,WAAY,CAAC,SACb2xB,OAAQ,cACR7kC,KAAOmT,IAAQ,EAAIy1B,GAAQN,eAAen1B,EAAK,mFCEvC,CACR7C,QAAS,QACTiF,KAAM,QACNrC,WAAY,CAAC,SAAU,WACvB2xB,OAAQ,oBARE,CACV5xB,QAAS,EAAGe,QAAUnG,IAAAA,MAAgB8B,EAAUhQ,GAAK,2BAA2BkO,UAChFmG,OAAQ,EAAGA,QAAUnG,IAAAA,MAAgB8B,EAAUlP,CAAG,WAAWoN,MAQ7D7N,KAAKmT,GACD,MAAMxE,OAAEA,EAAMuF,aAAEA,EAAYxF,GAAEA,GAAOyE,GAC/B01B,YAAEA,GAAgB30B,EACxBxF,EAAGhE,OAAQ,GACP,EAAIgK,EAAOyF,mBAAmBzL,EAAIC,KAElCk6B,GACA,EAAIC,EAAkBT,yBAAyBl1B,EAAK01B,GAEpD11B,EAAIiK,IAAG,EAAI1a,EAAO6lC,eAAep1B,gFCfjC,CACR7C,QAAS,WACTiF,KAAM,QACNrC,WAAY,CAAC,SAAU,WACvB2xB,OAAQ,cACR7d,aAAa,QAXH,CACV/T,QAAS,EAAGe,QAAU+0B,IAAAA,EAAKl4B,IAAAA,WAAoBzP,IAARyP,EAC7BlB,EAAUhQ,GAAK,yBAAyBopC,kBACxCp5B,EAAUhQ,GAAK,yBAAyBopC,sBAAwBl4B,kBAC1EmD,OAAQ,EAAGA,QAAU+0B,IAAAA,EAAKl4B,IAAAA,WAAoBzP,IAARyP,EAAwBlB,EAAUlP,CAAG,iBAAiBsoC,KAAap5B,EAAUlP,CAAG,iBAAiBsoC,mBAAqBl4B,MAS5J7Q,KAAKmT,GACD,MAAMzD,IAAEA,EAAGf,OAAEA,EAAMuF,aAAEA,EAAYzC,KAAEA,EAAI/C,GAAEA,GAAOyE,EAChD,IAAI41B,EACAl4B,EACJ,MAAMm4B,YAAEA,EAAWC,YAAEA,GAAgB/0B,EACjCxF,EAAGzK,KAAKqsB,MACRyY,OAAsB3nC,IAAhB4nC,EAA4B,EAAIA,EACtCn4B,EAAMo4B,GAGNF,EAAM,EAEV,MAAMl7B,EAAM6B,EAAIjN,MAAM,MAAWkN,EAAUlP,CAAG,GAAGgR,YAEjD,GADA0B,EAAI4G,UAAU,CAAEgvB,IAAAA,EAAKl4B,IAAAA,SACTzP,IAARyP,GAA6B,IAARk4B,EAErB,YADA,EAAIr0B,EAAOzF,iBAAiBP,EAAI,wEAGpC,QAAYtN,IAARyP,GAAqBk4B,EAAMl4B,EAG3B,OAFA,EAAI6D,EAAOzF,iBAAiBP,EAAI,wDAChCyE,EAAIiU,OAGR,IAAI,EAAI1S,EAAOyF,mBAAmBzL,EAAIC,GAAS,CAC3C,IAAIxF,EAAWwG,EAAUlP,CAAG,GAAGoN,QAAUk7B,IAIzC,YAHY3nC,IAARyP,IACA1H,EAAWwG,EAAUlP,CAAG,GAAG0I,QAAW0E,QAAUgD,UACpDsC,EAAI+I,KAAK/S,GAGbuF,EAAGhE,OAAQ,EACX,MAAMyK,EAAQzF,EAAInN,KAAK,SAcvB,SAAS2mC,IACL,MAAM/tB,EAAWzL,EAAInN,KAAK,UACpB+e,EAAQ5R,EAAI/M,IAAI,QAAS,GAC/BoY,EAAcI,EAAU,IAAMzL,EAAI5D,GAAGqP,EAAU,IAanD,SAAqBmG,GACjB5R,EAAI1P,KAAS2P,EAAUlP,CAAG,GAAG6gB,YACjBlgB,IAARyP,EACAnB,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAG6gB,QAAYynB,IAAO,IAAMr5B,EAAIhE,OAAOyJ,GAAO,GAAMlI,UAG5EyC,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAG6gB,OAAWzQ,IAAO,IAAMnB,EAAIhE,OAAOyJ,GAAO,GAAOlI,SAChE,IAAR87B,EACAr5B,EAAIhE,OAAOyJ,GAAO,GAElBzF,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAG6gB,QAAYynB,IAAO,IAAMr5B,EAAIhE,OAAOyJ,GAAO,KAvBzBg0B,CAAY7nB,KAErE,SAASvG,EAAcquB,EAAQ37B,GAC3BiC,EAAI/C,SAAS,IAAK,EAAGkB,EAAMjN,IACvBuS,EAAI8H,UAAU,CACV3K,QAAS,WACTc,SAAUxQ,EACVyQ,aAAcqD,EAAO3D,KAAKS,IAC1B+B,eAAe,GAChB61B,GACH37B,WA1BIrM,IAARyP,GAA6B,IAARk4B,EACrBhuB,EAAc5F,EAAO,IAAMzF,EAAI5D,GAAGqJ,EAAO,IAAMzF,EAAIzC,UAEtC,IAAR87B,GACLr5B,EAAI/M,IAAIwS,GAAO,QACH/T,IAARyP,GACAnB,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGgR,eAAmBy3B,KAGlDx5B,EAAI/M,IAAIwS,GAAO,GACf+zB,KAEJ/1B,EAAIqI,OAAOrG,EAAO,IAAMhC,EAAIsI,wDC5DpC3c,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,qBAA6BA,uBAA+BA,aAAgB,EAI5EA,QAAgB,CACZgU,QAAS,EAAGe,QAAUnQ,SAAAA,EAAUwlC,UAAAA,EAAW9rB,KAAAA,MAE5B5N,EAAUhQ,GAAK,aADS,IAAd0pC,EAAkB,WAAa,gBACG9rB,mBAAsB1Z,eAEjFmQ,OAAQ,EAAGA,QAAUnQ,SAAAA,EAAUwlC,UAAAA,EAAW9rB,KAAAA,EAAMvD,gBAAAA,MAA4BrK,EAAUlP,CAAG,cAAcoD;uBACpFmW;iBACNqvB;YACL9rB,MAEZ,MAAM3B,EAAM,CACRtL,QAAS,eACTiF,KAAM,SACNrC,WAAY,SACZ7K,MAAOpJ,EAAQoJ,MACfrI,KAAKmT,GACD,MAAOm2B,EAAUC,GAKzB,UAA2B56B,OAAEA,IACzB,MAAM66B,EAAe,GACfC,EAAa,GACnB,IAAK,MAAMrnC,KAAOuM,EACF,cAARvM,KAESX,MAAMC,QAAQiN,EAAOvM,IAAQonC,EAAeC,GACpDrnC,GAAOuM,EAAOvM,IAEvB,MAAO,CAAConC,EAAcC,GAdUC,CAAkBv2B,GAC9Cw2B,EAAqBx2B,EAAKm2B,GAC1BM,EAAmBz2B,EAAKo2B,KAchC,SAASI,EAAqBx2B,EAAKq2B,EAAer2B,EAAIxE,QAClD,MAAMe,IAAEA,EAAG+B,KAAEA,EAAI/C,GAAEA,GAAOyE,EAC1B,GAAyC,IAArCrU,OAAOkR,KAAKw5B,GAActpC,OAC1B,OACJ,MAAM+Z,EAAUvK,EAAI/M,IAAI,WACxB,IAAK,MAAMoW,KAAQywB,EAAc,CAC7B,MAAMjsB,EAAOisB,EAAazwB,GAC1B,GAAoB,IAAhBwE,EAAKrd,OACL,SACJ,MAAM2pC,GAAc,EAAInnC,EAAOokC,gBAAgBp3B,EAAK+B,EAAMsH,EAAMrK,EAAGzK,KAAK+I,eACxEmG,EAAI4G,UAAU,CACVlW,SAAUkV,EACVswB,UAAW9rB,EAAKrd,OAChBqd,KAAMA,EAAK5b,KAAK,QAEhB+M,EAAG8E,UACH9D,EAAI5D,GAAG+9B,EAAa,KAChB,IAAK,MAAMC,KAAWvsB,GAClB,EAAI7a,EAAOkkC,wBAAwBzzB,EAAK22B,MAKhDp6B,EAAI5D,GAAO6D,EAAUlP,CAAG,GAAGopC,UAAmB,EAAInnC,EAAOskC,kBAAkB7zB,EAAKoK,EAAMtD,QACtF,EAAIvX,EAAOukC,mBAAmB9zB,EAAK8G,GACnCvK,EAAIxG,SAKhB,SAAS0gC,EAAmBz2B,EAAKs2B,EAAat2B,EAAIxE,QAC9C,MAAMe,IAAEA,EAAG+B,KAAEA,EAAInB,QAAEA,EAAO5B,GAAEA,GAAOyE,EAC7BgC,EAAQzF,EAAInN,KAAK,SACvB,IAAK,MAAMwW,KAAQ0wB,GACX,EAAI/0B,EAAOyF,mBAAmBzL,EAAI+6B,EAAW1wB,MAEjDrJ,EAAI5D,IAAG,EAAIpJ,EAAOokC,gBAAgBp3B,EAAK+B,EAAMsH,EAAMrK,EAAGzK,KAAK+I,eAAgB,KACvE,MAAMqO,EAASlI,EAAI8H,UAAU,CAAE3K,QAAAA,EAASgL,WAAYvC,GAAQ5D,GAC5DhC,EAAIoI,oBAAoBF,EAAQlG,IACjC,IAAMzF,EAAI9M,IAAIuS,GAAO,IAExBhC,EAAIiK,GAAGjI,IAZflW,uBAA+B0qC,EAe/B1qC,qBAA6B2qC,EAC7B3qC,UAAkB2c,mDC3EN,CACRtL,QAAS,gBACTiF,KAAM,SACNrC,WAAY,CAAC,SAAU,iBAPb,CACVD,QAAS,8BACTe,OAAQ,EAAGA,OAAAA,KAAiBrE,EAAUlP,CAAG,kBAAkBuT,EAAOC,iBAOlEjU,KAAKmT,GACD,MAAMzD,IAAEA,EAAGf,OAAEA,EAAM8C,KAAEA,EAAI/C,GAAEA,GAAOyE,EAClC,IAAI,EAAIuB,EAAOyF,mBAAmBzL,EAAIC,GAClC,OACJ,MAAMwG,EAAQzF,EAAInN,KAAK,SACvBmN,EAAI5C,MAAM,MAAO2E,EAAOrP,IACpB+Q,EAAI4G,UAAU,CAAE9F,aAAc7R,IAC9B+Q,EAAI8H,UAAU,CACV3K,QAAS,gBACTmB,KAAMrP,EACNoW,UAAW,CAAC,UACZvE,aAAc7R,EACdmR,eAAe,GAChB4B,GACHzF,EAAI5D,IAAG,EAAI6D,EAAUrG,KAAK6L,GAAQ,KAC9BhC,EAAI9K,OAAM,GACLqG,EAAG8E,WACJ9D,EAAIzC,YAGhBkG,EAAIiK,GAAGjI,+BC3Bf,MAIMyG,GAAM,CACRtL,QAAS,uBACTiF,KAAM,CAAC,UACPrC,WAAY,CAAC,UAAW,UACxBmK,gBAAgB,EAChB2J,aAAa,QATH,CACV/T,QAAS,sCACTe,OAAQ,EAAGA,OAAAA,KAAiBrE,EAAUlP,CAAG,wBAAwBuT,EAAO+1B,uBASxE/pC,KAAKmT,GACD,MAAMzD,IAAEA,EAAGf,OAAEA,EAAMuF,aAAEA,EAAYzC,KAAEA,EAAIiC,UAAEA,EAAShF,GAAEA,GAAOyE,EAE3D,IAAKO,EACD,MAAM,IAAIjU,MAAM,4BACpB,MAAM+T,UAAEA,EAASvP,KAAEA,GAASyK,EAE5B,GADAA,EAAGoB,OAAQ,EACmB,QAA1B7L,EAAK+lC,mBAA8B,EAAIt1B,EAAOyF,mBAAmBzL,EAAIC,GACrE,OACJ,MAAMmB,GAAQ,EAAIpN,EAAOmX,qBAAqB3F,EAAakF,YACrD6wB,GAAW,EAAIvnC,EAAOmX,qBAAqB3F,EAAa+L,mBA6B9D,SAASiqB,EAAiB9nC,GACtBsN,EAAI1P,KAAS2P,EAAUlP,CAAG,UAAUgR,KAAQrP,MAEhD,SAAS+nC,EAAuB/nC,GAC5B,GAA8B,QAA1B6B,EAAK+lC,kBAA+B/lC,EAAK+lC,mBAA+B,IAAXr7B,EAC7Du7B,EAAiB9nC,OADrB,CAIA,IAAe,IAAXuM,EAKA,OAJAwE,EAAI4G,UAAU,CAAEgwB,mBAAoB3nC,IACpC+Q,EAAI9K,aACCmL,GACD9D,EAAIzC,SAGZ,GAAqB,iBAAV0B,KAAuB,EAAI+F,EAAOyF,mBAAmBzL,EAAIC,GAAS,CACzE,MAAMwG,EAAQzF,EAAInN,KAAK,SACO,YAA1B0B,EAAK+lC,kBACLI,EAAsBhoC,EAAK+S,GAAO,GAClCzF,EAAI5D,IAAG,EAAI6D,EAAUrG,KAAK6L,GAAQ,KAC9BhC,EAAIsI,QACJyuB,EAAiB9nC,OAIrBgoC,EAAsBhoC,EAAK+S,GACtB3B,GACD9D,EAAI5D,IAAG,EAAI6D,EAAUrG,KAAK6L,GAAQ,IAAMzF,EAAIzC,YAI5D,SAASm9B,EAAsBhoC,EAAK+S,EAAOlD,GACvC,MAAMgJ,EAAY,CACd3K,QAAS,uBACTc,SAAUhP,EACViP,aAAcqD,EAAO3D,KAAK6D,MAEf,IAAX3C,GACAnT,OAAO4M,OAAOuP,EAAW,CACrB1H,eAAe,EACfY,cAAc,EACdX,WAAW,IAGnBL,EAAI8H,UAAUA,EAAW9F,GArEzBzF,EAAI5C,MAAM,MAAO2E,EAAOrP,IACf0N,EAAM5P,QAAW+pC,EAAS/pC,OAG3BwP,EAAI5D,GAGhB,SAAsB1J,GAClB,IAAIioC,EACJ,GAAIv6B,EAAM5P,OAAS,EAAG,CAElB,MAAMoqC,GAAc,EAAI51B,EAAOkE,gBAAgBlK,EAAIwF,EAAakF,WAAY,cAC5EixB,GAAc,EAAI3nC,EAAOgX,eAAehK,EAAK46B,EAAaloC,QAG1DioC,EADKv6B,EAAM5P,QACG,EAAIyP,EAAUiK,OAAO9J,EAAMoK,IAAKjK,GAAUN,EAAUlP,CAAG,GAAG2B,SAAW6N,MAGrEN,EAAUpL,IAK5B,OAHI0lC,EAAS/pC,SACTmqC,GAAc,EAAI16B,EAAUiK,IAAIywB,KAAgBJ,EAAS/vB,IAAKjK,GAAUN,EAAUlP,CAAG,IAAG,EAAIiC,EAAO+jC,YAAYtzB,EAAKlD,WAAW7N,SAE5H,EAAIuN,EAAUrG,KAAK+gC,GAnBXE,CAAanoC,GAAM,IAAM+nC,EAAuB/nC,IAFvD+nC,EAAuB/nC,KAJnC+Q,EAAIiK,GAAOzN,EAAUlP,CAAG,GAAGiT,SAAiBjB,EAAQC,QAAQT,YA2EpE,mDAAkB2J,4EClGN,CACRtL,QAAS,aACTiF,KAAM,SACNrC,WAAY,SACZlT,KAAKmT,GACD,MAAMzD,IAAEA,EAAGf,OAAEA,EAAMuF,aAAEA,EAAYzC,KAAEA,EAAI/C,GAAEA,GAAOyE,EACf,QAA7BzE,EAAGzK,KAAK+lC,uBAAoE5oC,IAAtC8S,EAAauL,sBACnD+qB,GAAuB93B,QAAQ1S,KAAK,IAAI+qB,EAAWpE,WAAWjY,EAAI87B,GAAuB93B,QAAS,yBAEtG,MAAM+3B,GAAW,EAAI/nC,EAAOmX,qBAAqBlL,GACjD,IAAK,MAAMoK,KAAQ0xB,EACf/7B,EAAGsP,kBAAkB5S,IAAI2N,GAEzBrK,EAAGzK,KAAKiX,aAAeuvB,EAASvqC,SAAuB,IAAbwO,EAAGoB,QAC7CpB,EAAGoB,MAAQ4E,EAAOuU,eAAenZ,MAAMJ,GAAK,EAAIgF,EAAO+D,QAAQgyB,GAAW/7B,EAAGoB,QAEjF,MAAMsJ,EAAaqxB,EAASzzB,OAAQ/G,KAAO,EAAIyE,EAAOyF,mBAAmBzL,EAAIC,EAAOsB,KACpF,GAA0B,IAAtBmJ,EAAWlZ,OACX,OACJ,MAAMiV,EAAQzF,EAAInN,KAAK,SACvB,IAAK,MAAMwW,KAAQK,EACXsxB,EAAW3xB,GACX4xB,EAAoB5xB,IAGpBrJ,EAAI5D,IAAG,EAAIpJ,EAAOokC,gBAAgBp3B,EAAK+B,EAAMsH,EAAMrK,EAAGzK,KAAK+I,gBAC3D29B,EAAoB5xB,GACfrK,EAAG8E,WACJ9D,EAAIxG,OAAOtG,IAAIuS,GAAO,GAC1BzF,EAAIxD,SAERiH,EAAIzE,GAAGsP,kBAAkB5S,IAAI2N,GAC7B5F,EAAIiK,GAAGjI,GAEX,SAASu1B,EAAW3xB,GAChB,OAAOrK,EAAGzK,KAAKiV,cAAgBxK,EAAG6E,oBAA0CnS,IAAzBuN,EAAOoK,GAAMrG,QAEpE,SAASi4B,EAAoB5xB,GACzB5F,EAAI8H,UAAU,CACV3K,QAAS,aACTgL,WAAYvC,EACZ3H,SAAU2H,GACX5D,gCC3Cf,MAAMmE,GAAS5E,EAoEf,mDAnEY,CACRpE,QAAS,oBACTiF,KAAM,SACNrC,WAAY,SACZlT,KAAKmT,GACD,MAAMzD,IAAEA,EAAGf,OAAEA,EAAM8C,KAAEA,EAAIyC,aAAEA,EAAYxF,GAAEA,GAAOyE,GAC1ClP,KAAEA,GAASyK,EACXk8B,GAAW,EAAIloC,EAAOmX,qBAAqBlL,GAC3Ck8B,EAAsBD,EAAS5zB,OAAQ/G,IAAM,EAAIyE,EAAOyF,mBAAmBzL,EAAIC,EAAOsB,KAC5F,GAAwB,IAApB26B,EAAS1qC,QACR2qC,EAAoB3qC,SAAW0qC,EAAS1qC,UACnCwO,EAAGzK,KAAKiX,cAA4B,IAAbxM,EAAGoB,OAChC,OAEJ,MAAMg7B,EAAkB7mC,EAAK4K,eAAiB5K,EAAK8mC,yBAA2B72B,EAAakF,WACrFjE,EAAQzF,EAAInN,KAAK,UACN,IAAbmM,EAAGoB,OAAoBpB,EAAGoB,iBAAiBH,EAAUxQ,OACrDuP,EAAGoB,OAAQ,EAAIwJ,GAAO1J,sBAAsBF,EAAKhB,EAAGoB,QAExD,MAAMA,MAAEA,GAAUpB,EAgBlB,SAASs8B,EAAwBC,GAC7B,IAAK,MAAMlyB,KAAQ+xB,EACX,IAAIzsB,OAAO4sB,GAAKzrC,KAAKuZ,KACrB,EAAIrE,EAAOzF,iBAAiBP,EAAI,YAAYqK,qBAAwBkyB,mCAIhF,SAASC,EAAmBD,GACxBv7B,EAAI5C,MAAM,MAAO2E,EAAOrP,IACpBsN,EAAI5D,GAAO6D,EAAUlP,CAAG,IAAG,EAAIiC,EAAO+jC,YAAYtzB,EAAK83B,WAAa7oC,KAAQ,KACxE,MAAM+oC,EAAcN,EAAoBj0B,SAASq0B,GAC5CE,GACDh4B,EAAI8H,UAAU,CACV3K,QAAS,oBACTgL,WAAY2vB,EACZ75B,SAAUhP,EACViP,aAAciI,GAAOvI,KAAK6D,KAC3BO,GAEHzG,EAAGzK,KAAKiX,cAAyB,IAAVpL,EACvBJ,EAAIhE,OAAWiE,EAAUlP,CAAG,GAAGqP,KAAS1N,MAAQ,GAE1C+oC,GAAgBz8B,EAAG8E,WAGzB9D,EAAI5D,IAAG,EAAI6D,EAAUrG,KAAK6L,GAAQ,IAAMzF,EAAIzC,cAvC5D,WACI,IAAK,MAAMg+B,KAAOL,EACVE,GACAE,EAAwBC,GACxBv8B,EAAG8E,UACH03B,EAAmBD,IAGnBv7B,EAAI9M,IAAIuS,GAAO,GACf+1B,EAAmBD,GACnBv7B,EAAI5D,GAAGqJ,IAXnBi2B,8ECvBI,CACR96B,QAAS,MACT4C,WAAY,CAAC,SAAU,WACvB8T,aAAa,EACbhnB,KAAKmT,GACD,MAAMzD,IAAEA,EAAGf,OAAEA,EAAMD,GAAEA,GAAOyE,EAC5B,IAAI,EAAIuB,EAAOyF,mBAAmBzL,EAAIC,GAElC,YADAwE,EAAIiU,OAGR,MAAMjS,EAAQzF,EAAInN,KAAK,SACvB4Q,EAAI8H,UAAU,CACV3K,QAAS,MACTiD,eAAe,EACfY,cAAc,EACdX,WAAW,GACZ2B,GACHhC,EAAIgU,WAAWhS,EAAO,IAAMhC,EAAIsI,QAAS,IAAMtI,EAAI9K,UAEvDA,MAAO,CAAE4K,QAAS,+FCnBV,CACR3C,QAAS,QACT4C,WAAY,QACZ8T,aAAa,EACbhnB,KAAM0C,EAAO2oC,cACbhjC,MAAO,CAAE4K,QAAS,0GCAV,CACR3C,QAAS,QACT4C,WAAY,QACZ8T,aAAa,QAPH,CACV/T,QAAS,yCACTe,OAAQ,EAAGA,OAAAA,KAAiBrE,EAAUlP,CAAG,oBAAoBuT,EAAOs3B,YAOpEtrC,KAAKmT,GACD,MAAMzD,IAAEA,EAAGf,OAAEA,EAAMuF,aAAEA,EAAYxF,GAAEA,GAAOyE,EAE1C,IAAK1R,MAAMC,QAAQiN,GACf,MAAM,IAAIlP,MAAM,4BACpB,GAAIiP,EAAGzK,KAAKsnC,eAAiBr3B,EAAaq3B,cACtC,OACJ,MAAM9C,EAAS95B,EACTwG,EAAQzF,EAAI/M,IAAI,SAAS,GACzB2oC,EAAU57B,EAAI/M,IAAI,UAAW,MAC7BwY,EAAWzL,EAAInN,KAAK,UAC1B4Q,EAAI4G,UAAU,CAAEuxB,QAAAA,IAEhB57B,EAAIjC,MAEJ,WACIg7B,EAAO/iC,QAAQ,CAAC2T,EAAKzY,KACjB,IAAIya,GACA,EAAI3G,EAAOyF,mBAAmBzL,EAAI2K,GAClC3J,EAAI9M,IAAIuY,GAAU,GAGlBE,EAASlI,EAAI8H,UAAU,CACnB3K,QAAS,QACTgL,WAAY1a,EACZ2S,eAAe,GAChB4H,GAEHva,EAAI,GACJ8O,EACK5D,GAAO6D,EAAUlP,CAAG,GAAG0a,QAAehG,KACtCzJ,OAAOyJ,GAAO,GACdzJ,OAAO4/B,EAAa37B,EAAUlP,CAAG,IAAI6qC,MAAY1qC,MACjDsI,OAETwG,EAAI5D,GAAGqP,EAAU,KACbzL,EAAIhE,OAAOyJ,GAAO,GAClBzF,EAAIhE,OAAO4/B,EAAS1qC,GAChBya,GACAlI,EAAI8V,eAAe5N,EAAQ1L,EAAUxQ,YAzBrDgU,EAAIqI,OAAOrG,EAAO,IAAMhC,EAAIsI,QAAS,IAAMtI,EAAI9K,OAAM,+ECxBjD,CACRiI,QAAS,QACT4C,WAAY,QACZlT,KAAKmT,GACD,MAAMzD,IAAEA,EAAGf,OAAEA,EAAMD,GAAEA,GAAOyE,EAE5B,IAAK1R,MAAMC,QAAQiN,GACf,MAAM,IAAIlP,MAAM,4BACpB,MAAM0V,EAAQzF,EAAInN,KAAK,SACvBoM,EAAOjJ,QAAQ,CAAC2T,EAAKzY,KACjB,IAAI,EAAI8T,EAAOyF,mBAAmBzL,EAAI2K,GAClC,OACJ,MAAMgC,EAASlI,EAAI8H,UAAU,CAAE3K,QAAS,QAASgL,WAAY1a,GAAKuU,GAClEhC,EAAIiK,GAAGjI,GACPhC,EAAI8V,eAAe5N,iCC2C/B,SAASmwB,GAAU98B,EAAI4B,GACnB,MAAM3B,EAASD,EAAGC,OAAO2B,GACzB,YAAkBlP,IAAXuN,KAAyB,EAAI+F,EAAOyF,mBAAmBzL,EAAIC,GAEtE,mDAxDY,CACR2B,QAAS,KACT4C,WAAY,CAAC,SAAU,WACvB8T,aAAa,QAPH,CACV/T,QAAS,EAAGe,OAAAA,KAAiBrE,EAAUhQ,GAAK,eAAeqU,EAAOy3B,mBAClEz3B,OAAQ,EAAGA,OAAAA,KAAiBrE,EAAUlP,CAAG,oBAAoBuT,EAAOy3B,aAOpEzrC,KAAKmT,GACD,MAAMzD,IAAEA,EAAGwE,aAAEA,EAAYxF,GAAEA,GAAOyE,OACR/R,IAAtB8S,EAAayL,WAA4Cve,IAAtB8S,EAAahL,OAChD,EAAIwL,EAAOzF,iBAAiBP,EAAI,6CAEpC,MAAMg9B,EAAUF,GAAU98B,EAAI,QACxBi9B,EAAUH,GAAU98B,EAAI,QAC9B,IAAKg9B,IAAYC,EACb,OACJ,MAAMx2B,EAAQzF,EAAI/M,IAAI,SAAS,GACzBwY,EAAWzL,EAAInN,KAAK,UAG1B,GAYA,WACI,MAAM8Y,EAASlI,EAAI8H,UAAU,CACzB3K,QAAS,KACTiD,eAAe,EACfY,cAAc,EACdX,WAAW,GACZ2H,GACHhI,EAAI8V,eAAe5N,GArBvBuwB,GACAz4B,EAAIsI,QACAiwB,GAAWC,EAAS,CACpB,MAAMF,EAAW/7B,EAAI/M,IAAI,YACzBwQ,EAAI4G,UAAU,CAAE0xB,SAAAA,IAChB/7B,EAAI5D,GAAGqP,EAAU0wB,EAAe,OAAQJ,GAAWI,EAAe,OAAQJ,SAErEC,EACLh8B,EAAI5D,GAAGqP,EAAU0wB,EAAe,SAGhCn8B,EAAI5D,IAAG,EAAI6D,EAAUrG,KAAK6R,GAAW0wB,EAAe,SAYxD,SAASA,EAAev7B,EAASm7B,GAC7B,MAAO,KACH,MAAMpwB,EAASlI,EAAI8H,UAAU,CAAE3K,QAAAA,GAAW6K,GAC1CzL,EAAIhE,OAAOyJ,EAAOgG,GAClBhI,EAAIoI,oBAAoBF,EAAQlG,GAC5Bs2B,EACA/7B,EAAIhE,OAAO+/B,EAAc97B,EAAUlP,CAAG,GAAG6P,KAEzC6C,EAAI4G,UAAU,CAAE0xB,SAAUn7B,KAlBtC6C,EAAI+I,KAAK/G,EAAO,IAAMhC,EAAI9K,OAAM,+EClC5B,CACRiI,QAAS,CAAC,OAAQ,QAClB4C,WAAY,CAAC,SAAU,WACvBlT,MAAKsQ,QAAEA,EAAO4D,aAAEA,EAAYxF,GAAEA,SACFtN,IAApB8S,EAAapI,KACb,EAAI4I,EAAOzF,iBAAiBP,EAAI,IAAI4B,yGCUhD,SAAuBw7B,GAAY,GAC/B,MAAMC,EAAa,CAEfC,GAAMt5B,QACNu5B,GAAQv5B,QACRw5B,GAAQx5B,QACRy5B,GAAQz5B,QACR05B,GAAK15B,QACL25B,GAAW35B,QAEX45B,GAAgB55B,QAChB83B,GAAuB93B,QACvB65B,GAAe75B,QACf85B,GAAa95B,QACb+5B,GAAoB/5B,SAQxB,OALIo5B,EACAC,EAAWjrC,KAAK4rC,GAAch6B,QAASi6B,GAAYj6B,SAEnDq5B,EAAWjrC,KAAKgoC,EAAkBp2B,QAASk2B,GAAQl2B,SACvDq5B,EAAWjrC,KAAK8rC,GAAWl6B,SACpBq5B,4ECrCI,6CCIH,CACRz7B,QAAS,SACTiF,KAAM,CAAC,SAAU,UACjBrC,WAAY,SACZ3C,OAAO,QARG,CACV0C,QAAS,EAAGiC,WAAAA,KAAqBvF,EAAUhQ,GAAK,sBAAsBuV,KACtElB,OAAQ,EAAGkB,WAAAA,KAAqBvF,EAAUlP,CAAG,YAAYyU,MAQzDlV,KAAKmT,EAAKgW,GACN,MAAMzZ,IAAEA,EAAG+B,KAAEA,EAAIlB,MAAEA,EAAK5B,OAAEA,EAAMuG,WAAEA,EAAUxG,GAAEA,GAAOyE,GAC/ClP,KAAEA,EAAI4P,cAAEA,EAAaf,UAAEA,EAASlE,KAAEA,GAASF,EAC5CzK,EAAKy7B,kBAENnvB,EAIJ,WACI,MAAMs8B,EAAOn9B,EAAIzE,WAAW,UAAW,CACnCxG,IAAKmK,EAAKixB,QACV7/B,KAAMiE,EAAKjE,KAAK6/B,UAEdiN,EAAOp9B,EAAIjN,MAAM,OAAYkN,EAAUlP,CAAG,GAAGosC,KAAQ33B,MACrD63B,EAAQr9B,EAAI/M,IAAI,SAChBge,EAASjR,EAAI/M,IAAI,UAEvB+M,EAAI5D,GAAO6D,EAAUlP,CAAG,UAAUqsC,sBAAyBA,uBAA2B,IAAMp9B,EAAIhE,OAAOqhC,EAAWp9B,EAAUlP,CAAG,GAAGqsC,sBAAyBphC,OAAOiV,EAAYhR,EAAUlP,CAAG,GAAGqsC,cAAkB,IAAMp9B,EAAIhE,OAAOqhC,EAAWp9B,EAAUlP,CAAG,YAAYiL,OAAOiV,EAAQmsB,IACpR35B,EAAIkU,WAAU,EAAI1X,EAAUiK,KAEE,IAAtB3V,EAAK4K,aACEc,EAAUpL,IACVoL,EAAUlP,CAAG,GAAGyU,SAAkByL,IAEjD,WACI,MAAMqsB,EAAal6B,EAAUC,OACnBpD,EAAUlP,CAAG,IAAIqsC,mBAAsBnsB,KAAUlP,QAAWkP,KAAUlP,MACtE9B,EAAUlP,CAAG,GAAGkgB,KAAUlP,KAC9Bw7B,EAAgBt9B,EAAUlP,CAAG,WAAWkgB,qBAA0BqsB,OAAgBrsB,UAAelP,MACvG,OAAW9B,EAAUlP,CAAG,GAAGkgB,QAAaA,iBAAsBosB,SAAa5jB,SAAgB8jB,IAXjDC,KAb9CC,GA2BJ,WACI,MAAMC,EAAYx+B,EAAKixB,QAAQlxB,GAC/B,IAAKy+B,EAED,YAOJ,WACI,IAA0B,IAAtBnpC,EAAK4K,aAIT,MAAM,IAAIpP,MAAM4tC,KAChB,SAASA,IACL,MAAO,mBAAmB1+B,iCAAsCkF,KALhEjF,EAAKsC,OAAOC,KAAKk8B,KAVrBC,GAGJ,IAAkB,IAAdF,EACA,OACJ,MAAOG,EAAS5sB,EAAQ6sB,GAaxB,SAAmBC,GACf,MAAMztC,EAAOytC,aAAkBpvB,QACzB,EAAI1O,EAAUxJ,YAAYsnC,GAC1BxpC,EAAKjE,KAAK6/B,QACFlwB,EAAUlP,CAAG,GAAGwD,EAAKjE,KAAK6/B,WAAU,EAAIlwB,EAAUzJ,aAAayI,UACnEvN,EACJssC,EAAMh+B,EAAIzE,WAAW,UAAW,CAAE7I,IAAKuM,EAAQlK,IAAKgpC,EAAQztC,KAAAA,IAClE,MAAqB,iBAAVytC,GAAwBA,aAAkBpvB,OAG9C,CAAC,SAAUovB,EAAQC,GAFf,CAACD,EAAOl4B,MAAQ,SAAUk4B,EAAOrxB,SAAczM,EAAUlP,CAAG,GAAGitC,cArB5CC,CAAUP,GACxCG,IAAYpkB,GACZhW,EAAI+I,KAuBR,WACI,GAAwB,iBAAbkxB,KAA2BA,aAAqB/uB,SAAW+uB,EAAUnjC,MAAO,CACnF,IAAK6I,EAAUC,OACX,MAAM,IAAItT,MAAM,+BACpB,OAAWkQ,EAAUlP,CAAG,SAAS+sC,KAAU/7B,KAE/C,MAAwB,mBAAVkP,EAA2BhR,EAAUlP,CAAG,GAAG+sC,KAAU/7B,KAAc9B,EAAUlP,CAAG,GAAG+sC,UAAe/7B,KA7BvGm8B,IAnCbC,gCDlBan7B,sDEFzB5T,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,oBAA4BA,0BAA6B,EACzDA,qBAA6B,CACzB,QACA,cACA,UACA,aACA,WACA,YACA,YAEJA,oBAA4B,CACxB,mBACA,kBACA,mBCRJ,MAAM6uC,GAAqB,CACvBC,EAAOr7B,QACPs7B,EAAat7B,SACb,EAAIu7B,GAAav7B,WACjBw7B,GAASx7B,QACTy7B,GAAWC,mBACXD,GAAWE,mBAEf,mDAAkBP,gDCXlB,IAAWQ,EAHXxvC,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,kBAAqB,GAEVqvC,EAGKrvC,EAAQqvC,aAAervC,aAAqB,KAFxC,IAAI,MACpBqvC,EAAoB,QAAI,2DCMhB,CACRh+B,QAAS,gBACTiF,KAAM,SACNrC,WAAY,SACZ7K,MAVU,CACV4K,QAAS,EAAGe,QAAUu6B,WAAAA,EAAYC,QAAAA,MAAgBD,IAAeE,GAAQH,WAAWI,IAC9E,QAAQF,oBACR,iBAAiBA,sBACvBx6B,OAAQ,EAAGA,QAAUu6B,WAAAA,EAAYI,IAAAA,EAAKH,QAAAA,MAAoB7+B,EAAUlP,CAAG,WAAW8tC,WAAoBC,gBAAsBG,MAO5H3uC,KAAKmT,GACD,MAAMzD,IAAEA,EAAG+B,KAAEA,EAAI9C,OAAEA,EAAMuF,aAAEA,EAAYxF,GAAEA,GAAOyE,GAC1C2M,MAAEA,GAAU5L,EAClB,IAAKxF,EAAGzK,KAAKsnC,cACT,MAAM,IAAI9rC,MAAM,gDAEpB,MAAM+uC,EAAU7/B,EAAOsF,aACvB,GAAsB,iBAAXu6B,EACP,MAAM,IAAI/uC,MAAM,wCACpB,GAAIkP,EAAOigC,QACP,MAAM,IAAInvC,MAAM,2CACpB,IAAKqgB,EACD,MAAM,IAAIrgB,MAAM,yCACpB,MAAM0V,EAAQzF,EAAI/M,IAAI,SAAS,GACzBgsC,EAAMj/B,EAAIjN,MAAM,MAAWkN,EAAUlP,CAAG,GAAGgR,KAAO,EAAI9B,EAAUzJ,aAAasoC,MAcnF,SAASK,EAAevzB,GACpB,MAAM8tB,EAAS15B,EAAInN,KAAK,SAClB8Y,EAASlI,EAAI8H,UAAU,CAAE3K,QAAS,QAASgL,WAAAA,GAAc8tB,GAE/D,OADAj2B,EAAI8V,eAAe5N,EAAQ1L,EAAUxQ,MAC9BiqC,EAjBX15B,EAAI5D,GAAO6D,EAAUlP,CAAG,UAAUkuC,gBAAmB,IAErD,WACI,MAAMC,EAgBV,WACI,IAAIxuC,EACJ,MAAM0uC,EAAe,GACfC,EAAcC,EAAY96B,GAChC,IAAI+6B,GAAc,EAClB,IAAK,IAAIruC,EAAI,EAAGA,EAAIkf,EAAM5f,OAAQU,IAAK,CACnC,IAAIyY,EAAMyG,EAAMlf,IACXyY,MAAAA,OAAiC,EAASA,EAAI+K,SAAU,EAAI1P,EAAO4P,sBAAsBjL,EAAK3K,EAAGE,KAAKG,SACvGsK,EAAMspB,EAAUyC,WAAWrpB,KAAKrN,EAAGE,KAAMF,EAAGoE,UAAUkS,KAAMtW,EAAGyT,OAAQ9I,MAAAA,OAAiC,EAASA,EAAI+K,MACjH/K,aAAespB,EAAUtY,YACzBhR,EAAMA,EAAI1K,SAElB,MAAMugC,EAA8E,QAAnE9uC,EAAKiZ,MAAAA,OAAiC,EAASA,EAAID,kBAA+B,IAAPhZ,OAAgB,EAASA,EAAGouC,GACxH,GAAsB,iBAAXU,EACP,MAAM,IAAIzvC,MAAM,iFAAiF+uC,MAErGS,EAAcA,IAAgBF,GAAeC,EAAY31B,IACzD81B,EAAYD,EAAStuC,GAEzB,IAAKquC,EACD,MAAM,IAAIxvC,MAAM,mBAAmB+uC,uBACvC,OAAOM,EACP,SAASE,GAAY7uB,SAAEA,IACnB,OAAO1e,MAAMC,QAAQye,IAAaA,EAASvJ,SAAS43B,GAExD,SAASW,EAAY91B,EAAKzY,GACtB,GAAIyY,EAAI5W,MACJ2sC,EAAW/1B,EAAI5W,MAAO7B,OAErB,CAAA,IAAIyY,EAAI6G,KAMT,MAAM,IAAIzgB,MAAM,8BAA8B+uC,kCAL9C,IAAK,MAAMa,KAAYh2B,EAAI6G,KACvBkvB,EAAWC,EAAUzuC,IAOjC,SAASwuC,EAAWC,EAAUzuC,GAC1B,GAAuB,iBAAZyuC,GAAwBA,KAAYP,EAC3C,MAAM,IAAIrvC,MAAM,mBAAmB+uC,oCAEvCM,EAAaO,GAAYzuC,GA1Db0uC,GAChB5/B,EAAI5D,IAAG,GACP,IAAK,MAAMujC,KAAYT,EACnBl/B,EAAIvD,OAAWwD,EAAUlP,CAAG,GAAGkuC,SAAWU,KAC1C3/B,EAAIhE,OAAOyJ,EAAO05B,EAAeD,EAAQS,KAE7C3/B,EAAIxG,OACJiK,EAAI9K,OAAM,EAAO,CAAEkmC,WAAYE,GAAQH,WAAWiB,QAASZ,IAAAA,EAAKH,QAAAA,IAChE9+B,EAAIxD,QAXmDsjC,GAAmB,IAAMr8B,EAAI9K,OAAM,EAAO,CAAEkmC,WAAYE,GAAQH,WAAWI,IAAKC,IAAAA,EAAKH,QAAAA,KAChJr7B,EAAIiK,GAAGjI,s8EChCfrW,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,UAAkBA,OAAeA,MAAcA,YAAoBA,MAAcA,IAAYA,kBAAqB,EAKlH,MAAMwwC,EAAoB,CAAC,eACrBC,EAAiB,yCACvB,MAAM9P,UAAYmO,EAAOr7B,QACrBguB,mBACIphC,MAAMohC,mBACNiP,GAASj9B,QAAQhN,QAAS27B,GAAM3hC,KAAKqjC,cAAc1B,IAC/C3hC,KAAKuE,KAAKsnC,eACV7rC,KAAKqhC,WAAW6O,GAAgBl9B,SAExCiuB,wBAEI,GADArhC,MAAMqhC,yBACDjhC,KAAKuE,KAAKmhB,KACX,OACJ,MAAMwe,EAAalkC,KAAKuE,KAAKsM,MACvB7Q,KAAKikC,gBAAgBkM,GAAkBJ,GACvCI,GACNnwC,KAAKmhC,cAAc+C,EAAY8L,GAAgB,GAC/ChwC,KAAKwjB,KAAK,iCAAmCwsB,EAEjDvO,cACI,OAAQzhC,KAAKuE,KAAKk9B,YACd7hC,MAAM6hC,gBAAkBzhC,KAAK4hC,UAAUoO,GAAkBA,OAAiBtuC,IAGtFsd,UAAiBzf,EAAU2gC,EAC3B9gC,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IACtDC,UAAkB2gC,EAElB9gC,OAAOC,eAAeE,EAAS,aAAc,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOumB,EAAWpE,cAEtG7nB,OAAOC,eAAeE,EAAS,IAAK,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOmL,EAAUlP,KAC5F3B,OAAOC,eAAeE,EAAS,MAAO,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOmL,EAAUhQ,OAC9Fb,OAAOC,eAAeE,EAAS,YAAa,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOmL,EAAU3N,aACpGlD,OAAOC,eAAeE,EAAS,MAAO,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOmL,EAAUpL,OAC9FzF,OAAOC,eAAeE,EAAS,OAAQ,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOmL,EAAUxQ,QAC/FL,OAAOC,eAAeE,EAAS,UAAW,CAAE+G,YAAY,EAAMxB,IAAK,WAAc,OAAOmL,EAAU+a,qECzClG5rB,OAAOC,iBAAwB,aAAc,CAAEC,OAAO,IAOtD,MAAMsR,EAAU,eACVw/B,EAAO,IAAIC,GAAM5wC,KAAK,UACtB6wC,EAA0B,CAC5B7vB,SAAU,kBACV3C,aAAc,WACdyyB,kBAAmB,YAEjBC,EAAgB,cAChBC,EAAwB,iBACxBC,EAAY,2BAClB,SAASC,EAAarY,GAClB,MAAO,CACH1nB,QAAAA,EACA4C,WAAY,CAAC,SAAU,UACvB4C,MAAM,EACN9V,KAAKmT,GACD,MAAMzD,IAAEA,EAAG+B,KAAEA,EAAI9C,OAAEA,EAAMgF,YAAEA,EAAWjF,GAAEA,GAAOyE,EAC/C,IAAwB,IAApBzE,EAAGyF,aACH,OACJ,MAAMkF,EAAM1K,EACNgD,EAAehC,EAAU1J,UAAUwM,EAAQC,QAAQf,aAAcjD,EAAGkF,WAyK1E,SAAS08B,EAAkB99B,EAAK+9B,GAC5B,OAAO5gC,EAAU4I,IAAIw3B,GAAMtvC,CAAE,GAAG+R,iBAAmBlC,IAAWy/B,GAAMtvC,CAAE,IAAI+R,KAAOs9B,IAAQC,GAAMtvC,CAAE,GAAG+R,sBAAwBb,IAAgBo+B,GAAMtvC,CAAE,GAAG+R,gBAAkB+9B,IAEzKR,GAAMtvC,CAAE,GAAG+R,wBAA0B9D,EAAGmF,uBAAwBk8B,GAAMtvC,CAAE,uBAAuB+R,sBAAwB9D,EAAGmF,cAAc3T,YAgB5I,SAASswC,EAAiBxgC,EAAMygC,GAC5B,MAAMC,EAAgB,GACtB,IAAK,MAAM1e,KAAKhiB,EAAM,CAClB,MAAMgB,EAAMy/B,EAAKze,GACbke,EAAc1wC,KAAKwR,IACnB0/B,EAAc5vC,KAAK,CAACkxB,EAAG2e,EAAa3/B,KAE5C,OAAOtB,EAAI9D,UAAU8kC,GAEzB,SAASE,EAAa5/B,GAClB,OAAKk/B,EAAc1wC,KAAKwR,GAEjB,IAAItO,EAAO3C,MAAM2C,EAAOzB,cAAc+P,GACxC/O,QAAQkuC,EAAuB,CAAC/f,EAAIygB,IAAQ,sBAAsB9lB,EAAWjE,QAAQ+pB,EAAKniC,WAC1FzM,QAAQmuC,EAAW,KAHbL,GAAM/tC,UAAUgP,GAK/B,SAAS2/B,EAAa3/B,GAClB,OAAO++B,GAAMtvC,CAAE,qBAAqBmwC,EAAa5/B,MA5MrDtB,EAAI5D,GAAGikC,GAAMtvC,CAAE,GAAGgS,EAAQC,QAAQT,aAAc,KAC5C,GAAkB,iBAAPoH,EAAiB,CACxB,MAAOy3B,EAAeC,GA2B9B,SAA6BC,GACzB,IAAIC,EACAh/B,EACJ,IAAK,MAAM+f,KAAKgf,EAAU,CACtB,GAAU,eAANhf,GAA4B,UAANA,EACtB,SACJ,MAAMkf,EAASF,EAAShf,GACxB,GAAqB,iBAAVkf,EAAoB,CAC3BD,IAAeA,EAAa,IAC5B,MAAME,EAAUF,EAAWjf,GAAK,GAChC,IAAK,MAAM/hB,KAAKihC,EACZC,EAAOlhC,GAAK,QAGhBgC,IAAWA,EAAS,IACpBA,EAAO+f,GAAK,GAGpB,MAAO,CAACif,EAAYh/B,GA7CmBm/B,CAAoB/3B,GACnD03B,GA8CZ,SAA8BA,GAC1B,MAAMR,EAAU7gC,EAAIjN,MAAM,WAAYstC,GAAM/tC,UAAU+uC,IAChDM,EAAY3hC,EAAIjN,MAAM,YAAa+tC,EAAiBO,EAAWpiC,IACrEe,EAAI9C,MAAM,MAAO6F,EAAQC,QAAQV,QAAUQ,GAAQ9C,EAAI5D,GAAGwkC,EAAkB99B,EAAK+9B,GAAU,IAAM7gC,EAAI1P,KAAK+vC,GAAMtvC,CAAE,GAAG8vC,KAAW/9B,mBAAqBA,MAAQ9G,OAAOqkC,GAAMtvC,CAAE,GAAG+R,KAAOs9B,KAAQ,KAC9L,MAAMwB,YAAEA,GAAgBtZ,EACxB,GAAIsZ,EAAa,CACb,MAAMr+B,EAAUvD,EAAI/M,IAAI,UAAWotC,GAAMtvC,CAAE,MACrC8wC,EAAe7hC,EAAI/M,IAAI,eAAgBotC,GAAMtvC,CAAE,MACrD+wC,EAAYpvC,IACRsN,EAAI5D,GAAGmH,EAAS,IAAMvD,EAAI1P,KAAK+vC,GAAMtvC,CAAE,GAAGwS,QAAoC,iBAAfq+B,EAA0BA,EAAc,QACvG5hC,EAAI1P,KAAK+vC,GAAMtvC,CAAE,GAAGwS,QAAcw+B,EAAWrvC,MAC7CsN,EAAIhE,OAAO6lC,EAAcxB,GAAMtvC,CAAE,GAAG8wC,YAAuBhB,KAAWnuC,SAE1E4S,EAASC,YAAY9B,EAAK,CAAEF,QAAAA,EAASe,OAAQ+7B,GAAMtvC,CAAE,YAAY8wC,YAGjEC,EAAYpvC,GAAQ4S,EAASC,YAAY9B,EAAK,CAC1CF,QAASw+B,EAAWrvC,GACpB4R,OAAQ+7B,GAAMtvC,CAAE,YAAY8vC,KAAWnuC,SAG/C,SAASovC,EAAW9jC,GAChBgC,EAAI5C,MAAM,MAAOyjC,EAAUnuC,GAAQsN,EAAI5D,GAAGikC,GAAMtvC,CAAE,GAAG8vC,KAAWnuC,YAAe,IAAMsL,EAAKtL,KAE9F,SAASqvC,EAAWrvC,GAChB,OAAO2tC,GAAMtvC,CAAE,GAAG2B,QAAUivC,OAAeA,KAAajvC,UAAYuR,KAAevR,MAtE/EsvC,CAAqBX,GACrBD,GAwEZ,SAAkCA,GAC9B,MAAMP,EAAU7gC,EAAIjN,MAAM,WAAYstC,GAAM/tC,UAAU8uC,IAChDJ,EAAgB,GACtB,IAAK,MAAM1e,KAAK8e,EACZJ,EAAc5vC,KAAK,CACfkxB,EACAwe,EAAiBM,EAAc9e,GAAIrjB,EAAOqjB,MAGlD,MAAMqf,EAAY3hC,EAAIjN,MAAM,YAAaiN,EAAI9D,UAAU8kC,IACjDiB,EAAgBjiC,EAAIzE,WAAW,MAAO,CACxCxG,IAAKurC,EACLhwC,KAAM+vC,GAAM/tC,UAAUguC,KAEpB4B,EAAYliC,EAAI/M,IAAI,gBACpB4uC,EAAe7hC,EAAI/M,IAAI,kBAC7B+M,EAAI9C,MAAM,MAAO6F,EAAQC,QAAQV,QAAUQ,GAAQ9C,EAAI5D,GAAGwkC,EAAkB99B,EAAK+9B,GAAU,KACvF7gC,EAAIhE,OAAOkmC,EAAW7B,GAAMtvC,CAAE,GAAGkxC,KAAiBn/B,cAClD9C,EAAIhE,OAAO6lC,EAAcxB,GAAMtvC,CAAE,GAAG8vC,KAAW/9B,cAAgBA,YAAco/B,OAC7EliC,EAAI5D,GAAGylC,EAAc,IAAM7hC,EAAI1P,KAAK+vC,GAAMtvC,CAAE,GAAG8wC,UAAqB/+B,MAAQ9G,OAAOqkC,GAAMtvC,CAAE,GAAG+R,KAAOs9B,KAAQ,OAEjHpgC,EAAI5C,MAAM,MAAOyjC,EAAUnuC,GAAQsN,EAAI5C,MAAM,UAAWijC,GAAMtvC,CAAE,GAAG8vC,KAAWnuC,KAASyvC,IACnFniC,EAAIhE,OAAO6lC,EAAcxB,GAAMtvC,CAAE,GAAG8vC,KAAWnuC,MAAQyvC,MACvDniC,EAAI5D,GAAGikC,GAAMtvC,CAAE,GAAG8wC,WAAuB,KACrC,MAAMO,EAAOpiC,EAAIjN,MAAM,OAAQstC,GAAMtvC,CAAE,GAAG4wC,KAAajvC,SAAWivC,KAAajvC,MAAQyvC,MACvF78B,EAASC,YAAY9B,EAAK,CACtBF,QAAS88B,GAAMtvC,CAAE,GAAGqxC,OAAUA,SAAYn+B,KAAevR,MAAQyvC,KACjE79B,OAAQ+7B,GAAMtvC,CAAE,YAAY8wC,YAlGhCQ,CAAyBjB,GAuGrC,SAA4BkB,GACxB,MAAMliC,MAAEA,EAAKpF,MAAEA,GAAUsnC,EACzB,IAAKliC,IAAUpF,EACX,OACJ,MAAMunC,EAAQlC,GAAMtvC,CAAE,UAAUgR,gBAC1BygC,EAAQnC,GAAMtvC,CAAE,iBAAiBgR,KACjC0gC,EAAYziC,EAAI/M,IAAI,YAC1B,IAAIyvC,EACAC,EACJ,MAAMhB,EAAY3hC,EAAI/M,IAAI,aAgC1B,SAAS2vC,EAAKC,EAAU9B,GACpB/gC,EAAIhE,OAAOymC,EAAWpC,GAAM/tC,UAAUuwC,IACtC7iC,EAAIhE,OAAO2lC,EAAWb,EAAiB+B,EAAU9B,IAjCjD3gC,GAASpF,GACT0nC,EAAW1iC,EAAI/M,IAAI,cACnB+M,EAAI5D,GAAGmmC,GACPviC,EAAI5D,GAAGomC,EAAO,KACVI,EAAK5nC,EAAOiE,EAAOjE,OACnBgF,EAAIhE,OAAO0mC,EAAUrC,GAAMpwC,GAAI,UAChC,KACC2yC,EAAKxiC,EAAOnB,EAAOyK,YACnB1J,EAAIhE,OAAO0mC,EAAUrC,GAAMpwC,GAAI,gBAEnC0yC,EAAYtC,GAAMtvC,CAAE,IAAI2xC,MAEnB1nC,GACLgF,EAAI5D,GAAGomC,GACPI,EAAK5nC,EAAOiE,EAAOjE,OACnB2nC,EAAYtC,GAAMtvC,CAAE,UAEfqP,IACLJ,EAAI5D,GAAG6D,EAAU4I,IAAI05B,EAAOtiC,EAAUrG,IAAI4oC,KAC1CI,EAAKxiC,EAAOnB,EAAOyK,YACnBi5B,EAAYtC,GAAMtvC,CAAE,eAExBiP,EAAI9C,MAAM,MAAO6F,EAAQC,QAAQV,QAAUQ,GAgC/C,SAA6BA,EAAK2/B,EAAWpmC,GACzC2D,EAAI5D,GAAG6D,EAAU4I,IAAIw3B,GAAMtvC,CAAE,GAAG+R,iBAAmBlC,IAAWy/B,GAAMtvC,CAAE,IAAI+R,KAAOs9B,IAAQC,GAAMtvC,CAAE,GAAG+R,0BAA4Bb,YAAwB,KACpJ,MAAM6gC,EAAa9iC,EAAIzE,WAAW,UAAW,CACzCxG,IAAK,qBACLzE,KAAM+vC,GAAMtvC,CAAE,yCAEZgpB,EAAU/Z,EAAIjN,MAAM,YAAastC,GAAMtvC,CAAE,GAAG+xC,UAAmBhgC,wBAA0Bb,cACzF8gC,EAAQ/iC,EAAIjN,MAAM,UAAWstC,GAAMtvC,CAAE,GAAGgpB,QAAcA,gDAC5D/Z,EAAI5D,GAAGikC,GAAMtvC,CAAE,GAAGgyC,sBAA0BA,QAAYN,IAAa,IAAMpmC,EAAS0mC,MAxCrCC,CAAoBlgC,EAAK2/B,EAAYM,GAAU/iC,EAAI1P,KAAK+vC,GAAMtvC,CAAE,GAAG0xC,KAAaM,WAAejgC,MAAQ9G,OAAOqkC,GAAMtvC,CAAE,GAAG+R,KAAOs9B,KAAQ,KAC3LpgC,EAAI5C,MAAM,MAAOqlC,EAAY/vC,GAAQsN,EAAI5D,GAAGikC,GAAMtvC,CAAE,GAAG0xC,KAAa/vC,YAAe,KAC/E4S,EAASC,YAAY9B,EAAK,CACtBF,QAAS88B,GAAMtvC,CAAE,GAAG2B,QAAUivC,OAAeA,KAAajvC,UAAYuR,IAAc0+B,KAAajwC,KACjG4R,OAAQ+7B,GAAMtvC,CAAE,YAAY0xC,KAAa/vC,QAE7CsN,EAAIhE,OAAOqkC,GAAMtvC,CAAE,GAAGgS,EAAQC,QAAQV,WAAWS,EAAQC,QAAQT,yBAA0B89B,GAAMtvC,CAAE,GAAGkR,aAAwBvP,kDAElIsN,EAAIxD,QA9IAymC,CAQR,UAA2Bv5B,WAAEA,EAAU1O,MAAEA,IACrC,MAAMuH,EAAS,GACf,GAAImH,EAAY,CACZnH,EAAOnC,MAAQ,GACf,IAAK,MAAMG,KAAKmJ,EACZnH,EAAOnC,MAAMG,GAAK,GAE1B,GAAIvF,EAAO,CACPuH,EAAOvH,MAAQ,GACf,IAAK,IAAI9J,EAAI,EAAGA,EAAI8J,EAAMxK,OAAQU,IAC9BqR,EAAOvH,MAAM9J,GAAK,GAE1B,OAAOqR,EApBgB2gC,CAAkBv5B,IAEzC,MAAMw5B,EAA2B,iBAAPx5B,EAAkBA,EAAMA,EAAI5Y,EAClDoyC,GAiJR,SAA0BA,GACtB,MAAMjgC,EAAOlD,EAAIjN,MAAM,SAAUstC,GAAMtvC,CAAE,MACzCiP,EAAI9C,MAAM,MAAO6F,EAAQC,QAAQV,QAAUQ,GAAQ9C,EAAI5D,GA2B3D,SAAuB0G,GACnB,OAAO7C,EAAU4I,IAAIw3B,GAAMtvC,CAAE,GAAG+R,iBAAmBlC,IAAWy/B,GAAMtvC,CAAE,IAAI+R,KAAOs9B,IAAQngC,EAAUiK,GAAGm2B,GAAMtvC,CAAE,GAAG+R,sBAAwBb,IAAgBhC,EAAU4I,IAAIw3B,GAAMtvC,CAAE,GAAG+R,0BAA4Bb,WAAuBo+B,GAAMtvC,CAAE,GAAG+R,kBAAoBb,sBAAkCo+B,GAAMtvC,CAAE,GAAG+R,wBAA0B9D,EAAGmF,uBAAwBk8B,GAAMtvC,CAAE,GAAG+R,gBAAkB9D,EAAGmF,iCA5B5Ui/B,CAActgC,GAAM,IAAM9C,EAAI1P,KAAK+vC,GAAMtvC,CAAE,GAAGmS,UAAaJ,MAAQ9G,OAAOqkC,GAAMtvC,CAAE,GAAG+R,KAAOs9B,KAAQ,KAC9JpgC,EAAI5D,GAAGikC,GAAMtvC,CAAE,GAAGmS,WAAe,IAAMoC,EAASC,YAAY9B,EAAK,CAC7DF,QAAS29B,EAAaiC,GACtB7+B,OAAQ+7B,GAAMtvC,CAAE,YAAYmS,QArJ5BmgC,CAAiBF,GAChB7a,EAAQgb,YAuJjB,WACI,MAAMpgC,EAAOlD,EAAIjN,MAAM,SAAUstC,GAAMtvC,CAAE,MACzCiP,EAAI9C,MAAM,MAAO6F,EAAQC,QAAQV,QAAUQ,GAAQ9C,EAAI5D,GAAGikC,GAAMtvC,CAAE,IAAI+R,KAAOs9B,IAAQ,IAAMpgC,EAAI1P,KAAK+vC,GAAMtvC,CAAE,GAAGmS,UAAaJ,QAC5H9C,EAAIhE,OAAO+G,EAAQC,QAAQV,QAASY,GAAMlH,OAAO+G,EAAQC,QAAQT,OAAQ89B,GAAMtvC,CAAE,GAAGmS,YAzJhFqgC,MAkMZrP,WAAY,CACR/jB,MAAO,CACH,CAAEtK,KAAM,UACR,CACIA,KAAM,SACN6D,WAAY,CACRA,WAAY,CAAEgL,KAAM,qBACpB1Z,MAAO,CAAE0Z,KAAM,sBACfjE,SAAU,CAAEiE,KAAM,uBAClB5G,aAAc,CAAE4G,KAAM,wBAE1B3E,qBAAsB,CAAElK,KAAM,YAGtCwK,MAAO,CACHmzB,UAAW,CACP39B,KAAM,SACNkK,qBAAsB,CAAElK,KAAM,WAElC49B,YAAa,CACTtzB,MAAO,CAAC,CAAEtK,KAAM,UAAY,CAAE6O,KAAM,uBAExCgvB,WAAY,CAAE79B,KAAM,QAAS7K,MAAO,CAAE6K,KAAM,cAK5D,MAAMqoB,EAAY,CAAC5T,EAAKgO,EAAU,MAC9B,IAAKhO,EAAI/lB,KAAKuP,UACV,MAAM,IAAI/T,MAAM,iDACpB,GAAIuqB,EAAI/lB,KAAKqN,iBACT,MAAM,IAAI7R,MAAM,4DAEpB,OAAOuqB,EAAI+W,WAAWsP,EAAarY,KAEvC/4B,UAAkB2+B,EAClBlf,UAAiBkf,EACjBlf,kBAAyBkf,KC3QnByV,GAAmB,SACvBzV,EACA0V,GASA,OANA1V,EAAUl4B,QAAQ,SAAC2C,GACK,aAAlBA,EAAMiI,UACRjI,EAAMsJ,cAAgB,IAAMtJ,EAAM2L,OAAOgG,mBAItC4jB,EAAUt9B,OAAmC,SAACizC,EAAUlrC,GAE7D,IAAM6tB,EAAO7tB,EAAMsJ,aAAa6hC,UAAU,GAAGvxC,QAAQ,MAAO,KAS5D,GAPKsxC,EAASrd,KACZqd,EAASrd,GAAQ,CACfjjB,QAAS5K,EAAM4K,QACfsC,KAAMlN,EAAMiI,UAIZgjC,EAA0B,CAC5B,IAAM59B,EAAQ69B,EAASrd,GAAMxgB,MACvBpB,EAAWoB,GAASA,EAAMrN,EAAMiI,SAEtCijC,EAASrd,GAAQud,EACfvd,EACAod,EACAC,EACAlrC,EAAMiI,QACNgE,EACK,GAAgBo/B,OAAOp/B,EAAsBjM,EAAM4K,SAAW,IAC/D5K,EAAM4K,SAId,OAAOsgC,GACN,KAGQI,GACX,SAAChlC,EAAQilC,EAAeC,mBAAAA,IAAAA,EAAkB,aACnC1uC,EAAQ1E,EAAGu3B,aACVhO,EAAM,IAAI4V,MACdpsB,WAAW,EACXyI,gBAAgB,GACb23B,IAGLhW,GAAU5T,GAEV,IAAM5N,EAAW4N,EAAI3N,QACnBvd,OAAO4M,OAAO,CAAEqH,OAAkC,oBAA1B8gC,YAAiB5iC,OAAoBtC,IAI/D,OAFcyN,EAASjX,IAgBvB6yB,EAAQ8b,2BAA6BC,EAAuB,GAAI/b,mBAEzD,CACL7yB,OAAAA,EACA8M,OAAQ,sBAjBD,CACL9M,OAAQ,GACR8M,OAAQ+hC,EACNX,GACEj3B,EAASnK,QACR+lB,EAAQ8b,2BACkB,QAAzB9b,EAAQic,cAEZjc,KAxBR"}