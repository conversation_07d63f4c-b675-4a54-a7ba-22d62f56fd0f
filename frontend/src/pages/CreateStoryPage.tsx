import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, Save, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { DashboardLayout } from '@/components/layout/Layout';
import { StoryCreator } from '@/components/features/StoryCreator';
import { GenerationProgress } from '@/components/features/story-creator/GenerationProgress';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { useStoryStore } from '@/stores/storyStore';
import { useAuthStore } from '@/stores/authStore';
import { useNotifications } from '@/stores/uiStore';
import { shouldSkipAuth } from '@/utils/debug';
import { StoryGenerationClient } from '@/services/durableObjects/storyGenerationClient';
import type { CreateStoryRequest } from '@/types/story';

// Error Boundary Component
class CreateStoryErrorBoundary extends React.Component<
  { children: React.ReactNode; onRetry: () => void },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: React.ReactNode; onRetry: () => void }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Error logged for debugging in development
  }

  render() {
    if (this.state.hasError) {
      return (
        <DashboardLayout>
          <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex items-center justify-center">
            <Card className="p-8 max-w-md mx-auto text-center">
              <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                创作页面出现错误
              </h2>
              <p className="text-gray-600 mb-4">
                {this.state.error?.message || '页面加载失败，请重试'}
              </p>
              <div className="space-y-2">
                <Button
                  onClick={() => {
                    this.setState({ hasError: false, error: null });
                    this.props.onRetry();
                  }}
                  className="w-full"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  重试
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.location.href = '/my-stories'}
                  className="w-full"
                >
                  我的故事
                </Button>
              </div>
            </Card>
          </div>
        </DashboardLayout>
      );
    }

    return this.props.children;
  }
}

const CreateStoryPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isCreating, setIsCreating] = useState(false);
  const [creationError, setCreationError] = useState<string | null>(null);

  const {
    createStory,
    generationProgress,
    isGenerating,
    draftStory,
    saveDraft,
    clearDraft,
    loadDraft
  } = useStoryStore();

  const { user } = useAuthStore();
  const { showSuccess, showError, showWarning, showInfo } = useNotifications();

  // 使用ref来防止重复显示草稿通知
  const draftNotificationShown = useRef(false);

  // WebSocket客户端ref用于实时进度监听
  const wsClientRef = useRef<StoryGenerationClient | null>(null);

  // Load draft on component mount
  useEffect(() => {
    try {
      const draft = loadDraft?.();
      if (draft && showWarning && !draftNotificationShown.current) {
        showWarning('发现草稿', '已恢复您之前的创作进度');
        draftNotificationShown.current = true;
      }
    } catch (error) {
      // Silently handle draft loading errors
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loadDraft]); // 移除showWarning依赖以避免无限循环

  // Cleanup WebSocket connection on unmount
  useEffect(() => {
    return () => {
      if (wsClientRef.current) {
        wsClientRef.current.disconnect();
        wsClientRef.current = null;
      }
    };
  }, []);




  // Start progress monitoring for story generation
  const startProgressMonitoring = (storyId: string) => {
    console.log('🚀 startProgressMonitoring called for story:', storyId);
    try {
      // Clean up existing connection
      if (wsClientRef.current) {
        console.log('🧹 Cleaning up existing WebSocket connection');
        wsClientRef.current.disconnect();
        wsClientRef.current = null;
      }

      // Create new WebSocket client
      console.log('📡 Creating new StoryGenerationClient for story:', storyId);
      wsClientRef.current = new StoryGenerationClient(storyId);
      console.log('✅ StoryGenerationClient created successfully');

      // Monitor progress updates
      console.log('📊 Setting up onTaskProgress listener');
      wsClientRef.current.onTaskProgress((data) => {
        console.log('📈 Task progress received:', data);
        // Update progress in store
        if (data.progress !== undefined) {
          const { updateGenerationProgress } = useStoryStore.getState();
          // Map taskId to stage type
          const getStageFromTaskId = (taskId: string) => {
            if (taskId.includes('text')) return 'text';
            if (taskId.includes('image')) return 'images';
            if (taskId.includes('audio')) return 'audio';
            return 'text';
          };

          const stage = getStageFromTaskId(data.taskId);
          console.log('🎯 Updating generation progress:', { storyId, stage, progress: data.progress });
          updateGenerationProgress({
            storyId,
            stage: stage as 'text' | 'images' | 'audio' | 'completed',
            progress: data.progress,
            currentStep: stage,
          });
        }
      });

      // Monitor story completion
      console.log('🎉 Setting up onStoryCompleted listener');
      wsClientRef.current.onStoryCompleted((data) => {
        console.log('✅ Story generation completed:', data);
        // Update progress to completed
        const { updateGenerationProgress } = useStoryStore.getState();
        updateGenerationProgress({
          storyId,
          stage: 'completed',
          progress: 100,
          currentStep: 'completed',
        });

        // 延迟跳转到故事详情页面
        console.log('🔄 Scheduling navigation to story details page in 2 seconds');
        setTimeout(() => {
          navigate(`/stories/${storyId}`);
        }, 2000);
      });

      // Handle errors
      console.log('❌ Setting up onTaskUpdate error listener');
      wsClientRef.current.onTaskUpdate((data) => {
        console.log('📝 Task update received:', data);
        if (data.task?.status === 'failed') {
          console.error('💥 Task failed:', data.task);
          const { updateGenerationProgress } = useStoryStore.getState();
          updateGenerationProgress({
            storyId,
            stage: 'text',
            progress: 0,
            currentStep: 'failed',
            error: data.task.error || '生成失败',
          });
        }
      });

      // Connect to WebSocket
      console.log('🔌 Attempting to connect WebSocket...');
      wsClientRef.current.connect().then(() => {
        console.log('✅ WebSocket connection successful');
      }).catch((error) => {
        console.error('❌ WebSocket connection failed:', error);
        // Could implement polling fallback here if needed
      });

    } catch (error) {
      console.error('❌ Error in startProgressMonitoring:', error);
      // Silently handle connection errors
    }
  };

  // Handle story creation
  const handleCreateStory = async (storyData: CreateStoryRequest) => {
    try {
      // Skip auth check in debug mode
      if (!user && !shouldSkipAuth()) {
        if (showError) {
          showError('请先登录', '您需要登录才能创作故事');
        }
        navigate('/auth');
        return;
      }

      // In debug mode, use debug user if no user is set
      const currentUser = user || (shouldSkipAuth() ? { credits: 1000 } : null);
      if (!currentUser) {
        if (showError) {
          showError('用户状态异常', '请重新登录');
        }
        navigate('/auth');
        return;
      }

      // Check if user has enough credits
      const estimatedCredits = calculateEstimatedCredits(storyData);
      if (currentUser.credits < estimatedCredits) {
        if (showError) {
          showError('积分不足', `创作此故事需要 ${estimatedCredits} 积分，您当前有 ${currentUser.credits} 积分`);
        }
        navigate('/pricing');
        return;
      }

      // Skip auth check in debug mode if needed

      setIsCreating(true);
      setCreationError(null);

      if (!createStory) {
        throw new Error(t('create.error.serviceUnavailable', '创作服务不可用'));
      }

      const story = await createStory(storyData);

      // Clear draft after successful creation
      if (clearDraft) {
        clearDraft();
      }

      if (showSuccess) {
        showSuccess(t('create.success.title', '故事创作开始'), t('create.success.generating', '您的故事正在生成中，请稍候...'));
      }

      // Start real-time progress monitoring
      if (story?.id) {
        console.log('🎯 Story created successfully, starting progress monitoring for ID:', story.id);
        startProgressMonitoring(story.id);
      } else {
        console.error('❌ Story creation succeeded but no story ID returned:', story);
      }

      // Stay on the progress page until generation is complete
      // Navigation will be handled by GenerationProgress component

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : t('create.error.message', '创作失败，请重试');
      setCreationError(errorMessage);
      if (showError) {
        showError(t('create.error.title', '创作失败'), errorMessage);
      }
      // Error handled by UI notification
    } finally {
      setIsCreating(false);
    }
  };

  // Handle draft saving
  const handleSaveDraft = (storyData: Partial<CreateStoryRequest>) => {
    try {
      if (saveDraft) {
        saveDraft(storyData);
      }
      if (showSuccess) {
        showSuccess('草稿已保存', '您可以稍后继续创作');
      }
    } catch (error) {
      if (showError) {
        showError('保存失败', '草稿保存失败，请重试');
      }
    }
  };

  // Calculate estimated credits needed
  const calculateEstimatedCredits = (storyData: CreateStoryRequest): number => {
    let credits = 30; // Base cost

    // Add cost based on story length/complexity
    if (storyData.characterAge && storyData.characterAge > 8) {
      credits += 10; // More complex stories for older children
    }

    // Add cost for premium features
    if (storyData.style === 'fantasy' || storyData.style === 'adventure') {
      credits += 5; // More detailed illustrations
    }

    return credits;
  };

  // Handle navigation back
  const handleGoBack = () => {
    if (draftStory) {
      if (window.confirm(t('create.confirmLeave', '您有未保存的草稿，确定要离开吗？'))) {
        navigate('/my-stories');
      }
    } else {
      navigate('/my-stories');
    }
  };

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50"
      >
        {/* Header */}
        <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  onClick={handleGoBack}
                  className="p-2"
                >
                  <ArrowLeft className="w-5 h-5" />
                </Button>
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">
                    {t('create.newStory', '创作新故事')}
                  </h1>
                  <p className="text-sm text-gray-500">
                    {t('create.stepProgress', { current: currentStep, total: 4 })}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">

                {user && (
                  <div className="text-sm text-gray-600">
                    {t('create.currentCredits', '当前积分')}: <span className="font-semibold text-primary-600">{user.credits}</span>
                  </div>
                )}

                {draftStory && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSaveDraft(draftStory)}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {t('common.save')}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="py-8">
          <AnimatePresence mode="wait">
            {isGenerating && generationProgress ? (
              <motion.div
                key="generation"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.3 }}
                className="max-w-4xl mx-auto px-4"
              >
                <GenerationProgress
                  progress={generationProgress}
                />
              </motion.div>
            ) : (
              <motion.div
                key="creator"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="max-w-6xl mx-auto px-4"
              >
                <StoryCreator
                  className="w-full"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Error Display */}
        {creationError && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            className="fixed bottom-4 right-4 max-w-md"
          >
            <Card className="p-4 bg-red-50 border-red-200">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-red-900">创作失败</h4>
                  <p className="text-sm text-red-700 mt-1">{creationError}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCreationError(null)}
                    className="mt-2 text-red-700 border-red-300 hover:bg-red-100"
                  >
                    关闭
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Success Indicator */}
        {isGenerating && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="fixed top-4 right-4"
          >
            <Card className="p-3 bg-green-50 border-green-200">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span className="text-sm font-medium text-green-900">
                  故事生成中...
                </span>
              </div>
            </Card>
          </motion.div>
        )}
      </motion.div>
    </DashboardLayout>
  );
};

// Wrapped component with error boundary
const CreateStoryPageWithErrorBoundary: React.FC = () => {
  const [retryKey, setRetryKey] = useState(0);

  const handleRetry = () => {
    setRetryKey(prev => prev + 1);
  };

  return (
    <CreateStoryErrorBoundary onRetry={handleRetry}>
      <CreateStoryPage key={retryKey} />
    </CreateStoryErrorBoundary>
  );
};

export default CreateStoryPageWithErrorBoundary;
