import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { Clock, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { StoryGenerationClient } from '@/services/durableObjects/storyGenerationClient';
import { useStoryStore } from '@/stores/storyStore';

interface StoryGenerationProgressProps {
  storyId: string;
  storyTitle?: string;
  characterName?: string;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

interface ProgressStage {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  progress: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
}

export const StoryGenerationProgress: React.FC<StoryGenerationProgressProps> = ({
  storyId,
  storyTitle,
  characterName,
  onComplete,
  onError
}) => {
  const wsClientRef = useRef<StoryGenerationClient | null>(null);
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [stages, setStages] = useState<ProgressStage[]>([
    {
      id: 'text',
      name: '创作故事文本',
      description: 'AI正在根据您的设定创作精彩的故事内容...',
      icon: <Loader2 className="w-5 h-5" />,
      progress: 0,
      status: 'pending'
    },
    {
      id: 'image',
      name: '绘制精美插图',
      description: 'AI正在为故事绘制生动的插图...',
      icon: <Loader2 className="w-5 h-5" />,
      progress: 0,
      status: 'pending'
    },
    {
      id: 'audio',
      name: '合成语音朗读',
      description: 'AI正在生成温暖的语音朗读...',
      icon: <Loader2 className="w-5 h-5" />,
      progress: 0,
      status: 'pending'
    }
  ]);

  const [overallProgress, setOverallProgress] = useState(0);
  const [currentStage, setCurrentStage] = useState('text');
  const [isConnected, setIsConnected] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime] = useState(Date.now());

  // 计算总体进度
  useEffect(() => {
    const totalProgress = stages.reduce((sum, stage) => sum + stage.progress, 0) / stages.length;
    setOverallProgress(Math.round(totalProgress));
  }, [stages]);

  // 更新经过时间
  useEffect(() => {
    const timer = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, [startTime]);

  // 触发故事生成
  const triggerStoryGeneration = async () => {
    console.log('🎯 triggerStoryGeneration called for story:', storyId);
    if (!wsClientRef.current) {
      console.error('❌ wsClientRef.current is null, cannot trigger generation');
      return;
    }
    console.log('✅ wsClientRef.current exists, proceeding with generation');

    try {
      // 从故事数据中获取生成参数
      console.log('📚 Getting story data from store...');
      const { getStoryById } = useStoryStore.getState();
      const story = await getStoryById(storyId);

      if (!story) {
        console.error('❌ Story not found in store for ID:', storyId);
        throw new Error('Story not found');
      }
      console.log('✅ Story data retrieved successfully');

      console.log('Triggering story generation with params:', {
        characterName: story.characterName,
        age: story.characterAge,
        traits: story.characterTraits,
        theme: story.theme,
        setting: story.setting,
        style: story.style,
        voice: story.voice
      });

      // 调用startGeneration方法
      console.log('🚀 Calling wsClientRef.current.startGeneration...');
      await wsClientRef.current.startGeneration({
        characterName: story.characterName || characterName || '小朋友',
        age: story.characterAge,
        traits: story.characterTraits || [],
        theme: story.theme || '冒险探索',
        setting: story.setting || '神秘森林',
        style: story.style || '冒险探索',
        voice: story.voice || '温柔女声'
      });

      console.log('✅ Story generation triggered successfully');
    } catch (error) {
      console.error('Failed to trigger story generation:', error);
      if (onError) {
        onError(`启动生成失败: ${error.message}`);
      }
    }
  };

  // 设置WebSocket连接和轮询
  useEffect(() => {
    console.log('🚀 StoryGenerationProgress useEffect triggered for storyId:', storyId);
    let mounted = true;

    const setupConnection = async () => {
      try {
        console.log('📡 Attempting to create WebSocket connection for story:', storyId);
        // 尝试建立WebSocket连接
        wsClientRef.current = new StoryGenerationClient(storyId);
        console.log('✅ StoryGenerationClient created successfully');

        // 监听连接事件
        wsClientRef.current.onConnected(() => {
          if (mounted) {
            console.log('🔗 WebSocket connected for progress monitoring');
            setIsConnected(true);
          }
        });

        // 监听任务进度
        wsClientRef.current.onTaskProgress((data) => {
          if (!mounted) return;
          
          console.log('Task progress received:', data);
          updateStageProgress(data.taskId, data.progress);
        });

        // 监听故事完成
        wsClientRef.current.onStoryCompleted(() => {
          if (!mounted) return;
          
          console.log('Story generation completed');
          setStages(prev => prev.map(stage => ({
            ...stage,
            progress: 100,
            status: 'completed',
            icon: <CheckCircle className="w-5 h-5 text-green-500" />
          })));
          
          setTimeout(() => {
            if (mounted && onComplete) {
              onComplete();
            }
          }, 1000);
        });

        // 监听错误
        wsClientRef.current.onTaskUpdate((data) => {
          if (!mounted) return;

          if (data.task?.status === 'failed') {
            console.error('Task failed:', data.task);
            const stageId = getStageIdFromTaskId(data.task.id);
            updateStageStatus(stageId, 'failed');

            if (onError) {
              onError(data.task.error || '生成失败');
            }
          }
        });

        // 连接WebSocket
        console.log('🔌 Attempting WebSocket connection...');
        await wsClientRef.current.connect();
        console.log('✅ WebSocket connection established successfully');

        // 🔥 关键修复：连接成功后立即触发故事生成
        console.log('🎯 WebSocket connected, starting story generation...');
        await triggerStoryGeneration();
        console.log('✅ Story generation triggered successfully');

      } catch (error) {
        console.error('❌ WebSocket connection failed, using polling fallback:', error);
        if (mounted) {
          setIsConnected(false);
          console.log('📊 Starting polling fallback...');
          startPolling();
        }
      }
    };

    console.log('🔄 Calling setupConnection...');
    setupConnection();

    return () => {
      console.log('🧹 StoryGenerationProgress cleanup for story:', storyId);
      mounted = false;

      // 清理WebSocket连接
      if (wsClientRef.current) {
        wsClientRef.current.disconnect();
        wsClientRef.current = null;
      }

      // 清理轮询
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }
    };
  }, [storyId, onComplete, onError]);

  // 轮询机制作为WebSocket的后备方案
  const startPolling = () => {
    if (pollIntervalRef.current) return;

    pollIntervalRef.current = setInterval(async () => {
      try {
        const { getStoryById } = useStoryStore.getState();
        const story = await getStoryById(storyId);
        
        if (story) {
          if (story.status === 'completed') {
            // 故事生成完成
            setStages(prev => prev.map(stage => ({
              ...stage,
              progress: 100,
              status: 'completed',
              icon: <CheckCircle className="w-5 h-5 text-green-500" />
            })));
            
            if (pollIntervalRef.current) {
              clearInterval(pollIntervalRef.current);
              pollIntervalRef.current = null;
            }
            
            setTimeout(() => {
              if (onComplete) {
                onComplete();
              }
            }, 1000);
          } else if (story.status === 'failed') {
            // 故事生成失败
            setStages(prev => prev.map(stage => ({
              ...stage,
              status: 'failed',
              icon: <AlertCircle className="w-5 h-5 text-red-500" />
            })));
            
            if (pollIntervalRef.current) {
              clearInterval(pollIntervalRef.current);
              pollIntervalRef.current = null;
            }
            
            if (onError) {
              onError('故事生成失败');
            }
          }
        }
      } catch (error) {
        console.error('Polling failed:', error);
      }
    }, 3000); // 每3秒轮询一次
  };

  // 从taskId提取stage ID
  const getStageIdFromTaskId = (taskId: string): string => {
    if (taskId.includes('text')) return 'text';
    if (taskId.includes('image')) return 'image';
    if (taskId.includes('audio')) return 'audio';
    return 'text';
  };

  // 更新阶段进度
  const updateStageProgress = (taskId: string, progress: number) => {
    const stageId = getStageIdFromTaskId(taskId);
    
    setStages(prev => prev.map(stage => {
      if (stage.id === stageId) {
        const status = progress === 100 ? 'completed' : progress > 0 ? 'running' : 'pending';
        const icon = status === 'completed' 
          ? <CheckCircle className="w-5 h-5 text-green-500" />
          : status === 'running'
          ? <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
          : <Clock className="w-5 h-5 text-gray-400" />;
          
        return {
          ...stage,
          progress,
          status,
          icon
        };
      }
      return stage;
    }));

    // 更新当前阶段
    if (progress > 0) {
      setCurrentStage(stageId);
    }
  };

  // 更新阶段状态
  const updateStageStatus = (stageId: string, status: 'pending' | 'running' | 'completed' | 'failed') => {
    setStages(prev => prev.map(stage => {
      if (stage.id === stageId) {
        const icon = status === 'failed'
          ? <AlertCircle className="w-5 h-5 text-red-500" />
          : stage.icon;
          
        return {
          ...stage,
          status,
          icon
        };
      }
      return stage;
    }));
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      {/* 总体进度 */}
      <Card className="p-6">
        <div className="text-center mb-6">
          <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
            <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            正在为您创作专属故事
          </h2>
          <p className="text-gray-600">
            《{storyTitle || (characterName ? `${characterName}的故事` : '专属故事')}》
          </p>
        </div>

        {/* 总进度条 */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">总体进度</span>
            <span className="text-sm font-medium text-gray-700">{overallProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <motion.div
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${overallProgress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>

        {/* 连接状态和时间 */}
        <div className="flex items-center justify-center space-x-6 mb-4">
          <div className={`flex items-center space-x-2 text-sm ${isConnected ? 'text-green-600' : 'text-yellow-600'}`}>
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-yellow-500'}`} />
            <span>{isConnected ? '实时连接已建立' : '使用轮询模式更新'}</span>
          </div>
          <div className="flex items-center space-x-1 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>已用时 {Math.floor(elapsedTime / 60)}:{(elapsedTime % 60).toString().padStart(2, '0')}</span>
          </div>
        </div>
      </Card>

      {/* 详细进度 */}
      <div className="space-y-4">
        {stages.map((stage, index) => (
          <motion.div
            key={stage.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className={`p-4 ${stage.status === 'running' ? 'ring-2 ring-blue-500' : ''}`}>
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {stage.icon}
                </div>
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium text-gray-900">{stage.name}</h3>
                    <span className="text-sm text-gray-500">{stage.progress}%</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{stage.description}</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      className={`h-2 rounded-full ${
                        stage.status === 'completed' ? 'bg-green-500' :
                        stage.status === 'failed' ? 'bg-red-500' :
                        stage.status === 'running' ? 'bg-blue-500' : 'bg-gray-300'
                      }`}
                      initial={{ width: 0 }}
                      animate={{ width: `${stage.progress}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};
