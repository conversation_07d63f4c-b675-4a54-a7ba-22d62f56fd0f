import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { Clock, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { StoryGenerationClient } from '@/services/durableObjects/storyGenerationClient';
import { useStoryStore } from '@/stores/storyStore';
import { storyService } from '@/services/stories';

interface StoryGenerationProgressProps {
  storyId: string;
  storyTitle?: string;
  characterName?: string;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

interface ProgressStage {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  progress: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
}

export const StoryGenerationProgress: React.FC<StoryGenerationProgressProps> = ({
  storyId,
  storyTitle,
  characterName,
  onComplete,
  onError
}) => {
  // 保留WebSocket引用但不使用 - 为将来的实时功能保留
  const wsClientRef = useRef<StoryGenerationClient | null>(null);
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [stages, setStages] = useState<ProgressStage[]>([
    {
      id: 'text',
      name: '创作故事文本',
      description: 'AI正在根据您的设定创作精彩的故事内容...',
      icon: <Loader2 className="w-5 h-5" />,
      progress: 0,
      status: 'pending'
    },
    {
      id: 'image',
      name: '绘制精美插图',
      description: 'AI正在为故事绘制生动的插图...',
      icon: <Loader2 className="w-5 h-5" />,
      progress: 0,
      status: 'pending'
    },
    {
      id: 'audio',
      name: '合成语音朗读',
      description: 'AI正在生成温暖的语音朗读...',
      icon: <Loader2 className="w-5 h-5" />,
      progress: 0,
      status: 'pending'
    }
  ]);

  const [overallProgress, setOverallProgress] = useState(0);
  const [currentStage, setCurrentStage] = useState('text');
  const [isConnected, setIsConnected] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime] = useState(Date.now());

  // 计算总体进度
  useEffect(() => {
    const totalProgress = stages.reduce((sum, stage) => sum + stage.progress, 0) / stages.length;
    setOverallProgress(Math.round(totalProgress));
  }, [stages]);

  // 更新经过时间
  useEffect(() => {
    const timer = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, [startTime]);

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 触发故事生成 - 保留为将来WebSocket功能使用，现在使用HTTP轮询
  // const triggerStoryGeneration = async () => {
  //   console.log('🎯 triggerStoryGeneration called for story:', storyId);
  //   if (!wsClientRef.current) {
  //     console.error('❌ wsClientRef.current is null, cannot trigger generation');
  //     return;
  //   }
  //   console.log('✅ wsClientRef.current exists, proceeding with generation');
  //
  //   try {
  //     // 从故事数据中获取生成参数
  //     console.log('📚 Getting story data from store...');
  //     const { getStoryById } = useStoryStore.getState();
  //     const story = await getStoryById(storyId);
  //
  //     if (!story) {
  //       console.error('❌ Story not found in store for ID:', storyId);
  //       throw new Error('Story not found');
  //     }
  //     console.log('✅ Story data retrieved successfully');
  //
  //     console.log('Triggering story generation with params:', {
  //       characterName: story.characterName,
  //       age: story.characterAge,
  //       traits: story.characterTraits,
  //       theme: story.theme,
  //       setting: story.setting,
  //       style: story.style,
  //       voice: story.voice
  //     });
  //
  //     // 调用startGeneration方法
  //     console.log('🚀 Calling wsClientRef.current.startGeneration...');
  //     await wsClientRef.current.startGeneration({
  //       characterName: story.characterName || characterName || '小朋友',
  //       age: story.characterAge,
  //       traits: story.characterTraits || [],
  //       theme: story.theme || '冒险探索',
  //       setting: story.setting || '神秘森林',
  //       style: story.style || '冒险探索',
  //       voice: story.voice || '温柔女声'
  //     });
  //
  //     console.log('✅ Story generation triggered successfully');
  //   } catch (error) {
  //     console.error('Failed to trigger story generation:', error);
  //     if (onError) {
  //       onError(`启动生成失败: ${error.message}`);
  //     }
  //   }
  // };

  // 设置HTTP轮询进度监控 - 禁用WebSocket
  useEffect(() => {
    console.log('🚀 StoryGenerationProgress useEffect triggered for storyId:', storyId);
    let mounted = true;

    const setupPolling = async () => {
      console.log('📊 Using HTTP polling mode for story progress');
      setIsConnected(true);

      // 启动HTTP轮询
      startPolling();
    };

    console.log('🔄 Starting HTTP polling setup...');
    setupPolling();

    return () => {
      console.log('🧹 StoryGenerationProgress cleanup for story:', storyId);
      mounted = false;

      // 清理轮询
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }

      // 保留WebSocket引用清理 - 为将来功能保留
      if (wsClientRef.current) {
        wsClientRef.current = null;
      }
    };
  }, [storyId, onComplete, onError]);

  // HTTP轮询机制 - 主要的进度更新方式
  const startPolling = () => {
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
    }

    console.log('🔄 Starting HTTP polling for story:', storyId);

    const poll = async () => {
      try {
        console.log('📡 Polling story status...');

        // 调用故事状态API
        const { getStoryById } = useStoryStore.getState();
        const story = await getStoryById(storyId);

        if (!story) {
          console.error('❌ Story not found:', storyId);
          onError?.('故事不存在');
          return;
        }

        console.log('📊 Story status:', {
          id: story.id,
          status: story.status,
          pagesCount: story.pages?.length || 0,
          hasAudio: !!story.audioUrl,
          hasCover: !!story.coverImageUrl
        });

        // 根据故事状态更新进度
        updateProgressFromStory(story);

        // 如果故事完成，停止轮询
        if (story.status === 'completed') {
          console.log('✅ Story generation completed');
          if (pollIntervalRef.current) {
            clearInterval(pollIntervalRef.current);
            pollIntervalRef.current = null;
          }
          setTimeout(() => {
            onComplete?.();
          }, 1000);
        } else if (story.status === 'failed') {
          console.log('❌ Story generation failed');
          if (pollIntervalRef.current) {
            clearInterval(pollIntervalRef.current);
            pollIntervalRef.current = null;
          }
          onError?.('故事生成失败');
        }

      } catch (error) {
        console.error('❌ Polling error:', error);
        // 继续轮询，不中断
      }
    };

    // 立即执行一次
    poll();

    // 每8秒轮询一次
    pollIntervalRef.current = setInterval(poll, 8000);
  };

  // 根据故事数据更新进度
  const updateProgressFromStory = (story: any) => {
    const hasText = story.pages && story.pages.length > 0;
    const hasImages = story.pages && story.pages.some((p: any) => p.imageUrl);
    const hasAudio = !!story.audioUrl;

    setStages(prev => [
      {
        ...prev[0],
        progress: hasText ? 100 : (story.status === 'generating' ? 50 : 0),
        status: hasText ? 'completed' : (story.status === 'generating' ? 'running' : 'pending'),
        icon: hasText ? <CheckCircle className="w-5 h-5 text-green-500" /> : <Loader2 className="w-5 h-5" />
      },
      {
        ...prev[1],
        progress: hasImages ? 100 : (hasText ? 50 : 0),
        status: hasImages ? 'completed' : (hasText ? 'running' : 'pending'),
        icon: hasImages ? <CheckCircle className="w-5 h-5 text-green-500" /> : <Loader2 className="w-5 h-5" />
      },
      {
        ...prev[2],
        progress: hasAudio ? 100 : (hasImages ? 50 : 0),
        status: hasAudio ? 'completed' : (hasImages ? 'running' : 'pending'),
        icon: hasAudio ? <CheckCircle className="w-5 h-5 text-green-500" /> : <Loader2 className="w-5 h-5" />
      }
    ]);
  };

  // 从taskId提取stage ID - 保留为将来WebSocket功能使用
  const getStageIdFromTaskId = (taskId: string): string => {
    if (taskId.includes('text')) return 'text';
    if (taskId.includes('image')) return 'image';
    if (taskId.includes('audio')) return 'audio';
    return 'text';
  };

  // 更新阶段进度 - 保留为将来WebSocket功能使用
  // const updateStageProgress = (taskId: string, progress: number) => {
  //   const stageId = getStageIdFromTaskId(taskId);
  //
  //   setStages(prev => prev.map(stage => {
  //     if (stage.id === stageId) {
  //       const status = progress === 100 ? 'completed' : progress > 0 ? 'running' : 'pending';
  //       const icon = status === 'completed'
  //         ? <CheckCircle className="w-5 h-5 text-green-500" />
  //         : status === 'running'
  //         ? <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
  //         : <Clock className="w-5 h-5 text-gray-400" />;
  //
  //       return {
  //         ...stage,
  //         progress,
  //         status,
  //         icon
  //       };
  //     }
  //     return stage;
  //   }));
  //
  //   // 更新当前阶段
  //   if (progress > 0) {
  //     setCurrentStage(stageId);
  //   }
  // };

  // 更新阶段状态 - 保留为将来WebSocket功能使用
  // const updateStageStatus = (stageId: string, status: 'pending' | 'running' | 'completed' | 'failed') => {
  //   setStages(prev => prev.map(stage => {
  //     if (stage.id === stageId) {
  //       const icon = status === 'failed'
  //         ? <AlertCircle className="w-5 h-5 text-red-500" />
  //         : stage.icon;
  //
  //       return {
  //         ...stage,
  //         status,
  //         icon
  //       };
  //     }
  //     return stage;
  //   }));
  // };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      {/* 总体进度 */}
      <Card className="p-6">
        <div className="text-center mb-6">
          <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
            <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            正在为您创作专属故事
          </h2>
          <p className="text-gray-600">
            《{storyTitle || (characterName ? `${characterName}的故事` : '专属故事')}》
          </p>
        </div>

        {/* 总进度条 */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">总体进度</span>
            <span className="text-sm font-medium text-gray-700">{overallProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <motion.div
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${overallProgress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>

        {/* 连接状态和时间 */}
        <div className="flex items-center justify-center space-x-6 mb-4">
          <div className={`flex items-center space-x-2 text-sm ${isConnected ? 'text-green-600' : 'text-yellow-600'}`}>
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-yellow-500'}`} />
            <span>{isConnected ? '实时连接已建立' : '使用轮询模式更新'}</span>
          </div>
          <div className="flex items-center space-x-1 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>已用时 {Math.floor(elapsedTime / 60)}:{(elapsedTime % 60).toString().padStart(2, '0')}</span>
          </div>
        </div>
      </Card>

      {/* 详细进度 */}
      <div className="space-y-4">
        {stages.map((stage, index) => (
          <motion.div
            key={stage.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className={`p-4 ${stage.status === 'running' ? 'ring-2 ring-blue-500' : ''}`}>
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {stage.icon}
                </div>
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium text-gray-900">{stage.name}</h3>
                    <span className="text-sm text-gray-500">{stage.progress}%</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{stage.description}</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      className={`h-2 rounded-full ${
                        stage.status === 'completed' ? 'bg-green-500' :
                        stage.status === 'failed' ? 'bg-red-500' :
                        stage.status === 'running' ? 'bg-blue-500' : 'bg-gray-300'
                      }`}
                      initial={{ width: 0 }}
                      animate={{ width: `${stage.progress}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};
