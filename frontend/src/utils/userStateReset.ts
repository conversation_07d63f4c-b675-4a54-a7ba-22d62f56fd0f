/**
 * 用户状态重置工具
 * 用于解决前后端数据不一致问题
 */

import { useAuthStore } from '../stores/authStore';

/**
 * 强制重置用户状态并清理所有缓存
 * 用于解决数据不一致问题
 */
export const forceResetUserState = (): void => {
  console.warn('🔄 FORCE RESET: 强制重置用户状态');
  
  try {
    // 1. 清除所有本地存储
    console.log('🧹 清除 localStorage...');
    localStorage.removeItem('auth-storage');
    localStorage.removeItem('story-storage');
    localStorage.removeItem('user-preferences');
    
    // 2. 清除会话存储
    console.log('🧹 清除 sessionStorage...');
    sessionStorage.clear();
    
    // 3. 重置 Zustand 认证状态
    console.log('🔄 重置认证状态...');
    const authStore = useAuthStore.getState();
    authStore.logout();
    
    // 4. 清除任何可能的调试用户数据
    if (authStore.resetDebugState) {
      authStore.resetDebugState();
    }
    
    console.log('✅ 用户状态重置完成');
    
    // 5. 显示用户友好的提示
    if (typeof window !== 'undefined') {
      alert('检测到数据不一致问题，已清理缓存数据。页面将重新加载，请重新登录。');
    }
    
  } catch (error) {
    console.error('❌ 重置用户状态时出错:', error);
  }
};

/**
 * 检测并修复用户状态不一致问题
 */
export const detectAndFixUserStateInconsistency = (): boolean => {
  const authStore = useAuthStore.getState();
  const { user, isAuthenticated } = authStore;
  
  // 检查是否存在明显的数据不一致
  const hasInconsistency = (
    // 用户显示为认证但没有用户数据
    (isAuthenticated && !user) ||
    // 用户积分异常高（可能是调试用户数据）
    (user && user.credits > 100000) ||
    // 用户ID格式异常
    (user && (user.id === 'debug-user-001' || user.id === 'test-user')) ||
    // 用户邮箱包含调试标识
    (user && user.email?.includes('debug'))
  );
  
  if (hasInconsistency) {
    console.error('🚨 检测到用户状态不一致:', {
      isAuthenticated,
      userId: user?.id,
      userEmail: user?.email,
      userCredits: user?.credits
    });
    
    forceResetUserState();
    return true;
  }
  
  return false;
};

/**
 * 验证用户认证状态的有效性
 */
export const validateUserAuthState = async (): Promise<boolean> => {
  const authStore = useAuthStore.getState();
  const { user, tokens } = authStore;
  
  // 检查基本认证状态
  if (!user || !tokens?.accessToken) {
    console.log('ℹ️ 用户未认证或缺少令牌');
    return false;
  }
  
  try {
    // 检查JWT令牌格式
    const tokenParts = tokens.accessToken.split('.');
    if (tokenParts.length !== 3) {
      console.error('❌ JWT令牌格式无效');
      forceResetUserState();
      return false;
    }
    
    // 解析JWT payload
    const payload = JSON.parse(atob(tokenParts[1]));
    console.log('🔍 JWT payload:', {
      userId: payload.userId,
      email: payload.email,
      exp: payload.exp,
      type: payload.type
    });
    
    // 检查令牌是否过期
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      console.error('❌ JWT令牌已过期');
      forceResetUserState();
      return false;
    }
    
    // 检查用户ID是否匹配
    if (payload.userId !== user.id) {
      console.error('❌ JWT用户ID与本地用户ID不匹配:', {
        jwtUserId: payload.userId,
        localUserId: user.id
      });
      forceResetUserState();
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ 验证用户认证状态时出错:', error);
    forceResetUserState();
    return false;
  }
};

/**
 * 强制用户重新登录
 */
export const forceRelogin = (): void => {
  console.log('🔄 强制用户重新登录');
  
  // 清理状态
  forceResetUserState();
  
  // 延迟跳转，确保清理完成
  setTimeout(() => {
    if (typeof window !== 'undefined') {
      window.location.href = '/auth?reason=data_inconsistency';
    }
  }, 1000);
};
